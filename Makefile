REG ?= ghcr.io/YOUR_GITHUB_USERNAME
TAG ?= testing
STAGING_HOST ?= ************
WWW_ROOT ?= /opt/www

API_IMG := $(REG)/rmv-api:$(TAG)
WORKER_IMG := $(REG)/rmv-worker:$(TAG)

M1_DIR := apps/frontend_m1_player_v2
M2_DIR := apps/frontend_m2_uploader

# Frontend build-time envs
export API_BASE_URL ?= http://$(STAGING_HOST)/api
export MEDIA_BASE_URL ?= http://$(STAGING_HOST)

.PHONY: help images push api.image worker.image fe.m1v2 fe.m2 fe.deploy all login

help:
	@echo "Usage: make [target]"
	@echo "Targets:"
	@echo "  login            - podman login to GHCR using GHCR_TOKEN and USER"
	@echo "  images           - build API and Worker images (linux/amd64)"
	@echo "  push             - push API and Worker images to GHCR"
	@echo "  api.image        - build API image only"
	@echo "  worker.image     - build Worker image only"
	@echo "  fe.m1v2          - build frontend m1v2"
	@echo "  fe.m2            - build frontend m2"
	@echo "  fe.deploy        - rsync dist to $(STAGING_HOST):$(WWW_ROOT)/{m1v2,m2}"
	@echo "  all              - build+push images and deploy frontends"

login:
	echo $$GHCR_TOKEN | podman login ghcr.io --password-stdin -u $$USER

api.image:
	podman build --platform linux/amd64 -f Dockerfile.api -t $(API_IMG) .

worker.image:
	podman build --platform linux/amd64 -f Dockerfile.worker -t $(WORKER_IMG) .

images: api.image worker.image

push:
	podman push $(API_IMG)
	podman push $(WORKER_IMG)

fe.m1v2:
	cd $(M1_DIR) && npm ci && npm run build

fe.m2:
	cd $(M2_DIR) && npm ci && npm run build

fe.deploy: fe.m1v2 fe.m2
	rsync -avz $(M1_DIR)/dist/ $(STAGING_HOST):$(WWW_ROOT)/m1v2/
	rsync -avz $(M2_DIR)/dist/ $(STAGING_HOST):$(WWW_ROOT)/m2/

all: images push fe.deploy 