# syntax=docker/dockerfile:1

FROM --platform=$BUILDPLATFORM golang:1.24 AS builder
WORKDIR /src

# Copy only the backend source to speed up builds
COPY apps/realmaster-video-backend/ /src/

# Build static binary for linux/amd64
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
    go build -buildvcs=false -o /out/worker ./cmd/video-worker


FROM alpine:latest
RUN addgroup -S appgroup && adduser -S -u 1000 -G appgroup appuser
WORKDIR /app

RUN mkdir -p /app/config /app/static /app/logs && chown -R appuser:appgroup /app
# Copy config into image (baked-in). NOTE: Rebuild image when config changes.
COPY apps/realmaster-video-backend/config.toml /app/config.toml

COPY --from=builder /out/worker /app/worker
RUN chmod +x /app/worker && chown appuser:appgroup /app/worker

USER appuser
CMD ["/app/worker"] 