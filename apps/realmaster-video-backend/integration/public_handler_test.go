package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/domain/advertiser"
	"realmaster-video-backend/internal/domain/public"
	"realmaster-video-backend/internal/domain/state"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/testutil"
)

func mockJWT(userID string) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("authValid", true)
		c.Set("userID", userID)
		c.Next()
	}
}

func TestPublicHandler_Feed_OK(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	advRepo := advertiser.NewRepository()
	if setter, ok := any(pubSvc).(interface{ SetAdvertiserRepo(advertiser.Repository) }); ok {
		setter.SetAdvertiserRepo(advRepo)
	}
	pubH := public.NewHandler(pubSvc)

	// seed advertiser and video
	adv := advertiser.Advertiser{ID: primitive.NewObjectID(), Name: "A", AvatarURL: "100/x/a.jpg"}
	require.NoError(t, advRepo.Create(c(), &adv))
	vid := &video.Video{ID: primitive.NewObjectID(), Title: video.MultilingualString{Zh: "v"}, Status: video.StatusPublished, ClientID: adv.ID}
	require.NoError(t, videoRepo.Create(c(), vid))

	r := gin.New()
	r.Use(mockJWT("u1"))
	r.GET("/video/public/feed", pubH.GetFeed)

	req := httptest.NewRequest(http.MethodGet, "/video/public/feed?page=1&limit=10", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)

	var resp struct {
		Ok   int                 `json:"ok"`
		Data public.FeedResponse `json:"data"`
	}
	require.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	require.Equal(t, 1, resp.Ok)
	require.GreaterOrEqual(t, len(resp.Data.Videos), 1)
}

func TestPublicHandler_States_Batch_JSON_OK(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	pubH := public.NewHandler(pubSvc)

	v1 := primitive.NewObjectID()
	require.NoError(t, videoRepo.Create(c(), &video.Video{ID: v1, Title: video.MultilingualString{Zh: "v1"}, Status: video.StatusPublished}))

	r := gin.New()
	r.Use(mockJWT("u1"))
	r.POST("/video/public/states/batch", pubH.GetUserStates)

	body := `{"userId":"u1","videoIds":["` + v1.Hex() + `"]}`
	req := httptest.NewRequest(http.MethodPost, "/video/public/states/batch", strings.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
}

func TestPublicHandler_States_Query_OK(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	pubH := public.NewHandler(pubSvc)

	v1 := primitive.NewObjectID()
	require.NoError(t, videoRepo.Create(c(), &video.Video{ID: v1, Title: video.MultilingualString{Zh: "v1"}, Status: video.StatusPublished}))

	r := gin.New()
	r.Use(mockJWT("u1"))
	r.GET("/video/public/states", pubH.GetUserStatesQuery)

	req := httptest.NewRequest(http.MethodGet, "/video/public/states?videoIds="+v1.Hex(), nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
}

func TestPublicHandler_Favorites_OK(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	pubH := public.NewHandler(pubSvc)

	v1 := primitive.NewObjectID()
	require.NoError(t, videoRepo.Create(c(), &video.Video{ID: v1, Title: video.MultilingualString{Zh: "v1"}, Status: video.StatusPublished}))

	// favorite once
	user := primitive.NewObjectID()
	require.NoError(t, stateRepo.Upsert(c(), &state.State{ID: state.StateID{UserID: user, VideoID: v1}, Favorited: true}))

	r := gin.New()
	r.Use(func(c *gin.Context) { c.Set("authValid", true); c.Set("userID", user.Hex()); c.Next() })
	r.GET("/video/public/favorites", pubH.GetUserFavorites)

	req := httptest.NewRequest(http.MethodGet, "/video/public/favorites?page=1&limit=10", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
}

// helper context
func c() context.Context { return context.Background() }

func TestPublicHandler_Feed_ClampLimit(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	pubH := public.NewHandler(pubSvc)

	// seed 120 published videos
	for i := 0; i < 120; i++ {
		_ = videoRepo.Create(c(), &video.Video{ID: primitive.NewObjectID(), Title: video.MultilingualString{Zh: "v"}, Status: video.StatusPublished})
	}

	r := gin.New()
	r.Use(mockJWT("u"))
	r.GET("/video/public/feed", pubH.GetFeed)

	req := httptest.NewRequest(http.MethodGet, "/video/public/feed?page=1&limit=1000", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
	var resp struct {
		Ok   int                 `json:"ok"`
		Data public.FeedResponse `json:"data"`
	}
	require.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	require.Equal(t, 1, resp.Ok)
	require.Equal(t, 100, int(resp.Data.Pagination.Limit))
	require.Equal(t, 100, len(resp.Data.Videos))
}

func TestPublicHandler_Feed_PageBeyondTotalEmpty(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	pubH := public.NewHandler(pubSvc)

	// seed 5 published
	for i := 0; i < 5; i++ {
		_ = videoRepo.Create(c(), &video.Video{ID: primitive.NewObjectID(), Title: video.MultilingualString{Zh: "v"}, Status: video.StatusPublished})
	}

	r := gin.New()
	r.Use(mockJWT("u"))
	r.GET("/video/public/feed", pubH.GetFeed)

	req := httptest.NewRequest(http.MethodGet, "/video/public/feed?page=3&limit=3", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
	var resp struct {
		Ok   int                 `json:"ok"`
		Data public.FeedResponse `json:"data"`
	}
	require.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	require.Equal(t, 0, len(resp.Data.Videos))
	require.Equal(t, int64(2), resp.Data.Pagination.TotalPages)
	require.Equal(t, int64(3), resp.Data.Pagination.CurrentPage)
	require.Equal(t, int64(3), resp.Data.Pagination.Limit)
}

func TestPublicHandler_Feed_Unauthorized(t *testing.T) {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	// no JWT middleware
	ts := testutil.SetupTestSuite(t)
	pubH := public.NewHandler(public.NewService(nil, nil, ts.Config))
	r.GET("/video/public/feed", pubH.GetFeed)

	req := httptest.NewRequest(http.MethodGet, "/video/public/feed", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestPublicHandler_States_Batch_TooManyIDs(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	pubH := public.NewHandler(pubSvc)

	r := gin.New()
	r.Use(mockJWT("u"))
	r.POST("/video/public/states/batch", pubH.GetUserStates)

	// build 101 ids
	ids := make([]string, 101)
	for i := 0; i < 101; i++ {
		ids[i] = primitive.NewObjectID().Hex()
	}
	bodyMap := map[string]interface{}{"userId": "u", "videoIds": ids}
	b, _ := json.Marshal(bodyMap)

	req := httptest.NewRequest(http.MethodPost, "/video/public/states/batch", bytes.NewReader(b))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusBadRequest, w.Code)
}

func TestPublicHandler_States_Batch_UserIdMismatch(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	r := gin.New()
	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	pubH := public.NewHandler(pubSvc)

	r.Use(mockJWT("u"))
	r.POST("/video/public/states/batch", pubH.GetUserStates)

	body := `{"userId":"u-other","videoIds":["` + primitive.NewObjectID().Hex() + `"]}`
	req := httptest.NewRequest(http.MethodPost, "/video/public/states/batch", strings.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusForbidden, w.Code)
}

func TestPublicHandler_States_Batch_BadBody(t *testing.T) {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	ts := testutil.SetupTestSuite(t)
	pubH := public.NewHandler(public.NewService(nil, nil, ts.Config))
	r.Use(mockJWT("u"))
	r.POST("/video/public/states/batch", pubH.GetUserStates)

	req := httptest.NewRequest(http.MethodPost, "/video/public/states/batch", strings.NewReader("{"))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusBadRequest, w.Code)
}

func TestPublicHandler_States_Query_TooManyIDs(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	r := gin.New()
	pubH := public.NewHandler(public.NewService(nil, nil, ts.Config))
	r.Use(mockJWT("u"))
	r.GET("/video/public/states", pubH.GetUserStatesQuery)

	var b strings.Builder
	for i := 0; i < 101; i++ {
		if i > 0 {
			b.WriteString(",")
		}
		b.WriteString(primitive.NewObjectID().Hex())
	}
	req := httptest.NewRequest(http.MethodGet, "/video/public/states?videoIds="+b.String(), nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusBadRequest, w.Code)
}

func TestPublicHandler_Favorites_ClampLimit(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	pubH := public.NewHandler(pubSvc)

	user := primitive.NewObjectID()
	// seed 150 favorites
	for i := 0; i < 150; i++ {
		vid := primitive.NewObjectID()
		_ = videoRepo.Create(c(), &video.Video{ID: vid, Title: video.MultilingualString{Zh: "v"}, Status: video.StatusPublished})
		_ = stateRepo.Upsert(c(), &state.State{ID: state.StateID{UserID: user, VideoID: vid}, Favorited: true})
	}

	r := gin.New()
	r.Use(func(c *gin.Context) { c.Set("authValid", true); c.Set("userID", user.Hex()); c.Next() })
	r.GET("/video/public/favorites", pubH.GetUserFavorites)

	req := httptest.NewRequest(http.MethodGet, "/video/public/favorites?page=1&limit=1000", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
	var resp struct {
		Ok   int                          `json:"ok"`
		Data public.UserFavoritesResponse `json:"data"`
	}
	require.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	require.Equal(t, 1, resp.Ok)
	require.Equal(t, 100, int(resp.Data.Pagination.Limit))
	require.Equal(t, 100, len(resp.Data.Favorites))
}

func TestPublicHandler_Favorites_Unauthorized(t *testing.T) {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	ts := testutil.SetupTestSuite(t)
	pubH := public.NewHandler(public.NewService(nil, nil, ts.Config))
	r.GET("/video/public/favorites", pubH.GetUserFavorites)

	req := httptest.NewRequest(http.MethodGet, "/video/public/favorites?page=1&limit=10", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestPublicHandler_Feed_NegativePageAndZeroLimit_Defaults(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	pubH := public.NewHandler(pubSvc)

	// seed 1 published
	_ = videoRepo.Create(c(), &video.Video{ID: primitive.NewObjectID(), Title: video.MultilingualString{Zh: "v"}, Status: video.StatusPublished})

	r := gin.New()
	r.Use(mockJWT("u"))
	r.GET("/video/public/feed", pubH.GetFeed)

	req := httptest.NewRequest(http.MethodGet, "/video/public/feed?page=-2&limit=0", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
	var resp struct {
		Ok   int                 `json:"ok"`
		Data public.FeedResponse `json:"data"`
	}
	require.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	require.Equal(t, 1, resp.Ok)
	require.Equal(t, int64(1), resp.Data.Pagination.CurrentPage)
	require.Equal(t, int64(20), resp.Data.Pagination.Limit)
}

func TestPublicHandler_States_Query_EmptyVideoIds(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	r := gin.New()
	pubH := public.NewHandler(public.NewService(nil, nil, ts.Config))
	r.Use(mockJWT("u"))
	r.GET("/video/public/states", pubH.GetUserStatesQuery)

	req := httptest.NewRequest(http.MethodGet, "/video/public/states?videoIds=", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusBadRequest, w.Code)
}

func TestPublicHandler_States_Batch_EmptyVideoIds(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	r := gin.New()
	pubH := public.NewHandler(public.NewService(nil, nil, ts.Config))
	r.Use(mockJWT("u"))
	r.POST("/video/public/states/batch", pubH.GetUserStates)

	b := []byte(`{"userId":"u","videoIds":[]}`)
	req := httptest.NewRequest(http.MethodPost, "/video/public/states/batch", bytes.NewReader(b))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusBadRequest, w.Code)
}

func TestPublicHandler_Favorites_NegativePageAndZeroLimit_Defaults(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	pubH := public.NewHandler(pubSvc)

	user := primitive.NewObjectID()
	vid := primitive.NewObjectID()
	_ = videoRepo.Create(c(), &video.Video{ID: vid, Title: video.MultilingualString{Zh: "v"}, Status: video.StatusPublished})
	_ = stateRepo.Upsert(c(), &state.State{ID: state.StateID{UserID: user, VideoID: vid}, Favorited: true})

	r := gin.New()
	r.Use(func(c *gin.Context) { c.Set("authValid", true); c.Set("userID", user.Hex()); c.Next() })
	r.GET("/video/public/favorites", pubH.GetUserFavorites)

	req := httptest.NewRequest(http.MethodGet, "/video/public/favorites?page=-5&limit=0", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
	var resp struct {
		Ok   int                          `json:"ok"`
		Data public.UserFavoritesResponse `json:"data"`
	}
	require.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	require.Equal(t, int64(1), resp.Data.Pagination.CurrentPage)
	require.Equal(t, int64(20), resp.Data.Pagination.Limit)
}

func TestPublicHandler_Feed_InvalidLimit_FallbackDefault(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	pubH := public.NewHandler(pubSvc)

	// seed 30 published
	for i := 0; i < 30; i++ {
		_ = videoRepo.Create(c(), &video.Video{ID: primitive.NewObjectID(), Title: video.MultilingualString{Zh: "v"}, Status: video.StatusPublished})
	}

	r := gin.New()
	r.Use(mockJWT("u"))
	r.GET("/video/public/feed", pubH.GetFeed)

	req := httptest.NewRequest(http.MethodGet, "/video/public/feed?page=1&limit=abc", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
	var resp struct {
		Ok   int                 `json:"ok"`
		Data public.FeedResponse `json:"data"`
	}
	require.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	require.Equal(t, 1, resp.Ok)
	// default for video pagination type is 20
	require.Equal(t, int64(20), resp.Data.Pagination.Limit)
	require.Equal(t, 20, len(resp.Data.Videos))
}

func TestPublicHandler_Feed_EmptyDB_PaginationZero(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubH := public.NewHandler(public.NewService(videoRepo, stateRepo, ts.Config))

	r := gin.New()
	r.Use(mockJWT("u"))
	r.GET("/video/public/feed", pubH.GetFeed)

	req := httptest.NewRequest(http.MethodGet, "/video/public/feed", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
	var resp struct {
		Ok   int                 `json:"ok"`
		Data public.FeedResponse `json:"data"`
	}
	require.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	require.Equal(t, 0, len(resp.Data.Videos))
	require.Equal(t, int64(0), resp.Data.Pagination.TotalItems)
	require.Equal(t, int64(0), resp.Data.Pagination.TotalPages)
}

func TestPublicHandler_States_Batch_Unauthorized(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	r := gin.New()
	pubH := public.NewHandler(public.NewService(nil, nil, ts.Config))
	r.POST("/video/public/states/batch", pubH.GetUserStates)

	b := []byte(`{"userId":"u","videoIds":["` + primitive.NewObjectID().Hex() + `"]}`)
	req := httptest.NewRequest(http.MethodPost, "/video/public/states/batch", bytes.NewReader(b))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestPublicHandler_States_Query_Unauthorized(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	r := gin.New()
	pubH := public.NewHandler(public.NewService(nil, nil, ts.Config))
	r.GET("/video/public/states", pubH.GetUserStatesQuery)

	req := httptest.NewRequest(http.MethodGet, "/video/public/states?videoIds="+primitive.NewObjectID().Hex(), nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestPublicHandler_States_Batch_WrongTypeVideoIds(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	r := gin.New()
	pubH := public.NewHandler(public.NewService(nil, nil, ts.Config))
	r.Use(mockJWT("u"))
	r.POST("/video/public/states/batch", pubH.GetUserStates)

	b := []byte(`{"userId":"u","videoIds":"not-array"}`)
	req := httptest.NewRequest(http.MethodPost, "/video/public/states/batch", bytes.NewReader(b))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusBadRequest, w.Code)
}

func TestPublicHandler_States_Query_MissingParam(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	r := gin.New()
	pubH := public.NewHandler(public.NewService(nil, nil, ts.Config))
	r.Use(mockJWT("u"))
	r.GET("/video/public/states", pubH.GetUserStatesQuery)

	req := httptest.NewRequest(http.MethodGet, "/video/public/states", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusBadRequest, w.Code)
}

func TestPublicHandler_Favorites_PageBeyondTotal_Empty(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pubSvc := public.NewService(videoRepo, stateRepo, ts.Config)
	pubH := public.NewHandler(pubSvc)

	user := primitive.NewObjectID()
	// seed 2 favorites
	for i := 0; i < 2; i++ {
		vid := primitive.NewObjectID()
		_ = videoRepo.Create(c(), &video.Video{ID: vid, Title: video.MultilingualString{Zh: "v"}, Status: video.StatusPublished})
		_ = stateRepo.Upsert(c(), &state.State{ID: state.StateID{UserID: user, VideoID: vid}, Favorited: true})
	}

	r := gin.New()
	r.Use(func(c *gin.Context) { c.Set("authValid", true); c.Set("userID", user.Hex()); c.Next() })
	r.GET("/video/public/favorites", pubH.GetUserFavorites)

	req := httptest.NewRequest(http.MethodGet, "/video/public/favorites?page=5&limit=1", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
	var resp struct {
		Ok   int                          `json:"ok"`
		Data public.UserFavoritesResponse `json:"data"`
	}
	require.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	require.Equal(t, 0, len(resp.Data.Favorites))
	require.Equal(t, int64(2), resp.Data.Pagination.TotalPages)
	require.Equal(t, int64(5), resp.Data.Pagination.CurrentPage)
}
