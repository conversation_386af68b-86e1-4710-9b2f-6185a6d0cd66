package database

import (
	"fmt"
	"sync"

	"github.com/real-rm/gomongo"

	"realmaster-video-backend/internal/platform/logger"
)

var (
	initOnce sync.Once
	initErr  error
)

// InitMongoDB 初始化gomongo数据库连接（并发安全且幂等）
// gomongo会自动从config.toml读取数据库配置
func InitMongoDB() error {
	initOnce.Do(func() {
		initErr = gomongo.InitMongoDB()
		if initErr == nil {
			logger.Log.Info("gomongo初始化成功")
		}
	})
	if initErr != nil {
		return fmt.Errorf("初始化gomongo失败: %w", initErr)
	}
	return nil
}

// GetCollection 获取指定数据库和集合的实例
func GetCollection(dbName, collectionName string) *gomongo.MongoCollection {
	return gomongo.Coll(dbName, collectionName)
}
