package testutil

import (
	"fmt"
	"math/rand"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// VideoFixture 创建测试用的视频数据
// 注意：这里不导入video包，避免循环依赖
// 使用interface{}和map[string]interface{}来构建测试数据
func VideoFixture() map[string]interface{} {
	return map[string]interface{}{
		"_id": primitive.NewObjectID(),
		"tl": map[string]interface{}{
			"zh": "测试视频标题",
			"en": "Test Video Title",
		},
		"dsc": map[string]interface{}{
			"zh": "这是一个测试视频的描述",
			"en": "This is a test video description",
		},
		"dur":    float64(rand.Intn(3540) + 60), // 1分钟到1小时
		"uldId":  fmt.Sprintf("user-%d", rand.Intn(1000)),
		"catId":  primitive.NewObjectID(),
		"tags":   []string{"测试", "视频", "demo"},
		"st":     "draft", // StatusDraft
		"prop":   []string{fmt.Sprintf("prop-%d", rand.Intn(1000))},
		"ExtUrl": "https://example.com/property/123",
		"cltId":  primitive.NewObjectID(),
		"stat": map[string]interface{}{
			"views":          int64(rand.Intn(10000)),
			"likes":          int64(rand.Intn(1000)),
			"collections":    int64(rand.Intn(500)),
			"completions":    int64(rand.Intn(800)),
			"completionRate": "75.5%",
		},
		"thumbUrl": "/media/test/thumbnail.jpg",
		"hlsUrl":   "/media/test/playlist.m3u8",
	}
}

// VideoFixtureWithStatus 创建指定状态的视频数据
func VideoFixtureWithStatus(status string) map[string]interface{} {
	v := VideoFixture()
	v["st"] = status
	if status == "published" {
		now := time.Now()
		v["pts"] = &now
	}
	return v
}

// CategoryFixture 创建测试用的分类数据
func CategoryFixture() map[string]interface{} {
	return map[string]interface{}{
		"_id": primitive.NewObjectID(),
		"nm":  "测试分类",
		"ord": rand.Intn(100) + 1,
	}
}

// AdvertiserFixture 创建测试用的广告主数据
func AdvertiserFixture() map[string]interface{} {
	return map[string]interface{}{
		"_id":  primitive.NewObjectID(),
		"nm":   "测试广告主公司",
		"avt":  "/media/avatars/test.jpg",
		"ph":   "13800138000",
		"em":   "<EMAIL>",
		"mUid": fmt.Sprintf("mapp-user-%d", rand.Intn(1000)),
		"rem":  "这是一个测试广告主",
	}
}

// CreateVideoRequestFixture 创建测试用的视频创建请求
func CreateVideoRequestFixture() map[string]interface{} {
	return map[string]interface{}{
		"title": map[string]interface{}{
			"zh": "测试视频创建请求",
			"en": "Test Video Create Request",
		},
		"description": map[string]interface{}{
			"zh": "这是一个测试视频创建请求的描述",
			"en": "This is a test video create request description",
		},
		"uploaderId":        fmt.Sprintf("uploader-%d", rand.Intn(1000)),
		"categoryId":        primitive.NewObjectID().Hex(),
		"tags":              []string{"测试", "创建"},
		"propertyIds":       []string{fmt.Sprintf("prop-%d", rand.Intn(1000))},
		"externalUrl":       "https://example.com/property/456",
		"clientId":          primitive.NewObjectID().Hex(),
		"tempVideoFilePath": "/tmp/test_video.mp4",
		"tempThumbFilePath": "/tmp/test_thumb.jpg",
	}
}

// VideoFilterFixture 创建测试用的视频过滤器
func VideoFilterFixture() map[string]interface{} {
	categoryID := primitive.NewObjectID()
	clientID := primitive.NewObjectID()
	fromDate := time.Now().AddDate(0, -1, 0) // 一个月前
	toDate := time.Now()

	return map[string]interface{}{
		"categoryId": categoryID.Hex(),
		"clientId":   clientID.Hex(),
		"status":     []string{"published", "draft"},
		"fromDate":   fromDate.Format(time.RFC3339),
		"toDate":     toDate.Format(time.RFC3339),
		"page":       1,
		"limit":      10,
	}
}

// RandomObjectID 生成随机的ObjectID字符串
func RandomObjectID() string {
	return primitive.NewObjectID().Hex()
}

// RandomString 生成指定长度的随机字符串
func RandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// RandomInt 生成指定范围内的随机整数
func RandomInt(min, max int) int {
	return rand.Intn(max-min+1) + min
}

// RandomBool 生成随机布尔值
func RandomBool() bool {
	return rand.Intn(2) == 1
}
