package testutil

import (
	"context"
	"fmt"
	"hash/fnv"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/platform/logger"
)

// TestSuite 测试套件，管理测试环境的生命周期
type TestSuite struct {
	Config *config.Config
	T      *testing.T
}

// SetupTestSuite 初始化测试环境
// 这个函数会：
// 1. 生成带唯一DB名称的临时测试配置（包含包名+测试名信息的hash）
// 2. 加载测试配置
// 3. 初始化日志系统
// 4. 初始化数据库连接
func SetupTestSuite(t *testing.T) *TestSuite {
	// 获取当前工作目录（调用方包目录）
	origWd, err := os.Getwd()
	if err != nil {
		t.Fatalf("获取工作目录失败: %v", err)
	}

	// 查找项目根目录（包含test_config.toml的目录）
	root := origWd
	configPath := ""
	rootDir := ""
	for {
		testConfigPath := filepath.Join(root, "test_config.toml")
		if _, err := os.Stat(testConfigPath); err == nil {
			configPath = testConfigPath
			rootDir = root
			break
		}

		parent := filepath.Clean(filepath.Join(root, ".."))
		if parent == root {
			t.Skip("跳过测试：找不到test_config.toml文件")
		}
		root = parent
	}

	// 读取原始配置并生成带唯一DB名的临时配置
	origBytes, err := os.ReadFile(configPath)
	if err != nil {
		t.Fatalf("读取测试配置失败: %v", err)
	}
	// 计算包相对路径（相对项目根）并与测试名共同参与hash
	relPkg, _ := filepath.Rel(rootDir, origWd)
	if relPkg == "." {
		relPkg = "root"
	}
	suffix := buildDBSuffix(relPkg + ":" + t.Name())
	content := string(origBytes)
	// 将物理数据库名从 test_realmaster_video 替换为带后缀版本
	content = strings.ReplaceAll(content, "/test_realmaster_video?", "/test_realmaster_video_"+suffix+"?")
	content = strings.ReplaceAll(content, "/test_realmaster_video\n", "/test_realmaster_video_"+suffix+"\n")
	content = strings.ReplaceAll(content, "/test_realmaster_video\r\n", "/test_realmaster_video_"+suffix+"\r\n")

	// 写入临时配置文件
	tmpDir := os.TempDir()
	tmpFile := filepath.Join(tmpDir, fmt.Sprintf("test_config_%s.toml", suffix))
	if err := os.WriteFile(tmpFile, []byte(content), 0644); err != nil {
		t.Fatalf("写入临时测试配置失败: %v", err)
	}
	t.Cleanup(func() { _ = os.Remove(tmpFile) })

	// 设置测试配置文件环境变量
	originalConfigFile := os.Getenv("CONFIG_FILE")
	originalRMBaseFile := os.Getenv("RMBASE_FILE_CFG")

	os.Setenv("CONFIG_FILE", tmpFile)
	os.Setenv("RMBASE_FILE_CFG", tmpFile)

	// 确保测试结束后恢复原始配置
	t.Cleanup(func() {
		if originalConfigFile != "" {
			os.Setenv("CONFIG_FILE", originalConfigFile)
		} else {
			os.Unsetenv("CONFIG_FILE")
		}

		if originalRMBaseFile != "" {
			os.Setenv("RMBASE_FILE_CFG", originalRMBaseFile)
		} else {
			os.Unsetenv("RMBASE_FILE_CFG")
		}
	})

	// 1. 加载测试配置
	cfg, err := config.LoadConfig()
	if err != nil {
		t.Fatalf("加载测试配置失败: %v", err)
	}

	// 2. 初始化日志系统（使用测试配置）
	if _, err := logger.NewLogger(); err != nil {
		t.Fatalf("初始化测试日志失败: %v", err)
	}

	// 3. 初始化数据库连接（使用临时配置中的唯一DB名称）
	err = database.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：无法连接到测试数据库: %v\n请确保MongoDB服务器正在运行: mongodb://localhost:27017", err)
	}

	// 验证数据库连接是否真的可用
	collection := database.GetCollection("realmaster_video", "video_videos")
	if collection == nil {
		t.Skip("跳过测试：数据库集合初始化失败，请检查MongoDB连接")
	}

	return &TestSuite{
		Config: cfg,
		T:      t,
	}
}

func buildDBSuffix(seed string) string {
	// 使用短固定长度：8位hash(seed) + 8位时间戳低位，确保总库名长度 < 63
	h := fnv.New32a()
	_, _ = h.Write([]byte(seed))
	n := h.Sum32()
	ts := uint32(time.Now().UnixNano())
	return fmt.Sprintf("%08x%08x", n, ts)
}

// CleanupDatabase 清理测试数据库
// 在每个测试后调用，确保测试隔离
func (ts *TestSuite) CleanupDatabase() {
	ctx := context.Background()

	// 清理各个集合的测试数据（基于别名 realmaster_video，会映射到当前套件唯一DB）
	collections := []struct {
		dbName         string
		collectionName string
	}{
		{"realmaster_video", "video_videos"},
		{"realmaster_video", "video_categories"},
		{"realmaster_video", "video_advertisers"},
		{"realmaster_video", "video_interactions"},
		{"realmaster_video", "video_states"},
	}

	for _, coll := range collections {
		collection := database.GetCollection(coll.dbName, coll.collectionName)
		if collection != nil {
			// 删除所有测试数据（可以根据需要添加过滤条件）
			_, err := collection.DeleteMany(ctx, map[string]interface{}{})
			if err != nil {
				ts.T.Logf("清理集合 %s.%s 失败: %v", coll.dbName, coll.collectionName, err)
			}
		}
	}
}

// CreateTempDirs 创建测试需要的临时目录
func (ts *TestSuite) CreateTempDirs() {
	dirs := []string{
		ts.Config.Server.DraftDir,
		// 注意：媒体文件现在通过nginx提供，测试环境不需要创建媒体目录
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			ts.T.Fatalf("创建测试目录 %s 失败: %v", dir, err)
		}
	}

	// 确保测试结束后清理临时目录
	ts.T.Cleanup(func() {
		for _, dir := range dirs {
			os.RemoveAll(dir)
		}
	})
}
