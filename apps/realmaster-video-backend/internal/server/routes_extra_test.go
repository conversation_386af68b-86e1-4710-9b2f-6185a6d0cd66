package server

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/require"
)

func TestRegisterDevRoutes_JWT(t *testing.T) {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	RegisterDevRoutes(r)

	req := httptest.NewRequest("GET", "/dev/jwt?user=admin", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	require.Equal(t, http.StatusOK, w.Code)
	require.Contains(t, w.Body.String(), "jwt")
}
