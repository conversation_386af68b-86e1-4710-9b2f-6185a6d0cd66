package category

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/testutil"
)

func TestCategoryService_List_Create_EnsureNone(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ts.CleanupDatabase()
	ctx := context.Background()
	repo := NewRepository()
	vrepo := video.NewRepository()
	svc := NewService(repo, vrepo)

	// Ensure None
	require.NoError(t, svc.EnsureNoneCategoryExists(ctx))
	// Create categories
	for i := 0; i < 3; i++ {
		ord := i + 1
		name := "C" + string(rune('A'+i))
		_, err := svc.Create(ctx, CreateCategoryRequest{Name: name, Order: &ord})
		require.NoError(t, err)
	}

	// List page 1 limit 2
	resp, err := svc.List(ctx, ListCategoriesRequest{Page: 1, Limit: 2})
	require.NoError(t, err)
	require.NotNil(t, resp.Pagination)
	// compute total by counting repo
	total, err := repo.Count(ctx, primitive.M{})
	require.NoError(t, err)
	assert.Equal(t, total, resp.Pagination.TotalItems)
	assert.Len(t, resp.Items, 2)
}

func TestCategoryService_Create_Update_Delete_Validations(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ts.CleanupDatabase()
	ctx := context.Background()
	repo := NewRepository()
	vrepo := video.NewRepository()
	svc := NewService(repo, vrepo)

	// invalid create
	_, err := svc.Create(ctx, CreateCategoryRequest{Name: ""})
	assert.Equal(t, ErrCategoryNameRequired, err)
	long := make([]byte, 51)
	for i := range long {
		long[i] = 'x'
	}
	_, err = svc.Create(ctx, CreateCategoryRequest{Name: string(long)})
	assert.Equal(t, ErrCategoryNameTooLong, err)

	// create valid
	cat, err := svc.Create(ctx, CreateCategoryRequest{Name: "X"})
	require.NoError(t, err)

	// update validations
	err = svc.Update(ctx, "badid", UpdateCategoryRequest{Name: nil})
	assert.Equal(t, ErrInvalidCategoryID, err)
	err = svc.Update(ctx, cat.ID.Hex(), UpdateCategoryRequest{})
	assert.Equal(t, ErrUpdatePayloadRequired, err)
	n := ""
	err = svc.Update(ctx, cat.ID.Hex(), UpdateCategoryRequest{Name: &n})
	assert.Equal(t, ErrCategoryNameRequired, err)
	too := string(long)
	err = svc.Update(ctx, cat.ID.Hex(), UpdateCategoryRequest{Name: &too})
	assert.Equal(t, ErrCategoryNameTooLong, err)

	// conflict name
	_, _ = svc.Create(ctx, CreateCategoryRequest{Name: "Y"})
	err = svc.Update(ctx, cat.ID.Hex(), UpdateCategoryRequest{Name: strPtr("Y")})
	assert.Equal(t, ErrCategoryNameExists, err)

	// not found
	err = svc.Update(ctx, primitive.NewObjectID().Hex(), UpdateCategoryRequest{Name: strPtr("Z")})
	assert.Equal(t, ErrCategoryNotFound, err)

	// delete none protection
	require.NoError(t, svc.EnsureNoneCategoryExists(ctx))
	none, _ := repo.FindByName(ctx, NoneCategoryName)
	err = svc.Delete(ctx, none.ID.Hex())
	assert.Equal(t, ErrCannotDeleteNoneCategory, err)

	// delete normal
	cat2, _ := svc.Create(ctx, CreateCategoryRequest{Name: "DelMe"})
	require.NoError(t, svc.Delete(ctx, cat2.ID.Hex()))
}

func strPtr(s string) *string { return &s }
