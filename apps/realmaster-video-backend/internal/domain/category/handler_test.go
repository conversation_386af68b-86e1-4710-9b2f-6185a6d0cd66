package category

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/testutil"
)

func TestCategoryHandler_List_Create_Update_Delete(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ts.CleanupDatabase()

	repo := NewRepository()
	vrepo := video.NewRepository()
	svc := NewService(repo, vrepo)
	// 确保 'None' 分类存在，供删除逻辑迁移引用
	require.NoError(t, svc.EnsureNoneCategoryExists(context.Background()))
	h := NewHandler(svc)

	r := gin.New()
	r.GET("/categories", h.ListCategories)
	r.POST("/categories", h.CreateCategory)
	r.PUT("/categories/:id", h.UpdateCategory)
	r.DELETE("/categories/:id", h.DeleteCategory)

	// Create ok
	body := map[string]interface{}{"name": "Cat1", "order": 1}
	b, _ := json.Marshal(body)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest(http.MethodPost, "/categories", bytes.NewBuffer(b))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	t.Logf("create resp: %s", w.Body.String())
	require.Equal(t, http.StatusCreated, w.Code)

	// List pagination ok
	w2 := httptest.NewRecorder()
	r.ServeHTTP(w2, httptest.NewRequest(http.MethodGet, "/categories?page=1&limit=1", nil))
	require.Equal(t, http.StatusOK, w2.Code)

	// Create bad request
	w3 := httptest.NewRecorder()
	reqBR, _ := http.NewRequest(http.MethodPost, "/categories", bytes.NewBufferString("{"))
	reqBR.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w3, reqBR)
	require.Equal(t, http.StatusBadRequest, w3.Code)

	// Update ok
	var resp struct {
		Data map[string]interface{} `json:"data"`
	}
	_ = json.Unmarshal(w.Body.Bytes(), &resp)
	id := resp.Data["id"].(string)
	u := map[string]interface{}{"name": "Cat1New"}
	ub, _ := json.Marshal(u)
	w4 := httptest.NewRecorder()
	req4, _ := http.NewRequest(http.MethodPut, "/categories/"+id, bytes.NewBuffer(ub))
	req4.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w4, req4)
	require.Equal(t, http.StatusOK, w4.Code)

	// Update invalid id
	w5 := httptest.NewRecorder()
	req5, _ := http.NewRequest(http.MethodPut, "/categories/nothex", bytes.NewBuffer(ub))
	req5.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w5, req5)
	require.Equal(t, http.StatusBadRequest, w5.Code)

	// Delete invalid id
	w6 := httptest.NewRecorder()
	r.ServeHTTP(w6, httptest.NewRequest(http.MethodDelete, "/categories/nothex", nil))
	require.Equal(t, http.StatusBadRequest, w6.Code)

	// Delete ok
	w7 := httptest.NewRecorder()
	r.ServeHTTP(w7, httptest.NewRequest(http.MethodDelete, "/categories/"+id, nil))
	t.Logf("delete resp: %s", w7.Body.String())
	require.Equal(t, http.StatusOK, w7.Code)

	// Delete notfound
	w8 := httptest.NewRecorder()
	r.ServeHTTP(w8, httptest.NewRequest(http.MethodDelete, "/categories/"+primitive.NewObjectID().Hex(), nil))
	require.Equal(t, http.StatusNotFound, w8.Code)
}

func TestCategoryHandler_Update_EmptyPayload_BadRequest(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ts.CleanupDatabase()

	repo := NewRepository()
	vrepo := video.NewRepository()
	svc := NewService(repo, vrepo)
	require.NoError(t, svc.EnsureNoneCategoryExists(context.Background()))
	h := NewHandler(svc)

	r := gin.New()
	r.PUT("/categories/:id", h.UpdateCategory)
	r.POST("/categories", h.CreateCategory)

	// 先创建一个分类
	b, _ := json.Marshal(map[string]interface{}{"name": "AA"})
	w := httptest.NewRecorder()
	req, _ := http.NewRequest(http.MethodPost, "/categories", bytes.NewBuffer(b))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusCreated, w.Code)

	var resp struct {
		Data map[string]interface{} `json:"data"`
	}
	_ = json.Unmarshal(w.Body.Bytes(), &resp)
	id := resp.Data["id"].(string)

	// 尝试用空载荷更新，应返回400
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest(http.MethodPut, "/categories/"+id, bytes.NewBufferString("{}"))
	req2.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w2, req2)
	require.Equal(t, http.StatusBadRequest, w2.Code)
}

func TestCategoryHandler_Update_DuplicateName_Conflict(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ts.CleanupDatabase()

	repo := NewRepository()
	vrepo := video.NewRepository()
	svc := NewService(repo, vrepo)
	require.NoError(t, svc.EnsureNoneCategoryExists(context.Background()))
	h := NewHandler(svc)

	r := gin.New()
	r.POST("/categories", h.CreateCategory)
	r.PUT("/categories/:id", h.UpdateCategory)

	// 创建A、B两个分类
	create := func(name string) string {
		b, _ := json.Marshal(map[string]interface{}{"name": name})
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodPost, "/categories", bytes.NewBuffer(b))
		req.Header.Set("Content-Type", "application/json")
		r.ServeHTTP(w, req)
		require.Equal(t, http.StatusCreated, w.Code)
		var resp struct {
			Data map[string]interface{} `json:"data"`
		}
		_ = json.Unmarshal(w.Body.Bytes(), &resp)
		return resp.Data["id"].(string)
	}

	idA := create("A")
	_ = idA
	idB := create("B")

	// 将B改名为A，触发409
	payload, _ := json.Marshal(map[string]interface{}{"name": "A"})
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest(http.MethodPut, "/categories/"+idB, bytes.NewBuffer(payload))
	req2.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w2, req2)
	require.Equal(t, http.StatusConflict, w2.Code)
}

func TestCategoryHandler_Delete_NoneCategory_Conflict(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ts.CleanupDatabase()

	repo := NewRepository()
	vrepo := video.NewRepository()
	svc := NewService(repo, vrepo)
	require.NoError(t, svc.EnsureNoneCategoryExists(context.Background()))
	h := NewHandler(svc)

	r := gin.New()
	r.DELETE("/categories/:id", h.DeleteCategory)

	// 找到 None 分类并尝试删除，应返回409
	none, err := repo.FindByName(context.Background(), NoneCategoryName)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	r.ServeHTTP(w, httptest.NewRequest(http.MethodDelete, "/categories/"+none.ID.Hex(), nil))
	require.Equal(t, http.StatusConflict, w.Code)
}
