package category

import (
	"context"
	"errors"
	"fmt"

	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/platform/logger"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Repository 定义了分类数据访问接口
type Repository interface {
	FindAll(ctx context.Context, page, limit int64) ([]Category, error)
	FindByID(ctx context.Context, id string) (*Category, error)
	FindByName(ctx context.Context, name string) (*Category, error)
	Create(ctx context.Context, category *Category) error
	Delete(ctx context.Context, id string) error
	ExistsByID(ctx context.Context, id string) (bool, error)
	IsInUse(ctx context.Context, id string) (bool, error)
	Update(ctx context.Context, id string, updateData primitive.M) error
	ExistsByNameAndNotID(ctx context.Context, name, id string) (bool, error)
	Count(ctx context.Context, filter primitive.M) (int64, error)
}

// repository 实现了 Repository 接口
type repository struct {
	collection      *gomongo.MongoCollection
	videoCollection *gomongo.MongoCollection // 添加视频集合依赖
}

// NewRepository 创建一个新的分类仓库实例
func NewRepository() Repository {
	collection := database.GetCollection("realmaster_video", "video_categories")
	videoCollection := database.GetCollection("realmaster_video", "video_videos")
	return &repository{
		collection:      collection,
		videoCollection: videoCollection,
	}
}

// FindAll 查询所有分类
func (r *repository) FindAll(ctx context.Context, page, limit int64) ([]Category, error) {
	// 使用gomongo的QueryOptions方式
	queryOpts := gomongo.QueryOptions{
		Sort: bson.D{bson.E{Key: "ord", Value: 1}},
	}

	// 设置分页
	if limit > 0 {
		queryOpts.Limit = limit
		if page > 0 {
			queryOpts.Skip = (page - 1) * limit
		}
	}

	// 执行查询
	cursor, err := r.collection.Find(ctx, bson.D{}, queryOpts)
	if err != nil {
		logger.Log.Error("查询分类列表失败",
			logger.Error(err),
		)
		return nil, fmt.Errorf("查询分类列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	// 解码结果
	var categories []Category
	if err := cursor.All(ctx, &categories); err != nil {
		logger.Log.Error("解码分类列表失败",
			logger.Error(err),
		)
		return nil, fmt.Errorf("解码分类列表失败: %w", err)
	}

	logger.Log.Info("成功查询分类列表",
		logger.Int("count", len(categories)),
	)

	return categories, nil
}

// FindByID 根据ID查找分类
func (r *repository) FindByID(ctx context.Context, id string) (*Category, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("无效的分类ID: %w", err)
	}

	var category Category
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&category)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, ErrCategoryNotFound // 使用预定义的错误
		}
		return nil, fmt.Errorf("通过ID查找分类失败: %w", err)
	}
	return &category, nil
}

// FindByName 根据名称查找分类
func (r *repository) FindByName(ctx context.Context, name string) (*Category, error) {
	var category Category
	err := r.collection.FindOne(ctx, bson.M{"nm": name}).Decode(&category)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, ErrCategoryNotFound
		}
		return nil, fmt.Errorf("通过名称查找分类失败: %w", err)
	}
	return &category, nil
}

// Create 创建新的分类
// 支持事务：如果ctx是SessionContext，操作将在事务中执行
func (r *repository) Create(ctx context.Context, category *Category) error {
	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.InsertOne(ctx, category)
	if err != nil {
		logger.Log.Error("创建分类失败",
			logger.Error(err),
		)
		return fmt.Errorf("创建分类失败: %w", err)
	}

	// 使用InsertOne返回的ID回填到结构体中
	if insertedID, ok := result.InsertedID.(primitive.ObjectID); ok {
		category.ID = insertedID
	} else {
		logger.Log.Error("无法获取插入的分类ID", logger.Any("insertedID", result.InsertedID))
		return fmt.Errorf("无法获取插入的分类ID")
	}

	// 记录创建的分类信息
	logger.Log.Info("成功创建分类",
		logger.String("id", category.ID.Hex()),
		logger.String("name", category.Name),
		logger.Int("order", category.Order),
	)

	return nil
}

// Delete 删除指定ID的分类
// 支持事务：如果ctx是SessionContext，操作将在事务中执行
func (r *repository) Delete(ctx context.Context, id string) error {
	// 将字符串ID转换为ObjectID
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("无效的分类ID: %w", err)
	}

	// 使用传入的context（可能是SessionContext）
	// 执行删除操作
	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		logger.Log.Error("删除分类失败",
			logger.Error(err),
			logger.String("id", id),
		)
		return fmt.Errorf("删除分类失败: %w", err)
	}

	// 检查是否找到并删除了文档
	if result.DeletedCount == 0 {
		return ErrCategoryNotFound
	}

	logger.Log.Info("成功删除分类",
		logger.String("id", id),
	)

	return nil
}

// Update 更新指定的分类信息
// 支持事务：如果ctx是SessionContext，操作将在事务中执行
func (r *repository) Update(ctx context.Context, id string, updateData primitive.M) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("无效的分类ID: %w", err)
	}

	// 使用传入的context（可能是SessionContext）
	// 使用 $set 操作符来更新字段
	result, err := r.collection.UpdateOne(ctx, bson.M{"_id": objectID}, bson.M{"$set": updateData})
	if err != nil {
		logger.Log.Error("更新分类失败", logger.Error(err), logger.String("id", id))
		return fmt.Errorf("更新分类失败: %w", err)
	}

	// 检查是否找到了匹配的文档
	if result.MatchedCount == 0 {
		return ErrCategoryNotFound
	}

	logger.Log.Info("成功更新分类", logger.String("id", id))
	return nil
}

// ExistsByNameAndNotID 检查除指定ID外，是否存在同名的分类
func (r *repository) ExistsByNameAndNotID(ctx context.Context, name, id string) (bool, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		// 如果ID格式无效，直接认为不冲突
		return false, fmt.Errorf("无效的分类ID: %w", err)
	}

	// 查询条件：name 匹配，但 _id 不匹配
	filter := bson.M{
		"nm":  name,
		"_id": bson.M{"$ne": objectID},
	}
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, fmt.Errorf("查询同名分类失败: %w", err)
	}
	return count > 0, nil
}

// ExistsByID 检查分类ID是否存在
func (r *repository) ExistsByID(ctx context.Context, id string) (bool, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return false, fmt.Errorf("无效的分类ID: %w", err)
	}

	filter := bson.M{"_id": objectID}
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, fmt.Errorf("查询分类失败: %w", err)
	}
	return count > 0, nil
}

// IsInUse 检查分类是否正在被使用
func (r *repository) IsInUse(ctx context.Context, id string) (bool, error) {
	// 将字符串ID转换为ObjectID
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return false, fmt.Errorf("无效的分类ID: %w", err)
	}

	// 查询视频集合中是否有视频引用了这个分类
	filter := bson.M{"catId": objectID}
	count, err := r.videoCollection.CountDocuments(ctx, filter)
	if err != nil {
		logger.Log.Error("检查分类使用状态失败",
			logger.Error(err),
			logger.String("id", id),
		)
		return false, fmt.Errorf("检查分类使用状态失败: %w", err)
	}
	// 如果 count > 0，说明有视频引用了这个分类
	return count > 0, nil
}

// Count 根据条件统计分类数量
func (r *repository) Count(ctx context.Context, filter primitive.M) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		logger.Log.Error("统计分类数量失败", logger.Error(err))
		return 0, fmt.Errorf("统计分类数量失败: %w", err)
	}
	return count, nil
}
