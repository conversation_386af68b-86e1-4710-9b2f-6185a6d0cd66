package category

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/testutil"
)

func TestCategoryRepository_CRUD_Pagination_Exists(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ts.CleanupDatabase()
	ctx := context.Background()
	repo := NewRepository()
	videoRepo := video.NewRepository()

	// create three categories
	c1 := &Category{ID: primitive.NewObjectID(), Name: "A", Order: 1}
	require.NoError(t, repo.Create(ctx, c1))
	c2 := &Category{ID: primitive.NewObjectID(), Name: "B", Order: 2}
	require.NoError(t, repo.Create(ctx, c2))
	c3 := &Category{ID: primitive.NewObjectID(), Name: "C", Order: 3}
	require.NoError(t, repo.Create(ctx, c3))

	// find by id/name
	got, err := repo.FindByID(ctx, c2.ID.Hex())
	require.NoError(t, err)
	require.Equal(t, "B", got.Name)
	byName, err := repo.FindByName(ctx, "C")
	require.NoError(t, err)
	require.Equal(t, c3.ID, byName.ID)

	// pagination order by ord asc
	page1, err := repo.FindAll(ctx, 1, 2)
	require.NoError(t, err)
	require.Len(t, page1, 2)
	require.Equal(t, "A", page1[0].Name)
	require.Equal(t, "B", page1[1].Name)
	page2, err := repo.FindAll(ctx, 2, 2)
	require.NoError(t, err)
	require.Len(t, page2, 1)
	require.Equal(t, "C", page2[0].Name)

	// ExistsByID and ExistsByNameAndNotID
	exists, err := repo.ExistsByID(ctx, c1.ID.Hex())
	require.NoError(t, err)
	require.True(t, exists)
	existsNameOther, err := repo.ExistsByNameAndNotID(ctx, "A", c2.ID.Hex())
	require.NoError(t, err)
	require.True(t, existsNameOther)

	// Count
	cnt, err := repo.Count(ctx, primitive.M{})
	require.NoError(t, err)
	require.Equal(t, int64(3), cnt)

	// Update
	require.NoError(t, repo.Update(ctx, c1.ID.Hex(), primitive.M{"nm": "A2", "ord": 10}))
	upd, _ := repo.FindByID(ctx, c1.ID.Hex())
	require.Equal(t, "A2", upd.Name)
	require.Equal(t, 10, upd.Order)

	// IsInUse: seed a video with catId = c2
	v := &video.Video{ID: primitive.NewObjectID(), Title: video.MultilingualString{Zh: "x"}, Status: video.StatusDraft, UploaderID: "u", CategoryID: c2.ID}
	require.NoError(t, videoRepo.Create(ctx, v))
	inUse, err := repo.IsInUse(ctx, c2.ID.Hex())
	require.NoError(t, err)
	require.True(t, inUse)

	// Delete
	require.NoError(t, repo.Delete(ctx, c3.ID.Hex()))
	_, err = repo.FindByID(ctx, c3.ID.Hex())
	require.Error(t, err)
}

func TestCategoryRepository_FindByName_NotFound(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ts.CleanupDatabase()
	ctx := context.Background()
	repo := NewRepository()

	_, err := repo.FindByName(ctx, "__not_exist__")
	require.Error(t, err)
	require.Equal(t, ErrCategoryNotFound, err)
}
