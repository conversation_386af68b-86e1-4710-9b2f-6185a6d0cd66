package category

import (
	"realmaster-video-backend/internal/domain/common"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Category 表示视频分类模型
type Category struct {
	ID    primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	Name  string             `bson:"nm" json:"name"`
	Order int                `bson:"ord" json:"order"`
	// 移除手动时间戳字段，使用gomongo自动管理的_ts和_mt
	// CreatedAt time.Time          `bson:"ts" json:"createdAt"`
	// UpdatedAt time.Time          `bson:"mt" json:"updatedAt"`
}

// CreateCategoryRequest 创建分类的请求结构体
type CreateCategoryRequest struct {
	Name  string `json:"name" binding:"required"` // 使用binding:"required"让Gin进行基础的必填校验
	Order *int   `json:"order"`                   // 使用指针类型，以便区分 "未提供" 和 "值为0" 的情况
}

// UpdateCategoryRequest 更新分类的请求结构体
type UpdateCategoryRequest struct {
	Name  *string `json:"name"`  // 使用指针，允许部分更新
	Order *int    `json:"order"` // 使用指针，允许部分更新
}

// ListCategoriesResponseData 是分类列表响应中 'data' 字段的结构
type ListCategoriesResponseData struct {
	Items      []Category         `json:"items"`
	Pagination *common.Pagination `json:"pgn"`
}

// ListCategoriesRequest 定义了查询分类列表的请求参数
type ListCategoriesRequest struct {
	Page  int64 `form:"page"`  // 页码
	Limit int64 `form:"limit"` // 每页数量
}

// NewCategory 创建一个新的分类实例
func NewCategory(name string, order int) *Category {
	return &Category{
		Name:  name,
		Order: order,
		// gomongo会自动添加_ts和_mt字段
	}
}

// TableName 返回集合名称
func (c *Category) TableName() string {
	return "video_categories"
}
