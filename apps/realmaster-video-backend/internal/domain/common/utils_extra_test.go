package common

import (
	"testing"
)

func TestBuildGouploadURL_And_ConfigHelpers(t *testing.T) {
	// BuildGouploadURL empty path returns empty
	if got := BuildGouploadURL("TEST", "video_final", "", "https://cdn.example.com"); got != "" {
		t.Fatalf("expected empty for empty path, got %s", got)
	}

	// Prefix resolution relies on external config; function returns empty on error
	if got := BuildGouploadURL("UNKNOWN", "invalid", "user/2025-01/file.mp4", "cdn.example.com"); got != "" {
		t.Fatalf("expected empty when prefix resolve fails, got %s", got)
	}
}
