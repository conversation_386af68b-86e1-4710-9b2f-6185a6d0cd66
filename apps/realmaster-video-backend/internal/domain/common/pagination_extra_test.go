package common

import (
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestPagination_ParseAndValidate_DefaultsAndBounds(t *testing.T) {
	gin.SetMode(gin.TestMode)
	makeCtx := func(q url.Values) *gin.Context {
		req := httptest.NewRequest("GET", "/?"+q.Encode(), nil)
		c, _ := gin.CreateTestContext(httptest.NewRecorder())
		c.Request = req
		return c
	}

	// defaults (public)
	c := makeCtx(url.Values{})
	p := ParsePaginationFromQuery(c)
	if p.Page != DefaultPage || p.Limit != DefaultPageSize {
		t.Fatalf("default parse mismatch: %+v", p)
	}

	// negative page, zero/negative limit should clamp
	c2 := makeCtx(url.Values{"page": {"-5"}, "limit": {"0"}})
	p2 := ParsePaginationFromQueryWithType(c2, PaginationTypePublic)
	if p2.Page != DefaultPage || p2.Limit != DefaultPageSize {
		t.Fatalf("public clamp mismatch: %+v", p2)
	}

	// video type default and upper bound
	c3 := makeCtx(url.Values{"limit": {"999"}})
	p3 := ParsePaginationFromQueryWithType(c3, PaginationTypeVideo)
	if p3.Limit != PublicMaxPageSize {
		t.Fatalf("video upper bound mismatch: %d", p3.Limit)
	}
	c4 := makeCtx(url.Values{"limit": {"-1"}})
	p4 := ParsePaginationFromQueryWithType(c4, PaginationTypeVideo)
	if p4.Limit != VideoPageSize {
		t.Fatalf("video default mismatch: %d", p4.Limit)
	}

	// admin type default and upper bound
	c5 := makeCtx(url.Values{"limit": {"999"}})
	p5 := ParsePaginationFromQueryWithType(c5, PaginationTypeAdmin)
	if p5.Limit != AdminMaxPageSize {
		t.Fatalf("admin upper bound mismatch: %d", p5.Limit)
	}
	c6 := makeCtx(url.Values{"limit": {"-1"}})
	p6 := ParsePaginationFromQueryWithType(c6, PaginationTypeAdmin)
	if p6.Limit != AdminPageSize {
		t.Fatalf("admin default mismatch: %d", p6.Limit)
	}
}

func TestPagination_Calculate_And_Response(t *testing.T) {
	pg := CalculatePagination(0, 1, 10)
	if pg.TotalPages != 0 || pg.TotalItems != 0 {
		t.Fatalf("expect zero pagination, got %+v", pg)
	}
	pg2 := CalculatePagination(1, 1, 0)
	if pg2.TotalPages != 1 {
		t.Fatalf("expect one page when limit=0 & total>0, got %+v", pg2)
	}
	pg3 := CalculatePagination(101, 1, 20)
	if pg3.TotalPages != 6 {
		t.Fatalf("ceil division failed: %+v", pg3)
	}

	resp := NewPaginatedResponse([]int{1, 2}, 11, 1, 10)
	if resp.Pagination.TotalPages != 2 {
		t.Fatalf("response pgn failed: %+v", resp.Pagination)
	}
}
