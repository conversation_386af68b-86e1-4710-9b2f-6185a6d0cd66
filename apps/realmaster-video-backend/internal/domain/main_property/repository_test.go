package main_property

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

// helper to start a fake external API server
func startFakeAPI(t *testing.T, handler func(w http.ResponseWriter, r *http.Request)) *httptest.Server {
	ts := httptest.NewServer(http.HandlerFunc(handler))
	t.Cleanup(func() { ts.Close() })
	return ts
}

// create a repository with custom http client pointing to fake server
func newRepoWithBaseURL(t *testing.T, base string, timeout time.Duration) *repository {
	return NewRepositoryWithOptions(Options{BaseURL: base, Timeout: timeout}).(*repository)
}

func TestRepository_Search_Success_QueryAndIDs(t *testing.T) {
	// Fake server returns ok=1 with one item
	ts := startFakeAPI(t, func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(ExternalAPIResponse{Ok: 1, List: []ExternalProperty{{ID: "p1"}}})
	})

	repo := newRepoWithBaseURL(t, ts.URL, 2*time.Second)
	ctx := context.Background()

	// SearchByQuery
	props, err := repo.SearchByQuery(ctx, "keyword")
	if err != nil || len(props) != 1 || props[0].ID != "p1" {
		t.Fatalf("search by query failed: err=%v len=%d id=%v", err, len(props), func() interface{} {
			if len(props) > 0 {
				return props[0].ID
			}
			return nil
		}())
	}

	// SearchByIDs
	props2, err := repo.SearchByIDs(ctx, []string{"p1"})
	if err != nil || len(props2) != 1 || props2[0].ID != "p1" {
		t.Fatalf("search by ids failed: err=%v len=%d", err, len(props2))
	}

	// SearchByID
	p, err := repo.SearchByID(ctx, "p1")
	if err != nil || p == nil || p.ID != "p1" {
		t.Fatalf("search by id failed: err=%v p=%+v", err, p)
	}
}

func TestRepository_Search_EmptyIDsFastPath(t *testing.T) {
	repo := newRepoWithBaseURL(t, "", 2*time.Second)
	ctx := context.Background()
	props, err := repo.SearchByIDs(ctx, []string{})
	if err != nil || len(props) != 0 {
		t.Fatalf("expected empty result for empty ids, got len=%d err=%v", len(props), err)
	}
}

func TestRepository_Search_Non200(t *testing.T) {
	ts := startFakeAPI(t, func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusBadRequest)
	})

	repo := newRepoWithBaseURL(t, ts.URL, 2*time.Second)
	_, err := repo.SearchByQuery(context.Background(), "k")
	if err == nil {
		t.Fatalf("expected error for non-200 response")
	}
}

func TestRepository_Search_OkZeroReturnsEmpty(t *testing.T) {
	ts := startFakeAPI(t, func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		_ = json.NewEncoder(w).Encode(ExternalAPIResponse{Ok: 0, List: []ExternalProperty{}})
	})

	repo := newRepoWithBaseURL(t, ts.URL, 2*time.Second)
	props, err := repo.SearchByQuery(context.Background(), "k")
	if err != nil || len(props) != 0 {
		t.Fatalf("expected empty without error when ok=0, got len=%d err=%v", len(props), err)
	}
}

func TestRepository_Search_DecodeError(t *testing.T) {
	ts := startFakeAPI(t, func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		if _, err := w.Write([]byte("{")); err != nil {
			t.Fatalf("failed to write mock response: %v", err)
		}
	})

	repo := newRepoWithBaseURL(t, ts.URL, 2*time.Second)
	_, err := repo.SearchByQuery(context.Background(), "k")
	if err == nil {
		t.Fatalf("expected json decode error")
	}
}

func TestRepository_Search_Timeout(t *testing.T) {
	ts := startFakeAPI(t, func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(150 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(ExternalAPIResponse{Ok: 1, List: []ExternalProperty{}})
	})

	// set a very short timeout to trigger context deadline from http client
	repo := newRepoWithBaseURL(t, ts.URL, 50*time.Millisecond)
	_, err := repo.SearchByQuery(context.Background(), "k")
	if err == nil {
		t.Fatalf("expected timeout error")
	}
}
