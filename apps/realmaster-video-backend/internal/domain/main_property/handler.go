package main_property

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"realmaster-video-backend/internal/domain/common"
)

// Handler 处理房源相关的HTTP请求
type Handler struct {
	service Service
}

// NewHandler 创建一个新的房源处理器实例
func NewHandler(service Service) *Handler {
	return &Handler{service: service}
}

// SearchByKeyword 处理通过关键词搜索房源的请求
// @Summary 通过关键词搜索房源
// @Description 使用请求体中的关键词搜索房源
// @Tags properties
// @Accept json
// @Produce json
// @Param search body SearchByKeywordRequest true "搜索关键词"
// @Success 200 {object} common.APIResponse{data=[]MainProperty}
// @Failure 400 {object} common.APIResponse
// @Failure 500 {object} common.APIResponse
// @Router /video/admin/properties/search [post]
func (h *Handler) SearchByKeyword(c *gin.Context) {
	var req SearchByKeywordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendError(c, http.StatusBadRequest, "无效的请求体: "+err.Error(), err.Error())
		return
	}

	properties, err := h.service.SearchByKeyword(c.Request.Context(), req.Keyword)
	if err != nil {
		common.SendError(c, http.StatusInternalServerError, "搜索房源失败", err.Error())
		return
	}

	if properties == nil {
		properties = []MainProperty{}
	}

	common.SendSuccess(c, http.StatusOK, properties)
}

// GetByIDs 处理通过ID批量获取房源的请求
// @Summary 通过ID批量获取多个房源
// @Description 根据提供的ID列表获取房源列表
// @Tags properties
// @Accept json
// @Produce json
// @Param ids body BatchGetRequest true "房源ID列表"
// @Success 200 {object} common.APIResponse{data=[]MainProperty}
// @Failure 400 {object} common.APIResponse
// @Failure 500 {object} common.APIResponse
// @Router /video/admin/properties/batch-get [post]
func (h *Handler) GetByIDs(c *gin.Context) {
	var req BatchGetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendError(c, http.StatusBadRequest, "无效的请求体: "+err.Error(), err.Error())
		return
	}

	properties, err := h.service.GetByIDs(c.Request.Context(), req.IDs)
	if err != nil {
		common.SendError(c, http.StatusInternalServerError, "批量获取房源失败", err.Error())
		return
	}

	if properties == nil {
		properties = []MainProperty{}
	}

	common.SendSuccess(c, http.StatusOK, properties)
}

// GetByID 处理通过ID获取单个房源的请求
// @Summary 通过ID获取单个房源
// @Description 根据ID获取单个房源的信息
// @Tags properties
// @Accept json
// @Produce json
// @Param id path string true "房源ID"
// @Success 200 {object} common.APIResponse{data=MainProperty}
// @Failure 400 {object} common.APIResponse
// @Failure 404 {object} common.APIResponse
// @Failure 500 {object} common.APIResponse
// @Router /video/admin/properties/{id} [get]
func (h *Handler) GetByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		common.SendError(c, http.StatusBadRequest, "房源ID不能为空", "ID is empty")
		return
	}

	property, err := h.service.GetByID(c.Request.Context(), id)
	if err != nil {
		if errors.Is(err, ErrPropertyNotFound) {
			common.SendError(c, http.StatusNotFound, err.Error(), err.Error())
			return
		}
		common.SendError(c, http.StatusInternalServerError, "获取房源失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, property)
}
