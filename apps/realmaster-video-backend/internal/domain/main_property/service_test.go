package main_property

import (
	"context"
	"errors"
	"testing"
)

// fake repository for service tests
type fakeRepo struct {
	props map[string]MainProperty
}

func (f *fakeRepo) SearchByQuery(ctx context.Context, query string) ([]MainProperty, error) {
	res := make([]MainProperty, 0, len(f.props))
	for _, p := range f.props {
		res = append(res, p)
	}
	return res, nil
}

func (f *fakeRepo) SearchByID(ctx context.Context, id string) (*MainProperty, error) {
	if p, ok := f.props[id]; ok {
		pp := p
		return &pp, nil
	}
	return nil, nil
}

func (f *fakeRepo) SearchByIDs(ctx context.Context, ids []string) ([]MainProperty, error) {
	if len(ids) == 0 {
		return []MainProperty{}, nil
	}
	res := make([]MainProperty, 0, len(ids))
	for _, id := range ids {
		if p, ok := f.props[id]; ok {
			res = append(res, p)
		}
	}
	return res, nil
}

func TestService_GetByID_NotFound(t *testing.T) {
	s := NewService(&fakeRepo{props: map[string]MainProperty{}})
	_, err := s.GetByID(context.Background(), "x")
	if !errors.Is(err, ErrPropertyNotFound) {
		t.Fatalf("expected ErrPropertyNotFound, got %v", err)
	}
}

func TestService_GetByID_Found(t *testing.T) {
	fr := &fakeRepo{props: map[string]MainProperty{"1": {ID: "1", SearchAddr: "addr"}}}
	s := NewService(fr)
	p, err := s.GetByID(context.Background(), "1")
	if err != nil || p == nil || p.ID != "1" {
		t.Fatalf("get by id failed: err=%v p=%+v", err, p)
	}
}

func TestService_GetByIDs_Empty(t *testing.T) {
	s := NewService(&fakeRepo{props: map[string]MainProperty{}})
	props, err := s.GetByIDs(context.Background(), []string{})
	if err != nil || len(props) != 0 {
		t.Fatalf("expected empty slice for empty ids, got len=%d err=%v", len(props), err)
	}
}

func TestService_SearchByKeyword(t *testing.T) {
	fr := &fakeRepo{props: map[string]MainProperty{"1": {ID: "1", SearchAddr: "addr"}}}
	s := NewService(fr)
	props, err := s.SearchByKeyword(context.Background(), "k")
	if err != nil || len(props) != 1 || props[0].ID != "1" {
		t.Fatalf("search by keyword failed: err=%v len=%d", err, len(props))
	}
}
