package main_property

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// Test default constructor uses default base URL by exercising search path.
func TestNewRepository_DefaultBaseURL_AndTimeout(t *testing.T) {
	// Spin up a server that should not be hit (we'll not override base).
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(map[string]any{"ok": 1, "list": []any{}})
	}))
	defer ts.Close()

	r := NewRepository() // uses default base URL, not our ts URL
	// We cannot actually call network to real domain here; so just assert type
	if _, ok := r.(*repository); !ok {
		t.Fatalf("expect concrete *repository type")
	}
}

func TestNewRepositoryWithOptions_CustomBaseURL_AndTimeout(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Write<PERSON>eader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(map[string]any{"ok": 1, "list": []any{}})
	}))
	defer ts.Close()

	r := NewRepositoryWithOptions(Options{BaseURL: ts.URL, Timeout: 200 * time.Millisecond})
	// Invoke a simple search to hit the test server
	props, err := r.SearchByQuery(t.Context(), "condo")
	assert.NoError(t, err)
	assert.Equal(t, 0, len(props))
}
