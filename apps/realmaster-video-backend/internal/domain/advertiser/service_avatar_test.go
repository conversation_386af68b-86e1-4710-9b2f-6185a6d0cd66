package advertiser

import (
	"testing"

	"realmaster-video-backend/internal/config"
)

func TestService_buildFullAvatarURL(t *testing.T) {
	s := &service{cfg: &config.Config{}}
	s.cfg.UserUpload.Site = "UNKNOWN"
	// empty path returns empty, no error
	if got, err := s.buildFullAvatarURL(""); err != nil || got != "" {
		t.Fatalf("expected empty, nil; got %q, %v", got, err)
	}
	// invalid prefix (UNKNOWN site) should error
	if _, err := s.buildFullAvatarURL("user/2025-08/avatar.jpg"); err == nil {
		t.Fatalf("expected error for invalid prefix config")
	}
}
