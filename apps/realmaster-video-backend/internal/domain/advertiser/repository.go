package advertiser

import (
	"context"
	"fmt"

	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/platform/logger"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Repository 定义了广告主的数据访问接口
type Repository interface {
	Find(ctx context.Context, filter primitive.M, page, limit int64) ([]Advertiser, error)
	FindByID(ctx context.Context, id string) (*Advertiser, error)
	FindByObjectIDs(ctx context.Context, ids []primitive.ObjectID) ([]Advertiser, error)
	Count(ctx context.Context, filter primitive.M) (int64, error)
	Create(ctx context.Context, advertiser *Advertiser) error
	Update(ctx context.Context, id string, updateData primitive.M) error
	Delete(ctx context.Context, id string) error
	ExistsByID(ctx context.Context, id string) (bool, error)
	ExistsByEmail(ctx context.Context, email string) (bool, error)
	ExistsByPhone(ctx context.Context, phone string) (bool, error)
	ExistsByEmailAndNotID(ctx context.Context, email, id string) (bool, error)
	ExistsByPhoneAndNotID(ctx context.Context, phone, id string) (bool, error)
}

type repository struct {
	collection *gomongo.MongoCollection
}

// NewRepository 创建一个新的广告主仓库实例
func NewRepository() Repository {
	collection := database.GetCollection("realmaster_video", "video_advertisers")
	return &repository{collection: collection}
}

// Find 根据条件查询广告主列表（带分页）
func (r *repository) Find(ctx context.Context, filter primitive.M, page, limit int64) ([]Advertiser, error) {
	// 使用gomongo的正确QueryOptions方式
	queryOpts := gomongo.QueryOptions{
		Sort: bson.D{bson.E{Key: "_ts", Value: -1}},
	}

	if limit > 0 {
		queryOpts.Limit = limit
		if page > 0 {
			queryOpts.Skip = (page - 1) * limit
		}
	}

	cursor, err := r.collection.Find(ctx, filter, queryOpts)
	if err != nil {
		logger.Log.Error("查询广告主列表失败", logger.Error(err))
		return nil, fmt.Errorf("查询广告主列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var advertisers []Advertiser
	if err := cursor.All(ctx, &advertisers); err != nil {
		logger.Log.Error("解码广告主列表失败", logger.Error(err))
		return nil, fmt.Errorf("解码广告主列表失败: %w", err)
	}

	return advertisers, nil
}

// FindByID 通过ID查询单个广告主
func (r *repository) FindByID(ctx context.Context, id string) (*Advertiser, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("无效的广告主ID: %w", err)
	}

	var advertiser Advertiser
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&advertiser)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil // 未找到，不视为错误
		}
		logger.Log.Error("查询单个广告主失败", logger.Error(err), logger.String("id", id))
		return nil, fmt.Errorf("查询单个广告主失败: %w", err)
	}
	return &advertiser, nil
}

// FindByObjectIDs 批量按ID查询广告主
func (r *repository) FindByObjectIDs(ctx context.Context, ids []primitive.ObjectID) ([]Advertiser, error) {
	if len(ids) == 0 {
		return []Advertiser{}, nil
	}
	cursor, err := r.collection.Find(ctx, bson.M{"_id": bson.M{"$in": ids}}, gomongo.QueryOptions{})
	if err != nil {
		logger.Log.Error("批量查询广告主失败", logger.Error(err))
		return nil, fmt.Errorf("批量查询广告主失败: %w", err)
	}
	defer cursor.Close(ctx)

	var advertisers []Advertiser
	if err := cursor.All(ctx, &advertisers); err != nil {
		logger.Log.Error("解码广告主列表失败", logger.Error(err))
		return nil, fmt.Errorf("解码广告主列表失败: %w", err)
	}
	return advertisers, nil
}

// Count 根据条件统计广告主数量
func (r *repository) Count(ctx context.Context, filter primitive.M) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		logger.Log.Error("统计广告主数量失败", logger.Error(err))
		return 0, fmt.Errorf("统计广告主数量失败: %w", err)
	}
	return count, nil
}

// Create 创建一个新的广告主
// 支持事务：如果ctx是SessionContext，操作将在事务中执行
func (r *repository) Create(ctx context.Context, advertiser *Advertiser) error {
	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.InsertOne(ctx, advertiser)
	if err != nil {
		logger.Log.Error("创建广告主失败", logger.Error(err))
		return fmt.Errorf("创建广告主失败: %w", err)
	}

	// 使用InsertOne返回的ID回填到结构体中
	if insertedID, ok := result.InsertedID.(primitive.ObjectID); ok {
		advertiser.ID = insertedID
	} else {
		logger.Log.Error("无法获取插入的广告主ID", logger.Any("insertedID", result.InsertedID))
		return fmt.Errorf("无法获取插入的广告主ID")
	}

	logger.Log.Info("成功创建广告主",
		logger.String("id", advertiser.ID.Hex()),
		logger.String("name", advertiser.Name),
	)

	return nil
}

// Update 更新指定的广告主信息
// 支持事务：如果ctx是SessionContext，操作将在事务中执行
func (r *repository) Update(ctx context.Context, id string, updateData primitive.M) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("无效的广告主ID: %w", err)
	}

	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.UpdateOne(ctx, bson.M{"_id": objectID}, bson.M{"$set": updateData})
	if err != nil {
		logger.Log.Error("更新广告主失败", logger.Error(err), logger.String("id", id))
		return fmt.Errorf("更新广告主失败: %w", err)
	}
	if result.MatchedCount == 0 {
		return mongo.ErrNoDocuments // 使用标准错误以便 service 层判断
	}
	return nil
}

// Delete 删除一个广告主
// 支持事务：如果ctx是SessionContext，操作将在事务中执行
func (r *repository) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("无效的广告主ID: %w", err)
	}

	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		logger.Log.Error("删除广告主失败", logger.Error(err), logger.String("id", id))
		return fmt.Errorf("删除广告主失败: %w", err)
	}
	if result.DeletedCount == 0 {
		return mongo.ErrNoDocuments // 使用标准错误
	}
	return nil
}

// ExistsByID 检查广告主ID是否存在
func (r *repository) ExistsByID(ctx context.Context, id string) (bool, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return false, nil // ID格式错误，可认为不存在
	}
	count, err := r.collection.CountDocuments(ctx, bson.M{"_id": objectID})
	if err != nil {
		return false, fmt.Errorf("查询广告主ID失败: %w", err)
	}
	return count > 0, nil
}

// ExistsByEmail 检查邮箱是否已存在
func (r *repository) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{"em": email})
	if err != nil {
		return false, fmt.Errorf("查询广告主邮箱失败: %w", err)
	}
	return count > 0, nil
}

// ExistsByPhone 检查电话是否已存在
func (r *repository) ExistsByPhone(ctx context.Context, phone string) (bool, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{"ph": phone})
	if err != nil {
		return false, fmt.Errorf("查询广告主电话失败: %w", err)
	}
	return count > 0, nil
}

// ExistsByEmailAndNotID 检查除指定ID外，是否存在相同的邮箱
func (r *repository) ExistsByEmailAndNotID(ctx context.Context, email, id string) (bool, error) {
	objectID, _ := primitive.ObjectIDFromHex(id)
	filter := bson.M{"em": email, "_id": bson.M{"$ne": objectID}}
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, fmt.Errorf("查询同邮箱广告主失败: %w", err)
	}
	return count > 0, nil
}

// ExistsByPhoneAndNotID 检查除指定ID外，是否存在相同的电话
func (r *repository) ExistsByPhoneAndNotID(ctx context.Context, phone, id string) (bool, error) {
	objectID, _ := primitive.ObjectIDFromHex(id)
	filter := bson.M{"ph": phone, "_id": bson.M{"$ne": objectID}}
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, fmt.Errorf("查询同电话广告主失败: %w", err)
	}
	return count > 0, nil
}
