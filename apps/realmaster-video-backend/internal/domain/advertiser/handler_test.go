package advertiser

import (
	"bytes"
	"context"
	"encoding/json"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/testutil"
)

func mockJWT(userID string) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("authValid", true)
		c.Set("userID", userID)
		c.Next()
	}
}

func newAdvHandler(ts *testutil.TestSuite) *Handler {
	advRepo := NewRepository()
	videoRepo := video.NewRepository()
	svc := NewService(advRepo, videoRepo, ts.Config)
	return NewHandler(svc, ts.Config)
}

func makeMultipart(fields map[string]string) (contentType string, body *bytes.Buffer) {
	body = &bytes.Buffer{}
	w := multipart.NewWriter(body)
	for k, v := range fields {
		_ = w.WriteField(k, v)
	}
	_ = w.Close()
	contentType = w.FormDataContentType()
	return
}

func TestAdvertiserHandler_ListAndGet(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	h := newAdvHandler(ts)
	repo := NewRepository()
	// seed two
	_ = repo.Create(testContext(), &Advertiser{ID: primitive.NewObjectID(), Name: "A"})
	_ = repo.Create(testContext(), &Advertiser{ID: primitive.NewObjectID(), Name: "B"})

	r := gin.New()
	r.GET("/api/advertisers", h.List)
	r.GET("/api/advertisers/:id", h.GetByID)

	w := httptest.NewRecorder()
	r.ServeHTTP(w, httptest.NewRequest(http.MethodGet, "/api/advertisers?page=1&limit=1", nil))
	require.Equal(t, http.StatusOK, w.Code)

	// get by id
	itemsReq := httptest.NewRequest(http.MethodGet, "/api/advertisers", nil)
	w2 := httptest.NewRecorder()
	r.ServeHTTP(w2, itemsReq)
	var resp struct {
		Ok   int `json:"ok"`
		Data struct {
			Items []Advertiser `json:"items"`
		} `json:"data"`
	}
	_ = json.Unmarshal(w2.Body.Bytes(), &resp)
	require.GreaterOrEqual(t, len(resp.Data.Items), 2)
	id := resp.Data.Items[0].ID.Hex()

	w3 := httptest.NewRecorder()
	r.ServeHTTP(w3, httptest.NewRequest(http.MethodGet, "/api/advertisers/"+id, nil))
	require.Equal(t, http.StatusOK, w3.Code)
}

func TestAdvertiserHandler_Create_ValidateAndUnauthorized(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	h := newAdvHandler(ts)

	// missing name -> 400
	r := gin.New()
	r.Use(mockJWT("u"))
	r.POST("/api/advertisers", h.Create)
	w := httptest.NewRecorder()
	req := httptest.NewRequest(http.MethodPost, "/api/advertisers", bytes.NewBufferString(""))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusBadRequest, w.Code)

	// unauthorized with valid nm using multipart -> 401
	r2 := gin.New()
	r2.POST("/api/advertisers", h.Create)
	ct, body := makeMultipart(map[string]string{"nm": "Z"})
	w2 := httptest.NewRecorder()
	req2 := httptest.NewRequest(http.MethodPost, "/api/advertisers", body)
	req2.Header.Set("Content-Type", ct)
	r2.ServeHTTP(w2, req2)
	require.Equal(t, http.StatusUnauthorized, w2.Code)
}

func TestAdvertiserHandler_Update_MinimalFlow(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	h := newAdvHandler(ts)
	repo := NewRepository()
	adv := &Advertiser{ID: primitive.NewObjectID(), Name: "X"}
	_ = repo.Create(testContext(), adv)

	r := gin.New()
	r.Use(mockJWT("u"))
	r.PATCH("/api/advertisers/:id", h.Update)

	ct, body := makeMultipart(map[string]string{"nm": "Y"})
	req := httptest.NewRequest(http.MethodPatch, "/api/advertisers/"+adv.ID.Hex(), body)
	req.Header.Set("Content-Type", ct)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
}

func TestAdvertiserHandler_Delete_Errors(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	// 使用真实service，但不seed目标，触发错误路径
	h := newAdvHandler(ts)

	r := gin.New()
	r.DELETE("/api/advertisers/:id", h.Delete)

	// invalid id
	w := httptest.NewRecorder()
	r.ServeHTTP(w, httptest.NewRequest(http.MethodDelete, "/api/advertisers/not-hex", bytes.NewBufferString("{}")))
	require.Equal(t, http.StatusBadRequest, w.Code)
}

func TestAdvertiserHandler_ErrorMapping_ConflictAndNotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	// mock service to trigger specific errors
	ms := &mockAdvService{}
	h := NewHandler(ms, ts.Config)

	// Create conflict mapping
	ms.createErr = ErrEmailExists
	r := gin.New()
	r.Use(mockJWT("u"))
	r.POST("/api/advertisers", h.Create)
	ct1, body1 := makeMultipart(map[string]string{"nm": "A", "em": "<EMAIL>"})
	w1 := httptest.NewRecorder()
	req1 := httptest.NewRequest(http.MethodPost, "/api/advertisers", body1)
	req1.Header.Set("Content-Type", ct1)
	r.ServeHTTP(w1, req1)
	require.Equal(t, http.StatusConflict, w1.Code)

	// Update conflict mapping
	ms.updateErr = ErrPhoneExists
	r2 := gin.New()
	r2.Use(mockJWT("u"))
	r2.PATCH("/api/advertisers/:id", h.Update)
	ct2, body2 := makeMultipart(map[string]string{"ph": "+15551234567"})
	w2 := httptest.NewRecorder()
	req2 := httptest.NewRequest(http.MethodPatch, "/api/advertisers/"+primitive.NewObjectID().Hex(), body2)
	req2.Header.Set("Content-Type", ct2)
	r2.ServeHTTP(w2, req2)
	require.Equal(t, http.StatusConflict, w2.Code)

	// Update not found mapping
	ms.deleteErr = ErrAdvertiserNotFound
	r3 := gin.New()
	r3.DELETE("/api/advertisers/:id", h.Delete)
	w3 := httptest.NewRecorder()
	// send minimal JSON body to avoid bind errors
	payload := map[string]string{"target_advertiser_id": primitive.NewObjectID().Hex()}
	pb, _ := json.Marshal(payload)
	req3 := httptest.NewRequest(http.MethodDelete, "/api/advertisers/"+primitive.NewObjectID().Hex(), bytes.NewBuffer(pb))
	req3.Header.Set("Content-Type", "application/json")
	r3.ServeHTTP(w3, req3)
	require.Equal(t, http.StatusNotFound, w3.Code)

	// Update bad request mapping
	ms.updateErr = ErrInvalidEmailFormat
	w4 := httptest.NewRecorder()
	req4 := httptest.NewRequest(http.MethodPatch, "/api/advertisers/"+primitive.NewObjectID().Hex(), body2)
	req4.Header.Set("Content-Type", ct2)
	r2.ServeHTTP(w4, req4)
	require.Equal(t, http.StatusBadRequest, w4.Code)
}

// mockAdvService implements advertiser.Service for handler mapping tests
type mockAdvService struct {
	createErr error
	updateErr error
	deleteErr error
}

func (m *mockAdvService) List(ctx context.Context, req ListAdvertisersRequest) (*ListAdvertisersResponseData, error) {
	return nil, nil
}
func (m *mockAdvService) GetByID(ctx context.Context, id string) (*Advertiser, error) {
	return nil, nil
}
func (m *mockAdvService) Create(ctx context.Context, userID string, req CreateAdvertiserRequest, avatarFile *multipart.FileHeader) (*Advertiser, error) {
	if m.createErr != nil {
		return nil, m.createErr
	}
	return &Advertiser{ID: primitive.NewObjectID(), Name: req.Name, Email: req.Email, Phone: req.Phone}, nil
}
func (m *mockAdvService) Update(ctx context.Context, userID string, id string, req UpdateAdvertiserRequest, avatarFile *multipart.FileHeader) error {
	return m.updateErr
}
func (m *mockAdvService) Delete(ctx context.Context, idToDelete, targetAdvertiserID string) error {
	return m.deleteErr
}

// helpers
func testContext() context.Context { return context.Background() }
