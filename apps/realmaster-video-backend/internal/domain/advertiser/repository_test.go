package advertiser

import (
	"context"
	"testing"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/testutil"
)

func TestAdvertiserRepository_CRUD_AndExists(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ctx := context.Background()
	repo := NewRepository()

	adv := &Advertiser{ID: primitive.NewObjectID(), Name: "A", Email: "<EMAIL>", Phone: "111"}
	if err := repo.Create(ctx, adv); err != nil {
		t.Fatalf("create failed: %v", err)
	}

	// FindByID
	got, err := repo.FindByID(ctx, adv.ID.Hex())
	if err != nil || got == nil || got.Name != "A" {
		t.Fatalf("find by id failed: %v", err)
	}

	// Exists
	existsID, err := repo.ExistsByID(ctx, adv.ID.Hex())
	if err != nil || !existsID {
		t.Fatalf("exists by id failed")
	}
	existsEmail, _ := repo.ExistsByEmail(ctx, adv.Email)
	if !existsEmail {
		t.Fatalf("exists by email failed")
	}
	existsPhone, _ := repo.ExistsByPhone(ctx, adv.Phone)
	if !existsPhone {
		t.Fatalf("exists by phone failed")
	}

	// Update (use correct bson key 'nm')
	upd := primitive.M{"nm": "B"}
	if err := repo.Update(ctx, adv.ID.Hex(), upd); err != nil {
		t.Fatalf("update failed: %v", err)
	}
	got, _ = repo.FindByID(ctx, adv.ID.Hex())
	if got.Name != "B" {
		t.Fatalf("expected updated name B, got %s", got.Name)
	}

	// FindByObjectIDs
	list, err := repo.FindByObjectIDs(ctx, []primitive.ObjectID{adv.ID})
	if err != nil || len(list) != 1 {
		t.Fatalf("find by objectIDs failed: %v len=%d", err, len(list))
	}

	// Delete
	if err := repo.Delete(ctx, adv.ID.Hex()); err != nil {
		t.Fatalf("delete failed: %v", err)
	}
	gone, _ := repo.FindByID(ctx, adv.ID.Hex())
	if gone != nil {
		t.Fatalf("expected nil after delete")
	}
}
