package advertiser

import (
	"context"
	"errors"
	"strings"
	"testing"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"realmaster-video-backend/internal/config"
)

// minimal fake repo covering methods used by tests
type fakeRepo struct {
	items    map[string]Advertiser
	count    int64
	emails   map[string]bool
	phones   map[string]bool
	existsID map[string]bool
	// errors to inject
	errCreate error
	errDelete error
}

func newFakeRepo() *fakeRepo {
	return &fakeRepo{items: map[string]Advertiser{}, emails: map[string]bool{}, phones: map[string]bool{}, existsID: map[string]bool{}}
}

func (f *fakeRepo) Find(ctx context.Context, filter primitive.M, page, limit int64) ([]Advertiser, error) {
	return nil, nil
}
func (f *fakeRepo) FindByID(ctx context.Context, id string) (*Advertiser, error) {
	if a, ok := f.items[id]; ok {
		dup := a
		return &dup, nil
	}
	return nil, mongo.ErrNoDocuments
}
func (f *fakeRepo) FindByObjectIDs(ctx context.Context, ids []primitive.ObjectID) ([]Advertiser, error) {
	return nil, nil
}
func (f *fakeRepo) Count(ctx context.Context, filter primitive.M) (int64, error) { return f.count, nil }
func (f *fakeRepo) Create(ctx context.Context, advertiser *Advertiser) error {
	if f.errCreate != nil {
		return f.errCreate
	}
	if advertiser.ID.IsZero() {
		advertiser.ID = primitive.NewObjectID()
	}
	id := advertiser.ID.Hex()
	f.items[id] = *advertiser
	if advertiser.Email != "" {
		f.emails[advertiser.Email] = true
	}
	if advertiser.Phone != "" {
		f.phones[advertiser.Phone] = true
	}
	f.count++
	return nil
}
func (f *fakeRepo) Update(ctx context.Context, id string, updateData primitive.M) error { return nil }
func (f *fakeRepo) Delete(ctx context.Context, id string) error {
	if f.errDelete != nil {
		return f.errDelete
	}
	delete(f.items, id)
	f.count--
	return nil
}
func (f *fakeRepo) ExistsByID(ctx context.Context, id string) (bool, error) {
	return f.existsID[id], nil
}
func (f *fakeRepo) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	return f.emails[email], nil
}
func (f *fakeRepo) ExistsByPhone(ctx context.Context, phone string) (bool, error) {
	return f.phones[phone], nil
}
func (f *fakeRepo) ExistsByEmailAndNotID(ctx context.Context, email, id string) (bool, error) {
	return false, nil
}
func (f *fakeRepo) ExistsByPhoneAndNotID(ctx context.Context, phone, id string) (bool, error) {
	return false, nil
}

func TestAdvertiserService_Create_EmailPhoneValidation_Uniqueness_And_CreateError(t *testing.T) {
	cfg := &config.Config{}
	cfg.UserUpload.Site = "TEST"
	repo := newFakeRepo()
	// instantiate service directly to avoid initializing uploader/DB
	svc := &service{repo: repo, cfg: cfg}

	// invalid email
	if _, err := svc.Create(context.Background(), "u1", CreateAdvertiserRequest{Name: "A", Email: "bad"}, nil); !errors.Is(err, ErrInvalidEmailFormat) {
		t.Fatalf("expected ErrInvalidEmailFormat, got %v", err)
	}
	// email exists
	repo.emails["<EMAIL>"] = true
	if _, err := svc.Create(context.Background(), "u1", CreateAdvertiserRequest{Name: "A", Email: "<EMAIL>"}, nil); !errors.Is(err, ErrEmailExists) {
		t.Fatalf("expected ErrEmailExists, got %v", err)
	}
	// phone normalized and unique check
	if _, err := svc.Create(context.Background(), "u1", CreateAdvertiserRequest{Name: "A", Phone: "(*************"}, nil); err != nil {
		t.Fatalf("unexpected err: %v", err)
	}
	// duplicate phone should error (same number with +1 prefix)
	if _, err := svc.Create(context.Background(), "u1", CreateAdvertiserRequest{Name: "B", Phone: "+****************"}, nil); !errors.Is(err, ErrPhoneExists) {
		t.Fatalf("expected ErrPhoneExists, got %v", err)
	}
	// repo error on create propagates with service message
	repo.errCreate = errors.New("db fail")
	if _, err := svc.Create(context.Background(), "u1", CreateAdvertiserRequest{Name: "C"}, nil); err == nil || err.Error()[:9] != "service: " {
		t.Fatalf("expected service-wrapped error, got %v", err)
	}
}

func TestAdvertiserService_Delete_ValidationErrors(t *testing.T) {
	cfg := &config.Config{}
	cfg.UserUpload.Site = "TEST"
	repo := newFakeRepo()
	// instantiate service directly to avoid initializing uploader/DB
	svc := &service{repo: repo, cfg: cfg}

	// invalid source id
	if err := svc.Delete(context.Background(), "bad", primitive.NewObjectID().Hex()); !errors.Is(err, ErrInvalidAdvertiserID) {
		t.Fatalf("expected ErrInvalidAdvertiserID, got %v", err)
	}
	// invalid target id
	src := primitive.NewObjectID().Hex()
	if err := svc.Delete(context.Background(), src, "bad"); err == nil || !strings.HasPrefix(err.Error(), "无效的目标广告主ID") {
		t.Fatalf("expected invalid target id error, got %v", err)
	}
	// merge to self
	id := primitive.NewObjectID().Hex()
	if err := svc.Delete(context.Background(), id, id); !errors.Is(err, ErrMergeToSelf) {
		t.Fatalf("expected ErrMergeToSelf, got %v", err)
	}
	// cannot delete last
	repo.count = 1
	target := primitive.NewObjectID().Hex()
	repo.existsID[target] = true
	if err := svc.Delete(context.Background(), primitive.NewObjectID().Hex(), target); !errors.Is(err, ErrCannotDeleteLast) {
		t.Fatalf("expected ErrCannotDeleteLast, got %v", err)
	}
	// target not exists
	repo.count = 2
	repo.existsID[target] = false
	if err := svc.Delete(context.Background(), primitive.NewObjectID().Hex(), target); !errors.Is(err, ErrTargetAdvertiserNotFound) {
		t.Fatalf("expected ErrTargetAdvertiserNotFound, got %v", err)
	}
}
