package video

import (
	"bytes"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"realmaster-video-backend/internal/testutil"
)

func TestVideoHandler_InitiateChunkedUpload_InvalidJSON(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()
	suite.CreateTempDirs()
	gin.SetMode(gin.TestMode)

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	handler, err := NewHandler(service, suite.Config.Server.DraftDir, suite.Config)
	if err != nil {
		t.Skipf("skip: cannot init handler: %v", err)
	}

	req := httptest.NewRequest("POST", "/videos/chunk/initiate", bytes.NewBuffer([]byte("bad json")))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	r := gin.New()
	r.POST("/videos/chunk/initiate", handler.InitiateChunkedUpload)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestVideoHandler_InitiateChunkedUpload_Unauthorized(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()
	suite.CreateTempDirs()
	gin.SetMode(gin.TestMode)

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	handler, err := NewHandler(service, suite.Config.Server.DraftDir, suite.Config)
	if err != nil {
		t.Skipf("skip: cannot init handler: %v", err)
	}

	body := bytes.NewBufferString(`{"filename":"a.mp4","totalSize":100,"fileType":"video"}`)
	req := httptest.NewRequest("POST", "/videos/chunk/initiate", body)
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	r := gin.New()
	// 不添加 mockJWTMiddleware，触发未登录路径
	r.POST("/videos/chunk/initiate", handler.InitiateChunkedUpload)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestVideoHandler_UploadChunk_InvalidChunkNumber(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()
	suite.CreateTempDirs()
	gin.SetMode(gin.TestMode)

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	handler, err := NewHandler(service, suite.Config.Server.DraftDir, suite.Config)
	if err != nil {
		t.Skipf("skip: cannot init handler: %v", err)
	}

	req := httptest.NewRequest("POST", "/videos/chunk/up123/NaN", nil)
	w := httptest.NewRecorder()

	r := gin.New()
	r.POST("/videos/chunk/:uploadId/:chunkNumber", handler.UploadChunk)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestVideoHandler_UploadChunk_MissingFile(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()
	suite.CreateTempDirs()
	gin.SetMode(gin.TestMode)

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	handler, err := NewHandler(service, suite.Config.Server.DraftDir, suite.Config)
	if err != nil {
		t.Skipf("skip: cannot init handler: %v", err)
	}

	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)
	// 不写入名为 "chunk" 的文件字段，以触发缺失错误
	_ = writer.Close()

	req := httptest.NewRequest("POST", "/videos/chunk/up123/1", bytes.NewReader(buf.Bytes()))
	req.Header.Set("Content-Type", writer.FormDataContentType())
	w := httptest.NewRecorder()

	r := gin.New()
	r.POST("/videos/chunk/:uploadId/:chunkNumber", handler.UploadChunk)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestVideoHandler_UploadChunk_MissingParams_ManualContext(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()
	suite.CreateTempDirs()
	gin.SetMode(gin.TestMode)

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	handler, err := NewHandler(service, suite.Config.Server.DraftDir, suite.Config)
	if err != nil {
		t.Skipf("skip: cannot init handler: %v", err)
	}

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/videos/chunk//", nil)
	c.Params = []gin.Param{{Key: "uploadId", Value: ""}, {Key: "chunkNumber", Value: ""}}

	handler.UploadChunk(c)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestVideoHandler_CompleteChunkedUpload_InvalidJSON(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()
	suite.CreateTempDirs()
	gin.SetMode(gin.TestMode)

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	handler, err := NewHandler(service, suite.Config.Server.DraftDir, suite.Config)
	if err != nil {
		t.Skipf("skip: cannot init handler: %v", err)
	}

	req := httptest.NewRequest("POST", "/videos/chunk/complete", bytes.NewBuffer([]byte("{")))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	r := gin.New()
	r.POST("/videos/chunk/complete", handler.CompleteChunkedUpload)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}
