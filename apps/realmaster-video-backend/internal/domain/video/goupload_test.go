package video

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TestVideoModel_GouploadFields 测试新的goupload字段
func TestVideoModel_GouploadFields(t *testing.T) {
	t.Run("创建包含goupload字段的Video", func(t *testing.T) {
		video := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "测试视频",
				En: "Test Video",
			},
			Status:                 StatusDraft,
			UploaderID:             "test-user",
			DraftVideoGouploadPath: "test-user/2025-28/abc123/video.mp4",
			DraftThumbGouploadPath: "test-user/2025-28/abc123/thumb.jpg",
		}

		// 验证字段设置正确
		assert.Equal(t, "test-user/2025-28/abc123/video.mp4", video.DraftVideoGouploadPath)
		assert.Equal(t, "test-user/2025-28/abc123/thumb.jpg", video.DraftThumbGouploadPath)
		assert.Equal(t, StatusDraft, video.Status)
	})

	t.Run("创建包含最终goupload字段的Video", func(t *testing.T) {
		video := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "已发布视频",
				En: "Published Video",
			},
			Status:                 StatusPublished,
			UploaderID:             "test-user",
			FinalVideoGouploadPath: "test-user/2025-28/abc123/final-video.m3u8",
			FinalThumbGouploadPath: "test-user/2025-28/abc123/final-thumb.jpg",
		}

		// 验证字段设置正确
		assert.Equal(t, "test-user/2025-28/abc123/final-video.m3u8", video.FinalVideoGouploadPath)
		assert.Equal(t, "test-user/2025-28/abc123/final-thumb.jpg", video.FinalThumbGouploadPath)
		assert.Equal(t, StatusPublished, video.Status)
	})
}

// TestCreateVideoRequest_GouploadFields 测试CreateVideoRequest的goupload字段
func TestCreateVideoRequest_GouploadFields(t *testing.T) {
	t.Run("创建包含goupload字段的CreateVideoRequest", func(t *testing.T) {
		req := CreateVideoRequest{
			Title: MultilingualString{
				Zh: "测试请求",
				En: "Test Request",
			},
			UploaderID:             "test-user",
			DraftVideoGouploadPath: "test-user/2025-28/abc123/video.mp4",
			DraftThumbGouploadPath: "test-user/2025-28/abc123/thumb.jpg",
			PublishNow:             false,
		}

		// 验证字段设置正确
		assert.Equal(t, "test-user/2025-28/abc123/video.mp4", req.DraftVideoGouploadPath)
		assert.Equal(t, "test-user/2025-28/abc123/thumb.jpg", req.DraftThumbGouploadPath)
		assert.False(t, req.PublishNow)
	})
}

// TestBuildGouploadURL 测试URL构建功能（不依赖service实例）
func TestBuildGouploadURL(t *testing.T) {
	testCases := []struct {
		name         string
		entryName    string
		gouploadPath string
		baseURL      string
		expected     string
	}{
		{
			name:         "草稿视频URL",
			entryName:    "video_draft",
			gouploadPath: "test-user/2025-28/abc123/video.mp4",
			baseURL:      "http://localhost:8080",
			expected:     "http://localhost:8080/draft/videos/test-user/2025-28/abc123/video.mp4",
		},
		{
			name:         "草稿缩略图URL",
			entryName:    "thumbnail_draft",
			gouploadPath: "test-user/2025-28/abc123/thumb.jpg",
			baseURL:      "http://localhost:8080",
			expected:     "http://localhost:8080/draft/thumbnails/test-user/2025-28/abc123/thumb.jpg",
		},
		{
			name:         "最终视频URL",
			entryName:    "video_final",
			gouploadPath: "test-user/2025-28/abc123/video.m3u8",
			baseURL:      "http://localhost:8080",
			expected:     "http://localhost:8080/media/videos/test-user/2025-28/abc123/video.m3u8",
		},
		{
			name:         "最终缩略图URL",
			entryName:    "thumbnail_final",
			gouploadPath: "test-user/2025-28/abc123/thumb.jpg",
			baseURL:      "http://localhost:8080",
			expected:     "http://localhost:8080/media/thumbnails/test-user/2025-28/abc123/thumb.jpg",
		},
		{
			name:         "空路径返回空字符串",
			entryName:    "video_draft",
			gouploadPath: "",
			baseURL:      "http://localhost:8080",
			expected:     "",
		},
		{
			name:         "无效entryName返回空字符串",
			entryName:    "invalid_entry",
			gouploadPath: "test-user/2025-28/abc123/video.mp4",
			baseURL:      "http://localhost:8080",
			expected:     "",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 模拟buildGouploadURL函数的逻辑
			result := buildGouploadURLHelper(tc.entryName, tc.gouploadPath, tc.baseURL)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// buildGouploadURLHelper 辅助函数，模拟service中的buildGouploadURL方法
func buildGouploadURLHelper(entryName, gouploadPath, baseURL string) string {
	if gouploadPath == "" {
		return ""
	}

	// 根据entryName确定URL前缀
	var prefix string
	switch entryName {
	case "video_draft":
		prefix = "/draft/videos"
	case "thumbnail_draft":
		prefix = "/draft/thumbnails"
	case "video_final":
		prefix = "/media/videos"
	case "thumbnail_final":
		prefix = "/media/thumbnails"
	default:
		return ""
	}

	return baseURL + prefix + "/" + gouploadPath
}
