package video

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"realmaster-video-backend/internal/testutil"
)

func TestVideoRepository_Create(t *testing.T) {
	// 设置测试环境
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	// 创建repository实例
	repo := NewRepository()
	ctx := context.Background()

	t.Run("成功创建视频", func(t *testing.T) {
		// 准备测试数据
		video := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "测试视频标题",
				En: "Test Video Title",
			},
			Description: MultilingualString{
				Zh: "测试视频描述",
				En: "Test Video Description",
			},
			Duration:    120.5,
			UploaderID:  "test-uploader",
			CategoryID:  primitive.NewObjectID(),
			Tags:        []string{"测试", "视频"},
			Status:      StatusDraft,
			PropertyIDs: []string{"prop-123"},
			ExternalURL: "https://example.com/property/123",
			ClientID:    primitive.NewObjectID(),
			Stats: VideoStats{
				Views:          100,
				Likes:          10,
				Collections:    5,
				Completions:    80,
				CompletionRate: "80.0%",
			},
			// 使用新的goupload字段
			DraftVideoGouploadPath: "test-user/2025-28/abc123/video.mp4",
			DraftThumbGouploadPath: "test-user/2025-28/abc123/thumb.jpg",
		}

		// 执行创建操作
		err := repo.Create(ctx, video)

		// 验证结果
		require.NoError(t, err)
		assert.False(t, video.ID.IsZero())

		// 验证数据库中的数据
		foundVideo, err := repo.FindByID(ctx, video.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, video.Title.Zh, foundVideo.Title.Zh)
		assert.Equal(t, video.Status, foundVideo.Status)
		assert.Equal(t, video.Duration, foundVideo.Duration)
	})

	t.Run("创建视频时需要提供ID", func(t *testing.T) {
		video := &Video{
			ID: primitive.NewObjectID(), // 手动生成ID
			Title: MultilingualString{
				Zh: "手动ID测试",
				En: "Manual ID Test",
			},
			Status:     StatusDraft,
			UploaderID: "test-user",
		}

		err := repo.Create(ctx, video)

		require.NoError(t, err)
		assert.False(t, video.ID.IsZero())

		// 验证能够通过ID找到创建的视频
		foundVideo, err := repo.FindByID(ctx, video.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, video.ID, foundVideo.ID)
	})
}

func TestVideoRepository_FindByID(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	t.Run("成功查找存在的视频", func(t *testing.T) {
		// 先创建一个视频
		originalVideo := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "查找测试视频",
				En: "Find Test Video",
			},
			Status:     StatusDraft,
			UploaderID: "test-user",
		}

		err := repo.Create(ctx, originalVideo)
		require.NoError(t, err)

		// 通过ID查找
		foundVideo, err := repo.FindByID(ctx, originalVideo.ID.Hex())

		// 验证结果
		require.NoError(t, err)
		require.NotNil(t, foundVideo)
		assert.Equal(t, originalVideo.ID, foundVideo.ID)
		assert.Equal(t, originalVideo.Title.Zh, foundVideo.Title.Zh)
		assert.Equal(t, originalVideo.Status, foundVideo.Status)
	})

	t.Run("查找不存在的视频返回错误", func(t *testing.T) {
		nonExistentID := primitive.NewObjectID().Hex()

		video, err := repo.FindByID(ctx, nonExistentID)

		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
		assert.Nil(t, video)
	})

	t.Run("无效ID格式返回错误", func(t *testing.T) {
		invalidID := "invalid-id-format"

		video, err := repo.FindByID(ctx, invalidID)

		assert.Error(t, err)
		assert.Nil(t, video)
	})
}

func TestVideoRepository_Update(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	t.Run("成功更新视频", func(t *testing.T) {
		// 创建原始视频
		originalVideo := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "原始标题",
				En: "Original Title",
			},
			Status:     StatusDraft,
			UploaderID: "test-user",
		}

		err := repo.Create(ctx, originalVideo)
		require.NoError(t, err)

		// 更新视频
		originalVideo.Title.Zh = "更新后的标题"
		originalVideo.Status = StatusPending
		originalVideo.Duration = 300.0

		err = repo.Update(ctx, originalVideo)
		require.NoError(t, err)

		// 验证更新结果
		updatedVideo, err := repo.FindByID(ctx, originalVideo.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, "更新后的标题", updatedVideo.Title.Zh)
		assert.Equal(t, StatusPending, updatedVideo.Status)
		assert.Equal(t, 300.0, updatedVideo.Duration)
	})

	t.Run("更新不存在的视频", func(t *testing.T) {
		nonExistentVideo := &Video{
			ID:         primitive.NewObjectID(),
			Status:     StatusDraft,
			UploaderID: "test-user",
		}

		err := repo.Update(ctx, nonExistentVideo)

		// 根据实际实现，更新不存在的视频应该返回错误
		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
	})
}

func TestVideoRepository_Find(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	t.Run("按状态过滤查找视频", func(t *testing.T) {
		// 创建不同状态的视频
		draftVideo := &Video{
			ID:         primitive.NewObjectID(),
			Title:      MultilingualString{Zh: "草稿视频", En: "Draft Video"},
			Status:     StatusDraft,
			UploaderID: "test-user",
		}
		publishedVideo := &Video{
			ID:         primitive.NewObjectID(),
			Title:      MultilingualString{Zh: "已发布视频", En: "Published Video"},
			Status:     StatusPublished,
			UploaderID: "test-user",
		}

		err := repo.Create(ctx, draftVideo)
		require.NoError(t, err)
		err = repo.Create(ctx, publishedVideo)
		require.NoError(t, err)

		// 查找已发布的视频
		filter := VideoFilter{
			Status: []string{StatusPublished},
			Page:   1,
			Limit:  10,
		}

		videos, total, err := repo.Find(ctx, filter)

		require.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, videos, 1)
		assert.Equal(t, StatusPublished, videos[0].Status)
		assert.Equal(t, "已发布视频", videos[0].Title.Zh)
	})

	t.Run("分页查询", func(t *testing.T) {
		// 先清理数据库，确保测试隔离
		suite.CleanupDatabase()

		// 创建多个视频
		for i := 0; i < 5; i++ {
			video := &Video{
				ID:         primitive.NewObjectID(),
				Title:      MultilingualString{Zh: fmt.Sprintf("分页视频%d", i), En: fmt.Sprintf("PagingVideo%d", i)},
				Status:     StatusPublished,
				UploaderID: "test-user",
			}
			err := repo.Create(ctx, video)
			require.NoError(t, err)
		}

		// 第一页，每页2个
		filter := VideoFilter{
			Status: []string{StatusPublished},
			Page:   1,
			Limit:  2,
		}

		videos, total, err := repo.Find(ctx, filter)

		require.NoError(t, err)
		assert.Equal(t, int64(5), total)
		assert.Len(t, videos, 2)

		// 第二页
		filter.Page = 2
		videos, total, err = repo.Find(ctx, filter)

		require.NoError(t, err)
		assert.Equal(t, int64(5), total)
		assert.Len(t, videos, 2)
	})
}

// --- Added comprehensive tests ---

func TestVideoRepository_IncrementStats(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	v := &Video{
		ID:         primitive.NewObjectID(),
		Title:      MultilingualString{Zh: "统计视频"},
		Status:     StatusPublished,
		UploaderID: "u",
		Stats:      VideoStats{},
	}
	require.NoError(t, repo.Create(ctx, v))

	// no-op update should be ok
	require.NoError(t, repo.IncrementStats(ctx, v.ID.Hex(), 0, 0, 0, 0))
	got, err := repo.FindByID(ctx, v.ID.Hex())
	require.NoError(t, err)
	assert.Equal(t, int64(0), got.Stats.Views)
	assert.Equal(t, int64(0), got.Stats.Completions)

	// increment all
	require.NoError(t, repo.IncrementStats(ctx, v.ID.Hex(), 1, 2, 3, 1))
	got, err = repo.FindByID(ctx, v.ID.Hex())
	require.NoError(t, err)
	assert.Equal(t, int64(1), got.Stats.Views)
	assert.Equal(t, int64(2), got.Stats.Likes)
	assert.Equal(t, int64(3), got.Stats.Collections)
	assert.Equal(t, int64(1), got.Stats.Completions)

	// wait for async completion rate update
	waitUntil(t, 3*time.Second, func() bool {
		v2, e := repo.FindByID(ctx, v.ID.Hex())
		if e != nil {
			return false
		}
		cr := v2.Stats.CompletionRate
		return cr == "100%" || cr == "100.0%"
	})

	// video not found
	err = repo.IncrementStats(ctx, primitive.NewObjectID().Hex(), 1, 0, 0, 0)
	assert.Equal(t, ErrVideoNotFound, err)
}

func TestVideoRepository_Find_FilterByClientAndNull(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	clientA := primitive.NewObjectID()
	clientB := primitive.NewObjectID()

	vNull := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "N"}, Status: StatusPublished, UploaderID: "u"}
	vA := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "A"}, Status: StatusPublished, UploaderID: "u", ClientID: clientA}
	vB := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "B"}, Status: StatusPublished, UploaderID: "u", ClientID: clientB}
	require.NoError(t, repo.Create(ctx, vNull))
	require.NoError(t, repo.Create(ctx, vA))
	require.NoError(t, repo.Create(ctx, vB))

	// null client
	videos, total, err := repo.Find(ctx, VideoFilter{FilterForNullClient: true, Page: 1, Limit: 10})
	require.NoError(t, err)
	assert.Equal(t, int64(1), total)
	require.Len(t, videos, 1)
	assert.True(t, videos[0].ClientID.IsZero())

	// client A
	videos, total, err = repo.Find(ctx, VideoFilter{ClientID: &clientA, Page: 1, Limit: 10})
	require.NoError(t, err)
	assert.Equal(t, int64(1), total)
	require.Len(t, videos, 1)
	assert.Equal(t, clientA, videos[0].ClientID)
}

func TestVideoRepository_Find_DateRange(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	now := time.Now()
	v1 := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "d-3"}, Status: StatusPublished, UploaderID: "u", PublishedAt: ptrTime(now.Add(-72 * time.Hour))}
	v2 := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "d-2"}, Status: StatusPublished, UploaderID: "u", PublishedAt: ptrTime(now.Add(-48 * time.Hour))}
	v3 := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "d-1"}, Status: StatusPublished, UploaderID: "u", PublishedAt: ptrTime(now.Add(-24 * time.Hour))}
	require.NoError(t, repo.Create(ctx, v1))
	require.NoError(t, repo.Create(ctx, v2))
	require.NoError(t, repo.Create(ctx, v3))

	from := now.Add(-25 * time.Hour)
	to := now.Add(-23 * time.Hour)
	videos, total, err := repo.Find(ctx, VideoFilter{FromDate: &from, ToDate: &to, Page: 1, Limit: 10})
	require.NoError(t, err)
	assert.Equal(t, int64(1), total)
	require.Len(t, videos, 1)
	assert.Equal(t, "d-1", videos[0].Title.Zh)
}

func TestVideoRepository_FindOneAndUpdateToProcessing(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	v := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "P"}, Status: StatusPending, UploaderID: "u"}
	require.NoError(t, repo.Create(ctx, v))

	updated, err := repo.FindOneAndUpdateToProcessing(ctx)
	require.NoError(t, err)
	require.NotNil(t, updated)
	assert.Equal(t, StatusProcessing, updated.Status)

	// no pending left
	updated, err = repo.FindOneAndUpdateToProcessing(ctx)
	assert.Error(t, err)
	assert.Equal(t, mongo.ErrNoDocuments, err)
	assert.Nil(t, updated)
}

func TestVideoRepository_UpdateAdvertiser(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	oldID := primitive.NewObjectID()
	newID := primitive.NewObjectID()
	v1 := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "1"}, Status: StatusDraft, UploaderID: "u", ClientID: oldID}
	v2 := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "2"}, Status: StatusPublished, UploaderID: "u", ClientID: oldID}
	require.NoError(t, repo.Create(ctx, v1))
	require.NoError(t, repo.Create(ctx, v2))

	cnt, err := repo.UpdateAdvertiser(ctx, oldID, newID)
	require.NoError(t, err)
	assert.Equal(t, int64(2), cnt)

	vv1, _ := repo.FindByID(ctx, v1.ID.Hex())
	vv2, _ := repo.FindByID(ctx, v2.ID.Hex())
	assert.Equal(t, newID, vv1.ClientID)
	assert.Equal(t, newID, vv2.ClientID)
}

func TestVideoRepository_UpdateSelective_And_StatusCheck(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	v := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "S"}, Status: StatusDraft, UploaderID: "u", ExternalURL: "x"}
	require.NoError(t, repo.Create(ctx, v))

	// UpdateSelective: set ExtUrl, unset finalVideoGouploadPath
	upd := map[string]interface{}{
		"ExtUrl":                 "y",
		"finalVideoGouploadPath": "", // unset
	}
	require.NoError(t, repo.UpdateSelective(ctx, v.ID, upd))

	vv, _ := repo.FindByID(ctx, v.ID.Hex())
	assert.Equal(t, "y", vv.ExternalURL)

	// StatusCheck: expect Pending but current Draft -> error
	err := repo.UpdateSelectiveWithStatusCheck(ctx, v.ID, StatusPending, map[string]interface{}{"ExtUrl": "z"})
	assert.Error(t, err)
}

func TestVideoRepository_DeleteByID(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	v := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "D"}, Status: StatusDraft, UploaderID: "u"}
	require.NoError(t, repo.Create(ctx, v))

	require.NoError(t, repo.DeleteByID(ctx, v.ID))
	_, err := repo.FindByID(ctx, v.ID.Hex())
	assert.Equal(t, ErrVideoNotFound, err)

	// delete again -> not found
	err = repo.DeleteByID(ctx, v.ID)
	assert.Equal(t, ErrVideoNotFound, err)
}

func TestVideoRepository_GetAggregatedStats(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	// one with views/completions, one empty
	v1 := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "A"}, Status: StatusPublished, UploaderID: "u", Stats: VideoStats{Views: 10, Likes: 3, Collections: 2, Completions: 5}}
	v2 := &Video{ID: primitive.NewObjectID(), Title: MultilingualString{Zh: "B"}, Status: StatusPublished, UploaderID: "u", Stats: VideoStats{}}
	require.NoError(t, repo.Create(ctx, v1))
	require.NoError(t, repo.Create(ctx, v2))

	sum, err := repo.GetAggregatedStats(ctx, VideoFilter{Status: []string{StatusPublished}})
	require.NoError(t, err)
	assert.Equal(t, int64(2), sum.TotalVideos)
	assert.Equal(t, int64(10), sum.TotalViews)
	assert.Equal(t, int64(3), sum.TotalLikes)
	assert.Equal(t, int64(2), sum.TotalCollections)
	assert.Equal(t, "50%", sum.OverallCompletionRate)
}

// helpers
func ptrTime(ti time.Time) *time.Time { return &ti }

func waitUntil(t *testing.T, timeout time.Duration, cond func() bool) {
	deadline := time.Now().Add(timeout)
	for time.Now().Before(deadline) {
		if cond() {
			return
		}
		time.Sleep(50 * time.Millisecond)
	}
	t.Fatalf("condition not met within %v", timeout)
}
