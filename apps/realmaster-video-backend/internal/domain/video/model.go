package video

import (
	"time"

	"mime/multipart"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// 视频处理状态常量
const (
	StatusDraft            = "Draft"
	StatusPending          = "Pending"
	StatusProcessing       = "Processing"
	StatusPublished        = "Published"
	StatusProcessingFailed = "ProcessingFailed"
	StatusUnpublished      = "Unpublished"
)

// MultilingualString 定义了支持多语言的字符串结构
type MultilingualString struct {
	Zh string `bson:"zh" json:"zh"`
	En string `bson:"en,omitempty" json:"en,omitempty"`
}

// VideoStats 包含视频的统计数据
type VideoStats struct {
	Views          int64  `bson:"vws" json:"views"`
	Likes          int64  `bson:"lks" json:"likes"`
	Collections    int64  `bson:"cltsCnt" json:"collections"`
	Completions    int64  `bson:"cplCnt" json:"completions"`
	CompletionRate string `bson:"cplRt" json:"completionRate"`
}

// Video 是数据库中的核心视频模型
type Video struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	Title       MultilingualString `bson:"tl" json:"title"`
	Description MultilingualString `bson:"dsc,omitempty" json:"description,omitempty"`

	// === goupload路径字段 - 统一文件管理 ===
	DraftVideoGouploadPath string `bson:"draftVideoGouploadPath,omitempty" json:"-"`
	DraftThumbGouploadPath string `bson:"draftThumbGouploadPath,omitempty" json:"-"`
	FinalVideoGouploadPath string `bson:"finalVideoGouploadPath,omitempty" json:"-"`
	FinalThumbGouploadPath string `bson:"finalThumbGouploadPath,omitempty" json:"-"`
	ManifestsBaseName      string `bson:"manifestsBaseName,omitempty" json:"-"` // Base name for manifest and mp4 files

	Duration        float64            `bson:"dur" json:"duration"`
	Width           int                `bson:"w,omitempty" json:"width,omitempty"`  // 视频宽度
	Height          int                `bson:"h,omitempty" json:"height,omitempty"` // 视频高度
	UploaderID      string             `bson:"uldId" json:"uploaderId"`             // JWT中的真实用户ID，goupload需要用到
	CategoryID      primitive.ObjectID `bson:"catId,omitempty" json:"categoryId,omitempty"`
	Tags            []string           `bson:"tags,omitempty" json:"tags,omitempty"`
	Status          string             `bson:"st" json:"status"`
	PropertyIDs     []string           `bson:"prop,omitempty" json:"propertyIds,omitempty"`
	ExternalURL     string             `bson:"ExtUrl,omitempty" json:"externalUrl,omitempty"`
	ClientID        primitive.ObjectID `bson:"ClntId,omitempty" json:"clientId,omitempty"`
	Stats           VideoStats         `bson:"stat" json:"stats"`
	ProcessingError string             `bson:"procErr,omitempty" json:"processingError,omitempty"`
	// 移除手动时间戳字段，使用gomongo自动管理的_ts和_mt
	// CreatedAt       time.Time          `bson:"ts" json:"createdAt"`
	// UpdatedAt       time.Time          `bson:"mt" json:"updatedAt"`
	PublishedAt *time.Time `bson:"pts,omitempty" json:"publishedAt,omitempty"`
}

// VideoResponse is the DTO (Data Transfer Object) for API responses.
// It defines the structure of video data sent to the client, keeping the DB model clean.
type VideoResponse struct {
	ID              primitive.ObjectID `json:"id,omitempty"`
	Title           MultilingualString `json:"title"`
	Description     MultilingualString `json:"description,omitempty"`
	Status          string             `json:"status"`
	Duration        float64            `json:"duration"`
	Width           int                `json:"width,omitempty"`  // 视频宽度
	Height          int                `json:"height,omitempty"` // 视频高度
	PreviewThumbUrl string             `json:"previewThumbUrl,omitempty"`
	PreviewVideoUrl string             `json:"previewVideoUrl,omitempty"`
	UploaderID      string             `json:"uploaderId"`
	CategoryID      primitive.ObjectID `json:"categoryId,omitempty"`
	Tags            []string           `json:"tags,omitempty"`
	PropertyIDs     []string           `json:"propertyIds,omitempty"`
	ExternalURL     string             `json:"externalUrl,omitempty"`
	ClientID        primitive.ObjectID `json:"clientId,omitempty"`
	Client          *ClientSummary     `json:"client,omitempty"`
	Stats           VideoStats         `json:"stats"`
	ProcessingError string             `json:"processingError,omitempty"`
	// 移除手动时间戳字段，API响应中可以从gomongo的_ts和_mt获取
	// CreatedAt       time.Time          `json:"createdAt"`
	// UpdatedAt       time.Time          `json:"updatedAt"`
	PublishedAt *time.Time `json:"publishedAt,omitempty"`
}

// ClientSummary 提供前端展示所需的最小客户信息
type ClientSummary struct {
	ID        primitive.ObjectID `json:"id"`
	Name      string             `json:"name"`
	AvatarURL string             `json:"avatarUrl,omitempty"`
	Phone     string             `json:"phone,omitempty"`
	Email     string             `json:"email,omitempty"`
}

// VideoStatsSummary is the DTO for the aggregated video statistics API response.
type VideoStatsSummary struct {
	TotalVideos           int64  `json:"totalVideos"`
	TotalViews            int64  `json:"totalViews"`
	TotalLikes            int64  `json:"totalLikes"`
	TotalCollections      int64  `json:"totalCollections"`
	OverallCompletionRate string `json:"overallCompletionRate"`
}

// --- API 请求与响应结构体 ---

// CreateVideoRequest 定义了创建新视频草稿的结构体
// Handler 会从 metadata JSON 字段中解析此结构
type CreateVideoRequest struct {
	Title                  MultilingualString `json:"title"`
	Description            MultilingualString `json:"description"`
	UploaderID             string             `json:"uploaderId"`
	CategoryID             string             `json:"categoryId"`
	Tags                   []string           `json:"tags"`
	PropertyIDs            []string           `json:"propertyIds"`
	ExternalURL            string             `json:"externalUrl"`
	ClientID               string             `json:"clientId"`
	DraftVideoGouploadPath string             `json:"draftVideoGouploadPath,omitempty"` // goupload返回的相对路径
	DraftThumbGouploadPath string             `json:"draftThumbGouploadPath,omitempty"` // goupload返回的相对路径
	PublishNow             bool               `json:"publishNow,omitempty"`             // 支持从JSON解析和表单字段设置
}

// VideoFilter defines the criteria for filtering videos.
// Pointers are used to distinguish between zero-value and non-provided fields.
type VideoFilter struct {
	CategoryID          *primitive.ObjectID
	ClientID            *primitive.ObjectID
	FilterForNullClient bool     // New flag to specifically filter for null ClientID
	Status              []string // 可接受多个状态
	FromDate            *time.Time
	ToDate              *time.Time
	Page                int
	Limit               int
}

// UpdateVideoRequest defines the structure for updating a video.
// It supports both metadata and file updates.
type UpdateVideoRequest struct {
	ID                 primitive.ObjectID
	Metadata           map[string]interface{}
	Action             string                // e.g., "publish", "unpublish", "save_as_draft"
	DraftVideoFile     *multipart.FileHeader // For direct multipart uploads
	DraftThumbnailFile *multipart.FileHeader // For direct multipart uploads
	// Fields for post-chunked upload
	DraftVideoGouploadPath string
	DraftThumbGouploadPath string
}
