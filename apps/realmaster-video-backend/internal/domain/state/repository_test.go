package state

import (
	"context"
	"testing"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/testutil"
)

func TestStateRepository_UpsertAndFind(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ctx := context.Background()
	repo := NewRepository()

	user := primitive.NewObjectID()
	video := primitive.NewObjectID()
	st := NewState(user, video)
	st.Liked = true
	st.ProgressSeconds = 10
	if err := repo.Upsert(ctx, st); err != nil {
		t.Fatalf("upsert create failed: %v", err)
	}

	// update fields and ensure no cross-field overwrite
	st.Favorited = true
	st.ProgressSeconds = 20
	if err := repo.Upsert(ctx, st); err != nil {
		t.Fatalf("upsert update failed: %v", err)
	}

	got, err := repo.FindByUserAndVideo(ctx, user, video)
	if err != nil {
		t.Fatalf("find failed: %v", err)
	}
	if got == nil {
		t.Fatalf("expected state found")
	}
	if !got.Liked || !got.Favorited || got.Blocked {
		t.Fatalf("fields not persisted as expected")
	}
	if got.ProgressSeconds != 20 {
		t.Fatalf("expected progSec=20, got %d", got.ProgressSeconds)
	}

	// non-existent
	none, err := repo.FindByUserAndVideo(ctx, user, primitive.NewObjectID())
	if err != nil {
		t.Fatalf("find non-existent failed: %v", err)
	}
	if none != nil {
		t.Fatalf("expected nil for non-existent")
	}
}

func TestStateRepository_FindBatchByUser(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ctx := context.Background()
	repo := NewRepository()

	user := primitive.NewObjectID()
	v1 := primitive.NewObjectID()
	v2 := primitive.NewObjectID()
	v3 := primitive.NewObjectID()
	_ = repo.Upsert(ctx, &State{ID: StateID{UserID: user, VideoID: v1}, Liked: true})
	_ = repo.Upsert(ctx, &State{ID: StateID{UserID: user, VideoID: v2}, Favorited: true})

	states, err := repo.FindBatchByUser(ctx, user, []primitive.ObjectID{v1, v2, v3})
	if err != nil {
		t.Fatalf("batch find failed: %v", err)
	}
	if len(states) != 2 {
		t.Fatalf("expected 2, got %d", len(states))
	}
}

func TestStateRepository_FindUserFavorites_Pagination(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ctx := context.Background()
	repo := NewRepository()

	user := primitive.NewObjectID()
	// seed 25 favorites
	for i := 0; i < 25; i++ {
		_ = repo.Upsert(ctx, &State{ID: StateID{UserID: user, VideoID: primitive.NewObjectID()}, Favorited: true})
	}
	// page 2, limit 20 -> 5
	items, total, err := repo.FindUserFavorites(ctx, user, 2, 20)
	if err != nil {
		t.Fatalf("find favorites failed: %v", err)
	}
	if total < 25 {
		t.Fatalf("expected total>=25, got %d", total)
	}
	if len(items) != 5 {
		t.Fatalf("expected 5 items on page 2, got %d", len(items))
	}
}

func TestStateRepository_DeleteByUserAndVideo(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ctx := context.Background()
	repo := NewRepository()

	user := primitive.NewObjectID()
	vid := primitive.NewObjectID()
	_ = repo.Upsert(ctx, &State{ID: StateID{UserID: user, VideoID: vid}, Liked: true})

	if err := repo.DeleteByUserAndVideo(ctx, user, vid); err != nil {
		t.Fatalf("delete failed: %v", err)
	}
	// delete again should not error
	_ = repo.DeleteByUserAndVideo(ctx, user, vid)
}
