package interaction

import (
	"context"
	"fmt"
	"time"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/platform/logger"
)

// Repository 定义交互事件的数据访问接口
type Repository interface {
	Create(ctx context.Context, interaction *Interaction) error
	FindByUser(ctx context.Context, userID primitive.ObjectID, limit int64) ([]Interaction, error)
	FindByVideo(ctx context.Context, videoID primitive.ObjectID, limit int64) ([]Interaction, error)
	CountByType(ctx context.Context, videoID primitive.ObjectID, interactionType InteractionType) (int64, error)
}

type repository struct {
	collection *gomongo.MongoCollection
}

// NewRepository 创建新的交互事件仓库实例
func NewRepository() Repository {
	collection := database.GetCollection("realmaster_video", "video_interactions")
	return &repository{collection: collection}
}

// Create 创建新的交互事件记录
// 支持事务：如果ctx是SessionContext，操作将在事务中执行
func (r *repository) Create(ctx context.Context, interaction *Interaction) error {
	// 设置时间戳
	interaction.Timestamp = time.Now()

	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.InsertOne(ctx, interaction)
	if err != nil {
		logger.Log.Error("创建交互事件失败",
			logger.Error(err),
			logger.String("userId", interaction.UID.Hex()),
			logger.String("videoId", interaction.VID.Hex()),
			logger.String("type", string(interaction.Type)),
		)
		return fmt.Errorf("创建交互事件失败: %w", err)
	}

	// 使用InsertOne返回的ID回填到结构体中
	if insertedID, ok := result.InsertedID.(primitive.ObjectID); ok {
		interaction.ID = insertedID
	} else {
		logger.Log.Error("无法获取插入的交互事件ID", logger.Any("insertedID", result.InsertedID))
		return fmt.Errorf("无法获取插入的交互事件ID")
	}

	logger.Log.Info("成功创建交互事件",
		logger.String("id", interaction.ID.Hex()),
		logger.String("userId", interaction.UID.Hex()),
		logger.String("videoId", interaction.VID.Hex()),
		logger.String("type", string(interaction.Type)),
	)

	return nil
}

// FindByUser 根据用户ID查询交互事件
func (r *repository) FindByUser(ctx context.Context, userID primitive.ObjectID, limit int64) ([]Interaction, error) {
	filter := bson.M{"uid": userID}

	queryOpts := gomongo.QueryOptions{
		Sort:  bson.D{bson.E{Key: "_ts", Value: -1}}, // 按时间倒序
		Limit: limit,
	}

	cursor, err := r.collection.Find(ctx, filter, queryOpts)
	if err != nil {
		logger.Log.Error("查询用户交互事件失败",
			logger.Error(err),
			logger.String("userId", userID.Hex()),
		)
		return nil, fmt.Errorf("查询用户交互事件失败: %w", err)
	}
	defer cursor.Close(ctx)

	var interactions []Interaction
	if err := cursor.All(ctx, &interactions); err != nil {
		logger.Log.Error("解码用户交互事件失败",
			logger.Error(err),
			logger.String("userId", userID.Hex()),
		)
		return nil, fmt.Errorf("解码用户交互事件失败: %w", err)
	}

	return interactions, nil
}

// FindByVideo 根据视频ID查询交互事件
func (r *repository) FindByVideo(ctx context.Context, videoID primitive.ObjectID, limit int64) ([]Interaction, error) {
	filter := bson.M{"vid": videoID}

	queryOpts := gomongo.QueryOptions{
		Sort:  bson.D{bson.E{Key: "_ts", Value: -1}}, // 按时间倒序
		Limit: limit,
	}

	cursor, err := r.collection.Find(ctx, filter, queryOpts)
	if err != nil {
		logger.Log.Error("查询视频交互事件失败",
			logger.Error(err),
			logger.String("videoId", videoID.Hex()),
		)
		return nil, fmt.Errorf("查询视频交互事件失败: %w", err)
	}
	defer cursor.Close(ctx)

	var interactions []Interaction
	if err := cursor.All(ctx, &interactions); err != nil {
		logger.Log.Error("解码视频交互事件失败",
			logger.Error(err),
			logger.String("videoId", videoID.Hex()),
		)
		return nil, fmt.Errorf("解码视频交互事件失败: %w", err)
	}

	return interactions, nil
}

// CountByType 统计特定类型的交互事件数量
func (r *repository) CountByType(ctx context.Context, videoID primitive.ObjectID, interactionType InteractionType) (int64, error) {
	filter := bson.M{
		"vid": videoID,
		"tp":  interactionType,
	}

	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		logger.Log.Error("统计交互事件数量失败",
			logger.Error(err),
			logger.String("videoId", videoID.Hex()),
			logger.String("type", string(interactionType)),
		)
		return 0, fmt.Errorf("统计交互事件数量失败: %w", err)
	}

	return count, nil
}
