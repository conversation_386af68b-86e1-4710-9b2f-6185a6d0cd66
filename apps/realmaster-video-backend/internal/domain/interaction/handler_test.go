package interaction

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/domain/state"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/testutil"
)

func mockJWT(userID string) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("authValid", true)
		c.Set("userID", userID)
		c.Next()
	}
}

func seedVideoForHandler(t *testing.T, repo video.Repository) primitive.ObjectID {
	vid := primitive.NewObjectID()
	v := &video.Video{
		ID:         vid,
		Title:      video.MultilingualString{Zh: "v"},
		Status:     video.StatusPublished,
		Duration:   12,
		UploaderID: "u",
	}
	now := time.Now()
	v.PublishedAt = &now
	require.NoError(t, repo.Create(testContext(), v))
	return vid
}

func newHandler(ts *testutil.TestSuite) *Handler {
	interRepo := NewRepository()
	stateRepo := state.NewRepository()
	videoRepo := video.NewRepository()
	txMgr := database.NewTransactionManager("realmaster_video", false, "video_interactions")
	svc := NewService(interRepo, stateRepo, videoRepo, txMgr)
	return NewHandler(svc)
}

func testContext() context.Context { return context.Background() }

func TestInteractionHandler_Create_OK(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	h := newHandler(ts)
	vr := video.NewRepository()
	vid := seedVideoForHandler(t, vr)

	r := gin.New()
	r.Use(mockJWT("u1"))
	r.POST("/video/public/interactions", h.CreateInteraction)

	payload := map[string]interface{}{"videoId": vid.Hex(), "type": string(TypeLike)}
	b, _ := json.Marshal(payload)
	req := httptest.NewRequest(http.MethodPost, "/video/public/interactions", bytes.NewReader(b))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
}

func TestInteractionHandler_Create_InvalidType(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	h := newHandler(ts)
	vr := video.NewRepository()
	vid := seedVideoForHandler(t, vr)

	r := gin.New()
	r.Use(mockJWT("u1"))
	r.POST("/video/public/interactions", h.CreateInteraction)

	payload := map[string]interface{}{"videoId": vid.Hex(), "type": "invalid_type"}
	b, _ := json.Marshal(payload)
	req := httptest.NewRequest(http.MethodPost, "/video/public/interactions", bytes.NewReader(b))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusBadRequest, w.Code)
}

func TestInteractionHandler_Create_InvalidVideoID(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	h := newHandler(ts)

	r := gin.New()
	r.Use(mockJWT("u1"))
	r.POST("/video/public/interactions", h.CreateInteraction)

	payload := map[string]interface{}{"videoId": "not-a-hex", "type": string(TypeLike)}
	b, _ := json.Marshal(payload)
	req := httptest.NewRequest(http.MethodPost, "/video/public/interactions", bytes.NewReader(b))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	// service会返回视频ID无效，当前实现作为内部错误处理
	require.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestInteractionHandler_Create_Unauthorized(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	h := newHandler(ts)
	r := gin.New()
	r.POST("/video/public/interactions", h.CreateInteraction)

	payload := map[string]interface{}{"videoId": primitive.NewObjectID().Hex(), "type": string(TypeLike)}
	b, _ := json.Marshal(payload)
	req := httptest.NewRequest(http.MethodPost, "/video/public/interactions", bytes.NewReader(b))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestInteractionHandler_GetUserInteractions_OK(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	interRepo := NewRepository()
	stateRepo := state.NewRepository()
	videoRepo := video.NewRepository()
	txMgr := database.NewTransactionManager("realmaster_video", false, "video_interactions")
	svc := NewService(interRepo, stateRepo, videoRepo, txMgr)
	h := NewHandler(svc)

	vid := primitive.NewObjectID()
	// seed interactions via service (视频不存在也允许创建)
	_, _ = svc.CreateInteraction(testContext(), "u2", CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeLike})
	_, _ = svc.CreateInteraction(testContext(), "u2", CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeFavorite})

	r := gin.New()
	r.Use(mockJWT("u2"))
	r.GET("/video/public/interactions/user", h.GetUserInteractions)

	req := httptest.NewRequest(http.MethodGet, "/video/public/interactions/user?limit=abc", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
}

func TestInteractionHandler_GetUserInteractions_Unauthorized(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	h := newHandler(ts)
	r := gin.New()
	r.GET("/video/public/interactions/user", h.GetUserInteractions)

	req := httptest.NewRequest(http.MethodGet, "/video/public/interactions/user", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestInteractionHandler_GetVideoInteractions_OK(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	interRepo := NewRepository()
	stateRepo := state.NewRepository()
	videoRepo := video.NewRepository()
	txMgr := database.NewTransactionManager("realmaster_video", false, "video_interactions")
	svc := NewService(interRepo, stateRepo, videoRepo, txMgr)
	h := NewHandler(svc)

	vid := primitive.NewObjectID()
	_, _ = svc.CreateInteraction(testContext(), "u3", CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeLike})
	_, _ = svc.CreateInteraction(testContext(), "u4", CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeFavorite})

	r := gin.New()
	r.Use(mockJWT("u5"))
	r.GET("/video/public/interactions/video/:videoId", h.GetVideoInteractions)

	req := httptest.NewRequest(http.MethodGet, "/video/public/interactions/video/"+vid.Hex()+"?limit=-1", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)
}

func TestInteractionHandler_GetVideoInteractions_InvalidVideoID(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()

	h := newHandler(ts)

	r := gin.New()
	r.Use(mockJWT("u6"))
	r.GET("/video/public/interactions/video/:videoId", h.GetVideoInteractions)

	req := httptest.NewRequest(http.MethodGet, "/video/public/interactions/video/not-a-hex", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	// service 解析 videoId 失败，handler 返回 500
	require.Equal(t, http.StatusInternalServerError, w.Code)
}
