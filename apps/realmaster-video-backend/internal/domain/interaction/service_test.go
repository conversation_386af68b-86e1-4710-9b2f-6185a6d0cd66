package interaction

import (
	"context"
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/domain/state"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/testutil"
)

func seedVideo(t *testing.T, repo video.Repository) primitive.ObjectID {
	t.Helper()
	id := primitive.NewObjectID()
	v := &video.Video{
		ID:         id,
		Title:      video.MultilingualString{Zh: "v"},
		Status:     video.StatusPublished,
		Duration:   10,
		UploaderID: "u",
	}
	now := time.Now()
	v.PublishedAt = &now
	if err := repo.Create(context.Background(), v); err != nil {
		t.Fatalf("seed video failed: %v", err)
	}
	return id
}

func newServiceForTest(cfg *testutil.TestSuite) Service {
	interRepo := NewRepository()
	stateRepo := state.NewRepository()
	videoRepo := video.NewRepository()
	// 测试环境关闭事务
	txMgr := database.NewTransactionManager("realmaster_video", false, "video_interactions")
	return NewService(interRepo, stateRepo, videoRepo, txMgr)
}

func TestService_CreateInteraction_StateAndStats(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ctx := context.Background()

	svc := newServiceForTest(ts)
	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()

	vid := seedVideo(t, videoRepo)
	user := "userA"

	// like
	_, err := svc.CreateInteraction(ctx, user, CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeLike})
	if err != nil {
		t.Fatalf("Create like failed: %v", err)
	}
	// assert state liked=true
	st, err := stateRepo.FindByUserAndVideo(ctx, svc.(*service).generateObjectIDFromString(user), vid)
	if err != nil || st == nil || !st.Liked {
		t.Fatalf("state not liked as expected")
	}
	// assert stats likes=1
	vv, err := videoRepo.FindByID(ctx, vid.Hex())
	if err != nil || vv.Stats.Likes != 1 {
		t.Fatalf("expected likes=1, got %d", vv.Stats.Likes)
	}

	// unlike
	_, err = svc.CreateInteraction(ctx, user, CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeUnlike})
	if err != nil {
		t.Fatalf("Create unlike failed: %v", err)
	}
	vv, _ = videoRepo.FindByID(ctx, vid.Hex())
	if vv.Stats.Likes != 0 {
		t.Fatalf("expected likes=0, got %d", vv.Stats.Likes)
	}
	st, _ = stateRepo.FindByUserAndVideo(ctx, svc.(*service).generateObjectIDFromString(user), vid)
	if st.Liked {
		t.Fatalf("expected liked=false after unlike")
	}

	// favorite then unfavorite
	_, _ = svc.CreateInteraction(ctx, user, CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeFavorite})
	vv, _ = videoRepo.FindByID(ctx, vid.Hex())
	if vv.Stats.Collections != 1 {
		t.Fatalf("expected collections=1, got %d", vv.Stats.Collections)
	}
	st, _ = stateRepo.FindByUserAndVideo(ctx, svc.(*service).generateObjectIDFromString(user), vid)
	if !st.Favorited {
		t.Fatalf("expected favorited=true")
	}

	_, _ = svc.CreateInteraction(ctx, user, CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeUnfavorite})
	vv, _ = videoRepo.FindByID(ctx, vid.Hex())
	if vv.Stats.Collections != 0 {
		t.Fatalf("expected collections=0, got %d", vv.Stats.Collections)
	}
	st, _ = stateRepo.FindByUserAndVideo(ctx, svc.(*service).generateObjectIDFromString(user), vid)
	if st.Favorited {
		t.Fatalf("expected favorited=false")
	}

	// view_start increments views
	_, _ = svc.CreateInteraction(ctx, user, CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeViewStart})
	vv, _ = videoRepo.FindByID(ctx, vid.Hex())
	if vv.Stats.Views < 1 {
		t.Fatalf("expected views>=1, got %d", vv.Stats.Views)
	}

	// progress updates progSec
	_, _ = svc.CreateInteraction(ctx, user, CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeViewProgress, Meta: map[string]interface{}{"progSec": 33}})
	st, _ = stateRepo.FindByUserAndVideo(ctx, svc.(*service).generateObjectIDFromString(user), vid)
	if st.ProgressSeconds != 33 {
		t.Fatalf("expected progSec=33, got %d", st.ProgressSeconds)
	}

	// complete increments completions and may update progSec
	_, _ = svc.CreateInteraction(ctx, user, CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeViewComplete, Meta: map[string]interface{}{"progSec": 80}})
	vv, _ = videoRepo.FindByID(ctx, vid.Hex())
	if vv.Stats.Completions < 1 {
		t.Fatalf("expected completions>=1, got %d", vv.Stats.Completions)
	}
}

func TestService_CreateInteraction_VideoNotFound_Tolerated(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ctx := context.Background()

	svc := newServiceForTest(ts)
	stateRepo := state.NewRepository()

	vid := primitive.NewObjectID() // not seeded
	user := "userB"

	_, err := svc.CreateInteraction(ctx, user, CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeViewStart})
	if err != nil {
		t.Fatalf("expected no error even if video missing, got %v", err)
	}
	st, err := stateRepo.FindByUserAndVideo(ctx, svc.(*service).generateObjectIDFromString(user), vid)
	if err != nil || st == nil {
		t.Fatalf("expected state created even if video missing")
	}
}

func TestService_ListInteractions(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	defer ts.CleanupDatabase()
	ctx := context.Background()

	svc := newServiceForTest(ts)

	vid := primitive.NewObjectID()
	user := "userC"
	// create some interactions (video missing is tolerated)
	_, _ = svc.CreateInteraction(ctx, user, CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeLike})
	_, _ = svc.CreateInteraction(ctx, user, CreateInteractionRequest{VideoID: vid.Hex(), Type: TypeFavorite})

	uis, err := svc.GetUserInteractions(ctx, user, 10)
	if err != nil || len(uis) < 2 {
		t.Fatalf("expected >=2 user interactions, got %d, err=%v", len(uis), err)
	}
	vis, err := svc.GetVideoInteractions(ctx, vid.Hex(), 10)
	if err != nil || len(vis) < 2 {
		t.Fatalf("expected >=2 video interactions, got %d, err=%v", len(vis), err)
	}
}
