package interaction

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// InteractionType 定义交互类型的枚举
type InteractionType string

const (
	// 观看相关事件
	TypeViewStart    InteractionType = "view_start"
	TypeViewProgress InteractionType = "view_progress"
	TypeViewComplete InteractionType = "view_complete"

	// 用户行为事件
	TypeLike       InteractionType = "like"
	TypeUnlike     InteractionType = "unlike"
	TypeFavorite   InteractionType = "favorite"
	TypeUnfavorite InteractionType = "unfavorite"
	TypeShare      InteractionType = "share"

	// 其他事件
	TypeClickListingLink InteractionType = "click_listing_link"
	TypeBlockVideo       InteractionType = "block_video"
)

// Interaction 视频交互事件日志模型
type Interaction struct {
	ID        primitive.ObjectID     `bson:"_id,omitempty" json:"id,omitempty"`
	UID       primitive.ObjectID     `bson:"uid" json:"userId"`                    // 用户ID
	VID       primitive.ObjectID     `bson:"vid" json:"videoId"`                   // 视频ID
	Type      InteractionType        `bson:"tp" json:"type"`                       // 交互类型
	Meta      map[string]interface{} `bson:"meta,omitempty" json:"meta,omitempty"` // 元数据
	Timestamp time.Time              `bson:"_ts" json:"timestamp"`                 // 时间戳
}

// ViewProgressMeta view_progress 事件的元数据
type ViewProgressMeta struct {
	ProgressSeconds int `json:"progSec" bson:"progSec"` // 观看进度（秒）
}

// ViewCompleteMeta view_complete 事件的元数据
type ViewCompleteMeta struct {
	CompletionPercent    float64 `json:"compPct" bson:"compPct"`         // 完成百分比
	TotalDurationSeconds int     `json:"totalDurSec" bson:"totalDurSec"` // 总时长（秒）
}

// ShareMeta share 事件的元数据
type ShareMeta struct {
	Platform string `json:"plat" bson:"plat"` // 分享平台
}

// ClickListingLinkMeta click_listing_link 事件的元数据
type ClickListingLinkMeta struct {
	ListingID primitive.ObjectID `json:"lId" bson:"lId"` // 房源ID
}

// BlockVideoMeta block_video 事件的元数据
type BlockVideoMeta struct {
	Reason string `json:"rsn" bson:"rsn"` // 屏蔽原因
}

// CreateInteractionRequest 创建交互事件的请求
type CreateInteractionRequest struct {
	VideoID string                 `json:"videoId" binding:"required"`
	Type    InteractionType        `json:"type" binding:"required"`
	Meta    map[string]interface{} `json:"meta,omitempty"`
}

// InteractionResponse 交互事件的响应
type InteractionResponse struct {
	ID        primitive.ObjectID     `json:"id"`
	UserID    primitive.ObjectID     `json:"userId"`
	VideoID   primitive.ObjectID     `json:"videoId"`
	Type      InteractionType        `json:"type"`
	Meta      map[string]interface{} `json:"meta,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}
