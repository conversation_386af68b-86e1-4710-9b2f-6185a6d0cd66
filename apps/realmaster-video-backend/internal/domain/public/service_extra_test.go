package public

import (
	"testing"

	"realmaster-video-backend/internal/config"
)

func TestService_makeAbsoluteAvatarURL(t *testing.T) {
	s := &service{cfg: &config.Config{}}
	s.cfg.Media.ServerURL = "https://cdn.example.com"

	// empty
	if got := s.makeAbsoluteAvatarURL(""); got != "" {
		t.Fatalf("expected empty, got %s", got)
	}
	// already absolute http
	if got := s.makeAbsoluteAvatarURL("http://a/b"); got != "http://a/b" {
		t.Fatalf("expected passthrough http, got %s", got)
	}
	// already absolute https
	if got := s.makeAbsoluteAvatarURL("https://a/b"); got != "https://a/b" {
		t.Fatalf("expected passthrough https, got %s", got)
	}
	// base empty returns original
	s2 := &service{cfg: &config.Config{}}
	if got := s2.makeAbsoluteAvatarURL("/x/y"); got != "/x/y" {
		t.Fatalf("expected original when base empty, got %s", got)
	}
	// join with slash handling
	if got := s.makeAbsoluteAvatarURL("/x/y"); got != "https://cdn.example.com/x/y" {
		t.Fatalf("expected joined with single slash, got %s", got)
	}
	s3 := &service{cfg: &config.Config{}}
	s3.cfg.Media.ServerURL = "https://cdn.example.com/"
	if got := s3.makeAbsoluteAvatarURL("x/y"); got != "https://cdn.example.com/x/y" {
		t.Fatalf("expected joined with single slash when base has trailing slash, got %s", got)
	}
}
