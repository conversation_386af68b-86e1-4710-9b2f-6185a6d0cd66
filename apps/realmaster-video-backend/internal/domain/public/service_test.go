package public

import (
	"context"
	"strings"
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/domain/advertiser"
	"realmaster-video-backend/internal/domain/state"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/testutil"
)

// seedAdvertiser creates a minimal advertiser
func seedAdvertiser(t *testing.T, repo advertiser.Repository, name, avatarKey, phone, email string) primitive.ObjectID {
	t.Helper()
	adv := advertiser.Advertiser{
		ID:        primitive.NewObjectID(),
		Name:      name,
		AvatarURL: avatarKey, // stored as key like "100/55048/a.jpg"
		Phone:     phone,
		Email:     email,
	}
	ctx := context.Background()
	if err := repo.Create(ctx, &adv); err != nil {
		t.Fatalf("seed advertiser failed: %v", err)
	}
	return adv.ID
}

// seedVideo creates a minimal published video
func seedVideo(t *testing.T, repo video.Repository, titleZh string, clientID primitive.ObjectID) primitive.ObjectID {
	t.Helper()
	v := video.Video{
		ID:         primitive.NewObjectID(),
		Title:      video.MultilingualString{Zh: titleZh},
		Status:     video.StatusPublished,
		Duration:   10,
		UploaderID: "dev_admin_001",
		ClientID:   clientID,
		Stats:      video.VideoStats{},
	}
	now := time.Now()
	v.PublishedAt = &now
	ctx := context.Background()
	if err := repo.Create(ctx, &v); err != nil {
		t.Fatalf("seed video failed: %v", err)
	}
	return v.ID
}

func TestService_GetFeed_ClientInjected(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	// 清空数据库，确保分页第一页能拿到本测试数据
	ts.CleanupDatabase()
	defer ts.CleanupDatabase()
	ctx := context.Background()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pub := NewService(videoRepo, stateRepo, ts.Config)

	advRepo := advertiser.NewRepository()
	if setter, ok := any(pub).(interface{ SetAdvertiserRepo(advertiser.Repository) }); ok {
		setter.SetAdvertiserRepo(advRepo)
	}

	// seed: advertiser A with avatar key, video v1 with client=A, v2 without client
	advID := seedAdvertiser(t, advRepo, "Client A", "100/55048/a.jpg", "1234567890", "<EMAIL>")
	seededID := seedVideo(t, videoRepo, "有客户的视频", advID)
	_ = seedVideo(t, videoRepo, "无客户的视频", primitive.NilObjectID)

	resp, err := pub.GetFeed(ctx, "dev_admin_001", 1, 10)
	if err != nil {
		t.Fatalf("GetFeed failed: %v", err)
	}
	if len(resp.Videos) == 0 {
		t.Fatalf("expected videos > 0")
	}

	// assert the seeded video has client injected
	var match *video.VideoResponse
	for i := range resp.Videos {
		if resp.Videos[i].ID == seededID {
			match = &resp.Videos[i]
			break
		}
	}
	if match == nil {
		t.Fatalf("seeded video not found in feed")
	}
	if match.Client == nil {
		t.Fatalf("expected client injected, got nil")
	}
	if match.Client.Name == "" {
		t.Errorf("expected client name populated")
	}
	if match.Client.AvatarURL == "" || !strings.Contains(match.Client.AvatarURL, "/media/avatars/") {
		t.Errorf("expected avatar url contains /media/avatars/, got %s", match.Client.AvatarURL)
	}
}

func TestService_GetFeed_NoAdvertiserRepo_NoInjection(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	ts.CleanupDatabase()
	defer ts.CleanupDatabase()
	ctx := context.Background()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pub := NewService(videoRepo, stateRepo, ts.Config)

	// seed: one video with non-empty clientID
	clientID := primitive.NewObjectID()
	seededID := seedVideo(t, videoRepo, "有客户的视频但不注入", clientID)

	resp, err := pub.GetFeed(ctx, "dev_admin_001", 1, 10)
	if err != nil {
		t.Fatalf("GetFeed failed: %v", err)
	}
	var match *video.VideoResponse
	for i := range resp.Videos {
		if resp.Videos[i].ID == seededID {
			match = &resp.Videos[i]
			break
		}
	}
	if match == nil {
		t.Fatalf("seeded video not found in feed")
	}
	if match.Client != nil {
		t.Fatalf("expected client to be nil when advRepo not set")
	}
}

func TestService_GetUserStates_Basic(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	ts.CleanupDatabase()
	defer ts.CleanupDatabase()
	ctx := context.Background()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pub := NewService(videoRepo, stateRepo, ts.Config)

	// seed two videos and one state for user
	v1 := seedVideo(t, videoRepo, "v1", primitive.NilObjectID)
	v2 := seedVideo(t, videoRepo, "v2", primitive.NilObjectID)
	user := primitive.NewObjectID()
	// upsert one state for v1
	st := &state.State{ID: state.StateID{UserID: user, VideoID: v1}}
	if err := stateRepo.Upsert(ctx, st); err != nil {
		t.Fatalf("upsert state failed: %v", err)
	}

	states, err := pub.GetUserStates(ctx, user.Hex(), []string{v1.Hex(), v2.Hex(), "invalid"})
	if err != nil {
		t.Fatalf("GetUserStates failed: %v", err)
	}
	if len(states) != 1 {
		t.Fatalf("expected 1 state, got %d", len(states))
	}
	if states[0].VideoID != v1 {
		t.Errorf("expected state for v1")
	}
}

func TestService_GetUserFavorites_Basic(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	ts.CleanupDatabase()
	defer ts.CleanupDatabase()
	ctx := context.Background()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pub := NewService(videoRepo, stateRepo, ts.Config)

	// seed videos and favorites
	v1 := seedVideo(t, videoRepo, "f1", primitive.NilObjectID)
	v2 := seedVideo(t, videoRepo, "f2", primitive.NilObjectID)
	user := primitive.NewObjectID()
	// favorite v1 only
	st1 := &state.State{ID: state.StateID{UserID: user, VideoID: v1}, Favorited: true}
	if err := stateRepo.Upsert(ctx, st1); err != nil {
		t.Fatalf("upsert favorite v1 failed: %v", err)
	}
	// v2 not favorited
	st2 := &state.State{ID: state.StateID{UserID: user, VideoID: v2}, Favorited: false}
	if err := stateRepo.Upsert(ctx, st2); err != nil {
		t.Fatalf("upsert v2 failed: %v", err)
	}

	fav, err := pub.GetUserFavorites(ctx, user.Hex(), 1, 10)
	if err != nil {
		t.Fatalf("GetUserFavorites failed: %v", err)
	}
	if len(fav.Favorites) != 1 {
		t.Fatalf("expected 1 favorite, got %d", len(fav.Favorites))
	}
	if fav.Favorites[0].VideoID != v1 {
		t.Errorf("expected favorite video v1")
	}
}

func TestService_GetFeed_DefaultPagination_Empty(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	ts.CleanupDatabase()
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pub := NewService(videoRepo, stateRepo, ts.Config)

	resp, err := pub.GetFeed(context.Background(), "u", 0, 0)
	if err != nil {
		t.Fatalf("GetFeed failed: %v", err)
	}
	if len(resp.Videos) != 0 {
		t.Fatalf("expected empty videos, got %d", len(resp.Videos))
	}
	if resp.Pagination.CurrentPage != 1 {
		t.Errorf("expected currentPage=1, got %d", resp.Pagination.CurrentPage)
	}
	if resp.Pagination.Limit != 20 {
		t.Errorf("expected limit=20, got %d", resp.Pagination.Limit)
	}
	if resp.Pagination.TotalItems != 0 || resp.Pagination.TotalPages != 0 {
		t.Errorf("expected total=0, pages=0, got %d/%d", resp.Pagination.TotalItems, resp.Pagination.TotalPages)
	}
}

func TestService_GetFeed_ExcludeNonPublished(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	ts.CleanupDatabase()
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pub := NewService(videoRepo, stateRepo, ts.Config)

	// seed one published and one draft
	_ = seedVideo(t, videoRepo, "published", primitive.NilObjectID)
	vDraft := video.Video{ID: primitive.NewObjectID(), Title: video.MultilingualString{Zh: "draft"}, Status: video.StatusDraft, UploaderID: "u", Duration: 1}
	if err := videoRepo.Create(context.Background(), &vDraft); err != nil {
		t.Fatalf("seed draft failed: %v", err)
	}

	resp, err := pub.GetFeed(context.Background(), "u", 1, 10)
	if err != nil {
		t.Fatalf("GetFeed failed: %v", err)
	}
	for _, v := range resp.Videos {
		if v.Status != video.StatusPublished {
			t.Fatalf("unexpected non-published in feed: %s", v.Status)
		}
	}
}

func TestService_GetFeed_AdvMissing_NoClientInjected(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	ts.CleanupDatabase()
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pub := NewService(videoRepo, stateRepo, ts.Config)
	// inject adv repo but do not seed advertiser
	advRepo := advertiser.NewRepository()
	if setter, ok := any(pub).(interface{ SetAdvertiserRepo(advertiser.Repository) }); ok {
		setter.SetAdvertiserRepo(advRepo)
	}

	clientID := primitive.NewObjectID()
	seededID := seedVideo(t, videoRepo, "no adv exists", clientID)

	resp, err := pub.GetFeed(context.Background(), "u", 1, 10)
	if err != nil {
		t.Fatalf("GetFeed failed: %v", err)
	}
	var match *video.VideoResponse
	for i := range resp.Videos {
		if resp.Videos[i].ID == seededID {
			match = &resp.Videos[i]
			break
		}
	}
	if match == nil {
		t.Fatalf("seeded video not found")
	}
	if match.Client != nil {
		t.Fatalf("expected nil client when advertiser missing")
	}
}

func TestService_GetFeed_DuplicateClientIDs_BatchedInjection(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	ts.CleanupDatabase()
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pub := NewService(videoRepo, stateRepo, ts.Config)
	advRepo := advertiser.NewRepository()
	if setter, ok := any(pub).(interface{ SetAdvertiserRepo(advertiser.Repository) }); ok {
		setter.SetAdvertiserRepo(advRepo)
	}

	// seed one advertiser and two videos referencing it
	advID := seedAdvertiser(t, advRepo, "DupClient", "100/x/a.jpg", "", "")
	v1 := seedVideo(t, videoRepo, "v1", advID)
	v2 := seedVideo(t, videoRepo, "v2", advID)

	resp, err := pub.GetFeed(context.Background(), "u", 1, 10)
	if err != nil {
		t.Fatalf("GetFeed failed: %v", err)
	}
	var ok1, ok2 bool
	for i := range resp.Videos {
		if resp.Videos[i].ID == v1 {
			if resp.Videos[i].Client == nil {
				t.Fatalf("v1 client nil")
			} else {
				ok1 = true
			}
		}
		if resp.Videos[i].ID == v2 {
			if resp.Videos[i].Client == nil {
				t.Fatalf("v2 client nil")
			} else {
				ok2 = true
			}
		}
	}
	if !(ok1 && ok2) {
		t.Fatalf("seeded videos not both found: %v %v", ok1, ok2)
	}
}

func TestService_GetUserStates_EmptyIDs_ReturnsEmpty(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	ts.CleanupDatabase()
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pub := NewService(videoRepo, stateRepo, ts.Config)

	states, err := pub.GetUserStates(context.Background(), primitive.NewObjectID().Hex(), []string{})
	if err != nil {
		t.Fatalf("GetUserStates failed: %v", err)
	}
	if len(states) != 0 {
		t.Fatalf("expected 0 states, got %d", len(states))
	}
}

func TestService_GetUserStates_InvalidUser_ReturnsEmpty(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	ts.CleanupDatabase()
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pub := NewService(videoRepo, stateRepo, ts.Config)

	// seed a state for an objectID user
	vid := seedVideo(t, videoRepo, "v", primitive.NilObjectID)
	user := primitive.NewObjectID()
	_ = stateRepo.Upsert(context.Background(), &state.State{ID: state.StateID{UserID: user, VideoID: vid}})

	// query with string user that will hash to different objectID
	states, err := pub.GetUserStates(context.Background(), "invalid-user-str", []string{vid.Hex()})
	if err != nil {
		t.Fatalf("GetUserStates failed: %v", err)
	}
	if len(states) != 0 {
		t.Fatalf("expected 0 states, got %d", len(states))
	}
}

func TestService_GetUserFavorites_DefaultPagination_NoFavorites(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	ts.CleanupDatabase()
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pub := NewService(videoRepo, stateRepo, ts.Config)

	user := primitive.NewObjectID()
	fav, err := pub.GetUserFavorites(context.Background(), user.Hex(), 0, 0)
	if err != nil {
		t.Fatalf("GetUserFavorites failed: %v", err)
	}
	if fav.Pagination.CurrentPage != 1 || fav.Pagination.Limit != 20 {
		t.Errorf("expected page=1,limit=20, got %d/%d", fav.Pagination.CurrentPage, fav.Pagination.Limit)
	}
	if len(fav.Favorites) != 0 {
		t.Fatalf("expected 0 favorites, got %d", len(fav.Favorites))
	}
}

func TestService_GetUserFavorites_Pagination(t *testing.T) {
	ts := testutil.SetupTestSuite(t)
	ts.CleanupDatabase()
	defer ts.CleanupDatabase()

	videoRepo := video.NewRepository()
	stateRepo := state.NewRepository()
	pub := NewService(videoRepo, stateRepo, ts.Config)

	user := primitive.NewObjectID()
	// seed 25 favorites
	var vids []primitive.ObjectID
	for i := 0; i < 25; i++ {
		vids = append(vids, seedVideo(t, videoRepo, "fv", primitive.NilObjectID))
	}
	for _, id := range vids {
		_ = stateRepo.Upsert(context.Background(), &state.State{ID: state.StateID{UserID: user, VideoID: id}, Favorited: true})
	}

	fav, err := pub.GetUserFavorites(context.Background(), user.Hex(), 2, 20)
	if err != nil {
		t.Fatalf("GetUserFavorites failed: %v", err)
	}
	if len(fav.Favorites) != 5 {
		t.Fatalf("expected 5 favorites on page 2, got %d", len(fav.Favorites))
	}
	if fav.Pagination.TotalItems < 25 || fav.Pagination.TotalPages < 2 || fav.Pagination.CurrentPage != 2 {
		t.Errorf("unexpected pagination: %+v", fav.Pagination)
	}
}
