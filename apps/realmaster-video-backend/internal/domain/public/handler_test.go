package public

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"

	"realmaster-video-backend/internal/domain/state"
	"realmaster-video-backend/internal/domain/video"
)

// fake service implements Service
type fakeService struct {
	feedResp *FeedResponse
	states   []state.StateResponse
	favs     *UserFavoritesResponse
	err      error
}

func (f *fakeService) GetFeed(ctx context.Context, userID string, page, limit int) (*FeedResponse, error) {
	return f.feedResp, f.err
}
func (f *fakeService) GetUserStates(ctx context.Context, userID string, videoIDs []string) ([]state.StateResponse, error) {
	return f.states, f.err
}
func (f *fakeService) GetUserFavorites(ctx context.Context, userID string, page, limit int) (*UserFavoritesResponse, error) {
	return f.favs, f.err
}

var _ Service = (*fakeService)(nil)

// reuse the same mock pattern as interaction tests
func mockJWT(userID string) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("authValid", true)
		c.Set("userID", userID)
		c.Next()
	}
}

func TestPublicHandler_GetFeed_Success_And_Unauthorized(t *testing.T) {
	gin.SetMode(gin.TestMode)
	f := &fakeService{feedResp: &FeedResponse{Videos: []video.VideoResponse{}}}
	h := NewHandler(f)
	r := gin.New()
	r.GET("/feed", h.GetFeed)

	// unauthorized: no auth header
	w := httptest.NewRecorder()
	r.ServeHTTP(w, httptest.NewRequest(http.MethodGet, "/feed", nil))
	if w.Code != http.StatusUnauthorized {
		t.Fatalf("expected 401, got %d", w.Code)
	}

	// success
	w2 := httptest.NewRecorder()
	r2 := gin.New()
	r2.Use(mockJWT("dev_admin_001"))
	r2.GET("/feed", h.GetFeed)
	r2.ServeHTTP(w2, httptest.NewRequest(http.MethodGet, "/feed?page=1&limit=10", nil))
	if w2.Code != http.StatusOK {
		t.Fatalf("expected 200, got %d", w2.Code)
	}
}

func TestPublicHandler_GetUserStates_Post_BadBody_Forbidden_And_Success(t *testing.T) {
	gin.SetMode(gin.TestMode)
	f := &fakeService{states: []state.StateResponse{}}
	h := NewHandler(f)
	r := gin.New()
	r.POST("/states/batch", h.GetUserStates)

	// mount an authed router for subsequent requests
	rAuthed := gin.New()
	rAuthed.Use(mockJWT("dev_admin_001"))
	rAuthed.POST("/states/batch", h.GetUserStates)

	// unauthorized
	w0 := httptest.NewRecorder()
	r.ServeHTTP(w0, httptest.NewRequest(http.MethodPost, "/states/batch", bytes.NewBufferString("{}")))
	if w0.Code != http.StatusUnauthorized {
		t.Fatalf("expected 401, got %d", w0.Code)
	}

	// bad body
	w := httptest.NewRecorder()
	req := httptest.NewRequest(http.MethodPost, "/states/batch", bytes.NewBufferString("{"))
	req.Header.Set("Content-Type", "application/json")
	rAuthed.ServeHTTP(w, req)
	if w.Code != http.StatusBadRequest {
		t.Fatalf("expected 400, got %d", w.Code)
	}

	// forbidden (userId mismatch)
	payload := map[string]any{"userId": "u1", "videoIds": []string{"v1"}}
	b, _ := json.Marshal(payload)
	w2 := httptest.NewRecorder()
	req2 := httptest.NewRequest(http.MethodPost, "/states/batch", bytes.NewBuffer(b))
	req2.Header.Set("Content-Type", "application/json")
	rAuthed.ServeHTTP(w2, req2)
	if w2.Code != http.StatusForbidden {
		t.Fatalf("expected 403, got %d", w2.Code)
	}

	// success (userId matches, non-empty ids)
	payload2 := map[string]any{"userId": "dev_admin_001", "videoIds": []string{"000000000000000000000000"}}
	b2, _ := json.Marshal(payload2)
	w3 := httptest.NewRecorder()
	req3 := httptest.NewRequest(http.MethodPost, "/states/batch", bytes.NewBuffer(b2))
	req3.Header.Set("Content-Type", "application/json")
	rAuthed.ServeHTTP(w3, req3)
	if w3.Code != http.StatusOK {
		t.Fatalf("expected 200, got %d, body=%s", w3.Code, w3.Body.String())
	}
}

func TestPublicHandler_GetUserStatesQuery_EmptyIDs_TooMany_And_Success(t *testing.T) {
	gin.SetMode(gin.TestMode)
	f := &fakeService{states: []state.StateResponse{}}
	h := NewHandler(f)
	r := gin.New()
	r.GET("/states", h.GetUserStatesQuery)

	rAuthed := gin.New()
	rAuthed.Use(mockJWT("dev_admin_001"))
	rAuthed.GET("/states", h.GetUserStatesQuery)

	// unauthorized
	w0 := httptest.NewRecorder()
	r.ServeHTTP(w0, httptest.NewRequest(http.MethodGet, "/states", nil))
	if w0.Code != http.StatusUnauthorized {
		t.Fatalf("expected 401, got %d", w0.Code)
	}

	// empty
	w := httptest.NewRecorder()
	req := httptest.NewRequest(http.MethodGet, "/states?videoIds=", nil)
	rAuthed.ServeHTTP(w, req)
	if w.Code != http.StatusBadRequest {
		t.Fatalf("expected 400, got %d", w.Code)
	}

	// too many
	ids := make([]string, 101)
	for i := range ids {
		ids[i] = "000000000000000000000000"
	}
	w2 := httptest.NewRecorder()
	req2 := httptest.NewRequest(http.MethodGet, "/states?videoIds="+strings.Join(ids, ","), nil)
	rAuthed.ServeHTTP(w2, req2)
	if w2.Code != http.StatusBadRequest {
		t.Fatalf("expected 400, got %d", w2.Code)
	}

	// success
	w3 := httptest.NewRecorder()
	req3 := httptest.NewRequest(http.MethodGet, "/states?videoIds=000000000000000000000000", nil)
	rAuthed.ServeHTTP(w3, req3)
	if w3.Code != http.StatusOK {
		t.Fatalf("expected 200, got %d", w3.Code)
	}
}

func TestPublicHandler_GetUserFavorites_Success_And_Error(t *testing.T) {
	gin.SetMode(gin.TestMode)
	okResp := &UserFavoritesResponse{Favorites: []state.FavoriteVideoResponse{}}
	f := &fakeService{favs: okResp}
	h := NewHandler(f)
	r := gin.New()
	r.GET("/favorites", h.GetUserFavorites)

	rAuthed := gin.New()
	rAuthed.Use(mockJWT("dev_admin_001"))
	rAuthed.GET("/favorites", h.GetUserFavorites)

	// unauthorized
	w0 := httptest.NewRecorder()
	r.ServeHTTP(w0, httptest.NewRequest(http.MethodGet, "/favorites", nil))
	if w0.Code != http.StatusUnauthorized {
		t.Fatalf("expected 401, got %d", w0.Code)
	}

	// success
	w := httptest.NewRecorder()
	req := httptest.NewRequest(http.MethodGet, "/favorites?page=1&limit=10", nil)
	rAuthed.ServeHTTP(w, req)
	if w.Code != http.StatusOK {
		t.Fatalf("expected 200, got %d", w.Code)
	}

	// error path
	f.err = errors.New("boom")
	w2 := httptest.NewRecorder()
	req2 := httptest.NewRequest(http.MethodGet, "/favorites", nil)
	rAuthed.ServeHTTP(w2, req2)
	if w2.Code != http.StatusInternalServerError {
		t.Fatalf("expected 500, got %d", w2.Code)
	}
}
