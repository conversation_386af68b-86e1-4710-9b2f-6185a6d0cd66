# RealMaster 开发环境 Nginx配置 - 高性能视频静态文件服务（规范化 + 扁平化）

user kevin;
worker_processes auto;
pid /tmp/nginx.pid;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 传输/缓存/IO 优化
    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;
    keepalive_timeout 65;
    keepalive_requests 1000;

    aio        on;
    directio   4m;
    output_buffers 1 512k;

    # 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/vnd.apple.mpegurl;

    # 日志（开发环境：关闭访问日志）
    log_format minimal '$remote_addr - $status $body_bytes_sent "$request" $request_time';
    access_log off;

    client_max_body_size 2048m;

    # 临时目录
    client_body_temp_path /tmp/nginx_client_temp;
    proxy_temp_path       /tmp/nginx_proxy_temp;
    fastcgi_temp_path     /tmp/nginx_fastcgi_temp;
    uwsgi_temp_path       /tmp/nginx_uwsgi_temp;
    scgi_temp_path        /tmp/nginx_scgi_temp;

    server {
        listen 3000;
        server_name localhost;

        # 全局 CORS（注意：下层 location 使用 add_header 时会覆盖，因此各 location 会重复设置）
        add_header Access-Control-Allow-Origin  "*" always;
        add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
        add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;

        # 健康检查
        location = /health {
            default_type text/plain;
            add_header Cache-Control "no-cache";
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            return 200 "healthy\n";
        }

        # 统一处理末尾斜杠：/xxx/file.ext/ -> /xxx/file.ext
        location ~* ^/(media|draft)/(thumbnails|avatars|videos)/(.*\.(jpg|jpeg|png|webp|gif|mp4|mov|m4v|m3u8|ts))/$ {
            return 301 /$1/$2;
        }

        # ===================== 草稿视频 =====================
        # MP4/MOV/M4V（1小时缓存）
        location ~ ^/draft/videos/(.*\.(mp4|mov|m4v))$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/draft/rm_video_drafts/$1;
            add_header Accept-Ranges bytes;
            expires 1h;
            add_header Cache-Control "public, max-age=3600";
        }
        # HLS m3u8（不缓存）
        location ~ ^/draft/videos/(.*\.m3u8)$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/draft/rm_video_drafts/$1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma no-cache;
            add_header Expires 0;
        }
        # HLS ts（1小时缓存）
        location ~ ^/draft/videos/(.*\.ts)$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/draft/rm_video_drafts/$1;
            add_header Accept-Ranges bytes;
            expires 1h;
            add_header Cache-Control "public, max-age=3600";
        }
        # 其他草稿视频静态（兜底）
        location ~ ^/draft/videos/(.*)$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/draft/rm_video_drafts/$1;
            add_header Accept-Ranges bytes;
            expires 1h;
            add_header Cache-Control "public, max-age=3600";
        }

        # ===================== 最终视频 =====================
        # MP4/MOV/M4V（1天缓存）
        location ~ ^/media/videos/(.*\.(mp4|mov|m4v))$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/media/rm_videos/$1;
            add_header Accept-Ranges bytes;
            expires 1d;
            add_header Cache-Control "public, max-age=86400, immutable";
        }
        # HLS m3u8（不缓存）
        location ~ ^/media/videos/(.*\.m3u8)$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/media/rm_videos/$1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma no-cache;
            add_header Expires 0;
        }
        # HLS ts（30天缓存）
        location ~ ^/media/videos/(.*\.ts)$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/media/rm_videos/$1;
            add_header Accept-Ranges bytes;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000, immutable";
        }
        # 其他最终视频静态（兜底）
        location ~ ^/media/videos/(.*)$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/media/rm_videos/$1;
            add_header Accept-Ranges bytes;
            expires 1d;
            add_header Cache-Control "public, max-age=86400, immutable";
        }

        # ===================== 草稿缩略图 =====================
        location ~ ^/draft/thumbnails/(.*\.(jpg|jpeg|png|webp|gif))$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/draft/rm_thumbnail_drafts/$1;
            add_header Vary "Accept-Encoding";
            expires 1h;
            add_header Cache-Control "public, max-age=3600";
        }
        location ~ ^/draft/thumbnails/(.*)$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/draft/rm_thumbnail_drafts/$1;
            expires 1h;
            add_header Cache-Control "public, max-age=3600";
        }

        # ===================== 最终缩略图 =====================
        location ~ ^/media/thumbnails/(.*\.(jpg|jpeg|png|webp|gif))$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/media/rm_thumbnails/$1;
            add_header Vary "Accept-Encoding";
            expires 30d;
            add_header Cache-Control "public, max-age=2592000, immutable";
        }
        location ~ ^/media/thumbnails/(.*)$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/media/rm_thumbnails/$1;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000, immutable";
        }

        # ===================== 头像 =====================
        location ~ ^/media/avatars/(.*\.(jpg|jpeg|png|webp|gif))$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/media/rm_avatars/$1;
            add_header Vary "Accept-Encoding";
            expires 30d;
            add_header Cache-Control "public, max-age=2592000, immutable";
        }
        location ~ ^/media/avatars/(.*)$ {
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin  "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
                add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            add_header Access-Control-Allow-Origin  "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, Range, Content-Type, Accept-Encoding" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            alias /var/www/media/rm_avatars/$1;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000, immutable";
        }

        # 未匹配的直接 404
        location / {
            return 404;
        }
    }
}
