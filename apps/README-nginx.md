# RealMaster 高性能视频静态文件服务

## 🎯 目的

基于你的实际goupload配置，提供**高性能**静态文件服务让前端访问视频和图片。专门针对视频播放场景进行了深度优化，支持MP4快速seek、HLS流媒体、智能缓存等特性。

## 📁 文件说明

- `nginx.conf` - 高性能nginx配置（基于config.toml）
- `start-nginx.sh` - 智能启动脚本（包含性能检查）
- `nginx-performance-guide.md` - 详细性能优化指南
- `README-nginx.md` - 本说明文件

## 🚀 性能特性

- **4倍并发能力**: 4096连接 vs 1024连接
- **MP4快速seek**: 支持视频拖拽播放
- **HLS流优化**: m3u8/ts文件专门优化
- **智能缓存**: 图片30天，视频1天，HLS无缓存
- **异步I/O**: 大文件直接磁盘I/O，减少内存占用
- **零拷贝传输**: sendfile优化网络性能
- **自动压缩**: gzip压缩文本文件

## 🚀 快速启动

直接运行启动脚本即可：

```bash
./start-nginx.sh
```

脚本会自动：
- 检查nginx安装和模块支持
- 创建所需的存储目录
- 设置正确的权限
- 系统性能检查（文件描述符、TCP缓冲区等）
- 测试nginx配置文件
- 启动高性能nginx服务

## 🗂️ 存储目录结构

基于你的 `config.toml` 配置：

### 草稿阶段 (用户上传的原始文件)
```
/var/www/draft/rm_video_drafts/     # 视频草稿
/var/www/draft/rm_thumbnail_drafts/ # 缩略图草稿
```

### 最终阶段 (Worker处理后的文件)
```
/var/www/media/rm_videos/      # 最终视频文件 (包含HLS流)
/var/www/media/rm_thumbnails/  # 最终缩略图
/var/www/media/rm_avatars/     # 客户头像
```

## 🌐 URL路径映射

### 草稿文件访问
```
http://localhost:3000/draft/videos/USER/2025-28/abc123/video.mp4
http://localhost:3000/draft/thumbnails/USER/2025-28/abc123/thumb.jpg
```

### 最终文件访问  
```
http://localhost:3000/media/videos/USER/2025-28/abc123/video.m3u8
http://localhost:3000/media/videos/USER/2025-28/abc123/segment001.ts
http://localhost:3000/media/thumbnails/USER/2025-28/abc123/thumb.jpg
http://localhost:3000/media/avatars/USER/2025-28/abc123/avatar.jpg
```

## 🔧 配置详解

### 端口配置
- **nginx静态文件服务**: 端口 `3000`
- **Go后端API**: 端口 `8080` (config.toml中配置)

### 高性能特性
- **MP4模块**: 启用快速seek，支持 `?start=60` 参数
- **HLS流优化**: m3u8无缓存，ts分片30天缓存
- **异步I/O**: 大文件(>4MB)使用直接磁盘I/O
- **智能缓存**: 图片30天，视频1天，动态内容无缓存
- **Range请求**: 完整支持HTTP Range，视频拖拽播放
- **跨域访问**: 完整的CORS支持，包含预检缓存
- **压缩优化**: gzip压缩m3u8等文本文件

### goupload集成
配置完全基于你的 `config.toml`：
- site: "TEST"
- entryName: video_draft, thumbnail_draft, video_final, thumbnail_final, client_avatar
- 对应的prefix和storage路径

## 🛠️ 前端配置建议

### M1播放器 (frontend_m1_player)
可以添加媒体文件基础URL：
```javascript
// src/config.js
export const API_BASE_URL = 'http://*************:3000/video/v1';
export const MEDIA_BASE_URL = 'http://localhost:3000/media/';  // 新增
```

### M2管理后台 (frontend_m2_uploader)  
API配置保持不变：
```javascript
export const API_BASE_URL = 'http://localhost:8080';
```

## 🐛 故障排除

### 1. 权限问题
```bash
# 如果遇到权限错误，运行：
sudo chmod -R 755 /var/www/draft/
sudo chmod -R 755 /var/www/media/
```

### 2. 端口被占用
```bash
sudo lsof -i :3000
sudo kill -9 <PID>
```

### 3. MP4模块未启用
```bash
# 检查nginx模块
nginx -V 2>&1 | grep http_mp4_module

# 如果没有，安装包含mp4模块的nginx
sudo apt install nginx-extras  # Ubuntu
brew install nginx-full        # macOS
```

### 4. 性能问题
```bash
# 检查文件描述符限制
ulimit -n
# 如果小于4096，设置：
ulimit -n 4096

# 性能测试
ab -n 1000 -c 100 http://localhost:3000/media/videos/test.mp4
```

### 5. 目录不存在
启动脚本会自动创建，如果手动创建：
```bash
sudo mkdir -p /var/www/draft/rm_video_drafts
sudo mkdir -p /var/www/draft/rm_thumbnail_drafts
sudo mkdir -p /var/www/media/rm_videos
sudo mkdir -p /var/www/media/rm_thumbnails
sudo mkdir -p /var/www/media/rm_avatars
```

## 📝 重要说明

1. **高性能优化**: 针对视频播放场景深度优化，4倍并发能力提升
2. **完全基于你的配置**: nginx配置直接对应config.toml中的路径
3. **智能缓存策略**: 不同文件类型使用不同缓存时间
4. **开发环境专用**: 生产环境需要更严格的安全配置
5. **API保持不变**: Go后端继续在8080端口，前端API调用无需修改
6. **自动性能检查**: 启动脚本会检查系统性能配置

## 📊 性能监控

### 健康检查
```bash
curl http://localhost:3000/health
```

### 性能测试
```bash
# 并发测试
ab -n 1000 -c 100 http://localhost:3000/media/videos/test.mp4

# HLS流测试
curl -I http://localhost:3000/media/videos/stream.m3u8
```

### 缓存验证
```bash
# 检查缓存头
curl -I http://localhost:3000/media/thumbnails/test.jpg
# 应该看到: Cache-Control: public, max-age=2592000, immutable
```

## 🔄 停止服务

按 `Ctrl+C` 停止，或：
```bash
sudo pkill -f "nginx.*nginx.conf"
```
