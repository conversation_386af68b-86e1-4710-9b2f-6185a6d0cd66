import { renderView } from '../viewRenderer';
import { getCategoryList, getClientList } from '../services/apiClient';
import { navigateTo } from '../router';
import flatpickr from 'flatpickr';
import 'flatpickr/dist/flatpickr.min.css';

// In-memory state for filters. This will now persist across navigations.
let filterState = {
    categoryId: null,
    clientId: null,
    status: [],
    startDate: null,
    endDate: null,
};

// Store flatpickr instances to access them later
let fpStartDate, fpEndDate;

// --- HELPER FUNCTIONS ---

const setupFilterGroups = () => {
    const filterGroups = document.querySelectorAll('.filter-group');
    filterGroups.forEach(group => {
        const header = group.querySelector('.filter-select-box');
        if (header) {
            header.addEventListener('click', (e) => {
                e.stopPropagation();
                
                // Close other open groups
                filterGroups.forEach(otherGroup => {
                    if (otherGroup !== group) {
                        otherGroup.classList.remove('open');
                    }
                });
                // Toggle current group
                group.classList.toggle('open');
            });
        }
    });

    // This global listener is now managed in the main render function to avoid memory leaks.
};

const populateList = async (listId, apiFunction, inputType, stateKey) => {
    const listElement = document.getElementById(listId);
    if (!listElement) {return;}

    listElement.innerHTML = '<div class="loading-indicator">Loading...</div>';
    try {
        const { items } = await apiFunction({ limit: 1000 });
        listElement.innerHTML = '';

        const currentValue = filterState[stateKey];

        // Add "All" option
        const allLi = document.createElement('li');
        const isAllChecked = currentValue === null || currentValue === '';
        allLi.innerHTML = `<label><input type="${inputType}" name="${stateKey}" value="" ${isAllChecked ? 'checked' : ''}> All</label>`;
        listElement.appendChild(allLi);
        
        // For Client, also add a "None" option
        if (listId === 'client-options-list') {
            const noneLi = document.createElement('li');
            const isNoneChecked = currentValue === 'none';
            noneLi.innerHTML = `<label><input type="${inputType}" name="${stateKey}" value="none" ${isNoneChecked ? 'checked' : ''}> None</label>`;
            listElement.appendChild(noneLi);
        }

        items.forEach(item => {
            const li = document.createElement('li');
            const isItemChecked = currentValue === item.id;
            li.innerHTML = `<label><input type="${inputType}" name="${stateKey}" value="${item.id}" ${isItemChecked ? 'checked' : ''}> ${item.name}</label>`;
            listElement.appendChild(li);
        });
    } catch (error) {
        listElement.innerHTML = '<li>Error loading data.</li>';
        console.error(`Failed to populate ${listId}:`, error);
    }
};

const updateSelectedDisplay = (groupId, selectedText) => {
    const group = document.getElementById(groupId);
    if (group) {
        const display = group.querySelector('.selected-value-display');
        display.textContent = selectedText;
    }
};

const resetFilters = () => {
    // 1. Reset state object
    filterState = {
        categoryId: null,
        clientId: null,
        status: [],
        startDate: null,
        endDate: null,
    };

    // 2. Reset UI elements
    // Category
    const categoryAll = document.querySelector('#category-options-list input[value=""]');
    if (categoryAll) {categoryAll.checked = true;}
    updateSelectedDisplay('category-filter-group', 'All');

    // Client
    const clientAll = document.querySelector('#client-options-list input[value=""]');
    if (clientAll) {clientAll.checked = true;}
    updateSelectedDisplay('client-filter-group', 'All');

    // Status
    const statusCheckboxes = document.querySelectorAll('#status-options-list input[type="checkbox"]');
    statusCheckboxes.forEach(cb => {
        cb.checked = cb.value === ""; // Check "All", uncheck others
    });
    updateSelectedDisplay('status-filter-group', 'All');

    // Date pickers
    if (fpStartDate) {fpStartDate.clear();}
    if (fpEndDate) {fpEndDate.clear();}
};


// --- MAIN RENDER FUNCTION ---

// Flag to prevent re-attaching global listeners
let isGlobalListenerAttached = false;

export const renderFilter = async () => {
    try {
        await renderView('filter', '#app-container');

        // Populate lists, which will now respect the existing filterState
        await populateList('category-options-list', getCategoryList, 'radio', 'categoryId');
        await populateList('client-options-list', getClientList, 'radio', 'clientId');

        // --- SYNCHRONIZE UI WITH STATE ---
        // This block ensures the UI reflects the persisted state when re-entering the page.

        // 1. Sync Status checkboxes
        const statusCheckboxes = document.querySelectorAll('#status-options-list input[type="checkbox"]');
        const allStatusCheckbox = document.querySelector('#status-options-list input[value=""]');
        if (filterState.status.length > 0) {
            allStatusCheckbox.checked = false;
            statusCheckboxes.forEach(cb => {
                cb.checked = filterState.status.includes(cb.value);
            });
        } else {
            allStatusCheckbox.checked = true;
        }

        // 2. Sync display texts for all dropdowns
        const selectedCategory = document.querySelector('#category-options-list input:checked')?.parentElement.textContent.trim() || 'All';
        const selectedClient = document.querySelector('#client-options-list input:checked')?.parentElement.textContent.trim() || 'All';
        const statusDisplay = filterState.status.length > 0 ? `${filterState.status.length} selected` : 'All';
        updateSelectedDisplay('category-filter-group', selectedCategory);
        updateSelectedDisplay('client-filter-group', selectedClient);
        updateSelectedDisplay('status-filter-group', statusDisplay);
        
        // --- SETUP LISTENERS AND CONTROLS ---

        setupFilterGroups();

        // Global click listener to close dropdowns
        if (!isGlobalListenerAttached) {
            document.addEventListener('click', (event) => {
                const openGroup = document.querySelector('.filter-group.open');
                if (openGroup && !openGroup.contains(event.target) && !event.target.closest('.filter-select-box')) {
                    openGroup.classList.remove('open');
                }
            });
            isGlobalListenerAttached = true;
        }
        
        // Other listeners (radio, checkbox, datepickers, buttons) remain largely the same,
        // but are now set up after the UI is synced with the state.
        
        const filterContainer = document.querySelector('.filter-options-container');
        filterContainer.addEventListener('change', (event) => {
            const input = event.target;
            if (input.type === 'radio') {
                const stateKey = input.name; // e.g., 'categoryId' or 'clientId'
                const groupName = stateKey.replace('Id', ''); // e.g., 'category' or 'client'
                
                filterState[stateKey] = input.value;
                const selectedLabel = input.parentElement.textContent.trim();
                updateSelectedDisplay(`${groupName}-filter-group`, selectedLabel);
            }
        });
        
        const statusList = document.getElementById('status-options-list');
        statusList.addEventListener('change', (event) => {
            const checkbox = event.target;
            if (checkbox.type === 'checkbox') {
                const allCheckbox = statusList.querySelector('input[value=""]');
                const otherCheckboxes = Array.from(statusList.querySelectorAll('input:not([value=""])'));

                if (checkbox === allCheckbox) {
                    // If "All" is checked, uncheck others
                    if(allCheckbox.checked) {
                        otherCheckboxes.forEach(cb => cb.checked = false);
                    }
                } else {
                    // If another box is checked, uncheck "All"
                    if(checkbox.checked) {
                        allCheckbox.checked = false;
                    }
                }
                
                const selectedStatuses = otherCheckboxes.filter(cb => cb.checked).map(cb => cb.value);
                filterState.status = selectedStatuses;

                const display = selectedStatuses.length > 0 ? `${selectedStatuses.length} selected` : 'All';
                updateSelectedDisplay('status-filter-group', display);
            }
        });

        // Initialize date pickers, now with defaultDate from state
        fpEndDate = flatpickr("#end-date-wrapper", {
            wrap: true, clickOpens: false, dateFormat: "Y-m-d", defaultDate: filterState.endDate,
            onOpen: function(selectedDates, dateStr, instance) {
                instance.element.querySelector('.arrow-icon').classList.add('is-rotated');
            },
            onClose: function(selectedDates, dateStr, instance) {
                instance.element.querySelector('.arrow-icon').classList.remove('is-rotated');
            },
            onChange: function(selectedDates) { filterState.endDate = selectedDates[0]; },
        });

        fpStartDate = flatpickr("#start-date-wrapper", {
            wrap: true, clickOpens: false, dateFormat: "Y-m-d", defaultDate: filterState.startDate,
            onOpen: function(selectedDates, dateStr, instance) {
                instance.element.querySelector('.arrow-icon').classList.add('is-rotated');
            },
            onClose: function(selectedDates, dateStr, instance) {
                instance.element.querySelector('.arrow-icon').classList.remove('is-rotated');
            },
            onChange: function(selectedDates) {
                filterState.startDate = selectedDates[0];
                if (fpEndDate.selectedDates[0] && selectedDates[0] > fpEndDate.selectedDates[0]) { fpEndDate.clear(); }
                fpEndDate.set('minDate', selectedDates[0]);
            },
        });

        // Footer buttons
        document.getElementById('clear-filters-btn').addEventListener('click', resetFilters);
        document.getElementById('apply-filters-btn').addEventListener('click', () => {
            const params = new URLSearchParams();
            
            // Handle Category and Client
            if (filterState.categoryId) { // Will be "" for All, or an ID
                params.append('categoryId', filterState.categoryId);
            }
            if (filterState.clientId) { // Will be "" for All, "none" for None, or an ID
                params.append('clientId', filterState.clientId);
            }

            // Handle date range
            if (filterState.startDate) {
                params.append('from', filterState.startDate.toISOString());
            }
            if (filterState.endDate) {
                 // Set time to end of day for the 'to' parameter
                const endOfDay = new Date(filterState.endDate);
                endOfDay.setHours(23, 59, 59, 999);
                params.append('to', endOfDay.toISOString());
            }

            filterState.status.forEach(s => params.append('status', s));

            // 在基于hash的路由中，查询参数应该在hash内部
            navigateTo(`/dashboard?${params.toString()}`);
        });

    } catch (error) {
        console.error("Failed to render filter page:", error);
    }
}; 