import { renderView } from '../viewRenderer';
import { getCategoryList, getClientList, searchMlsProperties, createVideo, createVideoFromChunkedUpload, uploadThumbnail } from '../services/apiClient';
import { navigateTo, setPageCleanup } from '../router';
import { setupCustomDropdown } from '../components/customDropdown';
import { setupFileUploader } from '../components/fileUploader';
import { initClientModal, openClientModal } from '../components/clientModal';
import { businessConfig } from '../config';
import ErrorHandler, { ErrorTypes, ErrorSeverity, showSuccess, showWarning, showError, showInfo } from '../utils/errorHandler';
import { initTopProgress, showTopProgress, updateTopProgress, hideTopProgress } from '../components/topProgress';

const showPreviewModal = (url, fileType) => {
    const modal = document.getElementById('preview-modal');
    const previewContent = document.getElementById('preview-content');
    if (!modal || !previewContent) {return;}

    previewContent.innerHTML = '';

    let mediaElement;
    if (fileType === 'video') {
        mediaElement = document.createElement('video');
        mediaElement.controls = true;
        mediaElement.autoplay = true;
    } else {
        mediaElement = document.createElement('img');
    }
    mediaElement.src = url;

    previewContent.appendChild(mediaElement);
    modal.classList.remove('hidden');
};

const setupModalControls = () => {
    const modal = document.getElementById('preview-modal');
    const closeBtn = document.getElementById('close-preview-btn');
    const previewContent = document.getElementById('preview-content');

    if (!modal || !closeBtn || !previewContent) {return;}

    const closeModal = () => {
        modal.classList.add('hidden');
        previewContent.innerHTML = '';
    };

    closeBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (event) => {
        if (event.target === modal) {
            closeModal();
        }
    });
};

// --- Translation Helper ---
const setupTranslation = () => {
    const translateButtons = document.querySelectorAll('.btn-translate');
    
    translateButtons.forEach(button => {
        button.addEventListener('click', async (e) => {
            const container = e.target.closest('.input-with-btn');
            if (!container) {return;}

            const sourceInput = container.querySelector('input');
            const targetInput = container.nextElementSibling;

            if (!sourceInput || !targetInput) {return;}

            const sourceText = sourceInput.value.trim();
            if (!sourceText) {return;}

            const originalButtonText = e.target.textContent;
            e.target.textContent = '...';
            e.target.disabled = true;

            try {
                const response = await fetch(`https://api.mymemory.translated.net/get?q=${encodeURIComponent(sourceText)}&langpair=zh-CN|en-GB`);
                const data = await response.json();
                
                if (data && data.responseData && data.responseData.translatedText) {
                    targetInput.value = data.responseData.translatedText;
                    targetInput.dispatchEvent(new Event('input'));
                } else {
                    showWarning('Translation failed. Please try again.');
                }
            } catch (error) {
                console.error('Translation API error:', error);
                showError('Translation request failed. Check the console for details.');
            } finally {
                e.target.textContent = originalButtonText;
                e.target.disabled = false;
            }
        });
    });
};

// Debounce utility
const debounce = (func, delay) => {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
};

const setupMlsIdComponent = () => {
    const container = document.getElementById('mls-id-container');
    const template = document.getElementById('mls-id-row-template');
    const addButton = document.getElementById('add-mls-id-btn');

    if (!container || !template || !addButton) {
        console.error("MLS ID component elements not found!");
        return;
    }

    const createMlsIdRow = () => {
        const clone = template.content.cloneNode(true);
        const row = clone.querySelector('.mls-id-row');
        const input = clone.querySelector('.mls-id-input');
        const dropdown = clone.querySelector('.search-results-dropdown');
        const resultsList = clone.querySelector('ul');
        const deleteBtn = clone.querySelector('.delete-mls-id-btn');

        const closeDropdown = () => dropdown.classList.remove('open');
        
        const handleSearch = async (event) => {
            const keyword = event.target.value.trim();
            if (keyword.length < businessConfig.search.minSearchLength) {
                closeDropdown();
                return;
            }

            const properties = await searchMlsProperties(keyword);
            resultsList.innerHTML = '';

            if (properties.length > 0) {
                properties.forEach(prop => {
                    const li = document.createElement('li');
                    li.dataset.id = prop.id;
                    li.innerHTML = `
                        <div class="result-addr">${prop.searchAddr}</div>
                        <div class="result-meta">${prop.id} &middot; ${prop.city}, ${prop.prov}</div>
                    `;
                    resultsList.appendChild(li);
                });
                dropdown.classList.add('open');
            } else {
                closeDropdown();
            }
        };

        input.addEventListener('input', debounce(handleSearch, businessConfig.search.searchDebounceDelay));

        resultsList.addEventListener('click', (event) => {
            const li = event.target.closest('li');
            if (li) {
                input.value = li.dataset.id;
                closeDropdown();
            }
        });

        deleteBtn.addEventListener('click', () => {
            row.remove();
        });

        document.addEventListener('click', (event) => {
            if (!row.contains(event.target)) {
                closeDropdown();
            }
        });

        return row;
    };

    addButton.addEventListener('click', () => {
        const newRow = createMlsIdRow();
        container.appendChild(newRow);
    });

    container.appendChild(createMlsIdRow());
};

export const renderUpload = async () => {
    try {
        await renderView('upload', '#app-container');
        initTopProgress();

        const videoUploaderInstance = setupFileUploader('video-uploader', {
            onPreview: (url) => showPreviewModal(url, 'video'),
            onFileChange: () => checkFormValidityAndUpdateButton(),
            onUploadProgress: (percentage) => {
                updateTopProgress(percentage);
            }
        });

        const coverUploaderInstance = setupFileUploader('cover-uploader', {
            onPreview: (url) => showPreviewModal(url, 'image'),
            onFileChange: () => checkFormValidityAndUpdateButton(),
        });

        setupModalControls();
        setupTranslation();
        setupMlsIdComponent();

        // 客户下拉交由统一初始化（见文件末尾，与 category 一致）

        const newClientBtn = document.querySelector('.label-row .btn-new');
        if (newClientBtn) {
            newClientBtn.addEventListener('click', () => {
                openClientModal(null, () => {
                    // 创建成功后刷新下拉数据，但不自动选择，让用户自行选择
                    clientDropdownInstance?.refresh?.();
                });
            });
        }
        
        initClientModal();

        const form = document.getElementById('upload-form');
        const titleCnInput = document.getElementById('title-cn');
        const descriptionCnInput = document.getElementById('description-cn');
        const publishBtn = document.getElementById('publish-btn');
        const saveDraftBtn = document.getElementById('save-draft-btn');

        const checkFormValidityAndUpdateButton = () => {
            const videoFile = videoUploaderInstance.getFile();
            const coverFile = coverUploaderInstance.getFile();
            const titleCn = titleCnInput.value.trim();
            const descriptionCn = descriptionCnInput.value.trim();
            
            const isFormValid = videoFile && coverFile && titleCn && descriptionCn;
            publishBtn.disabled = !isFormValid;
            saveDraftBtn.disabled = !isFormValid;
        };

        const handleFormSubmit = async (publishNow) => {
            publishBtn.disabled = true;
            saveDraftBtn.disabled = true;

            const videoFile = videoUploaderInstance.getFile();
            const coverFile = coverUploaderInstance.getFile();

            const metadata = {
                title: {
                    zh: document.getElementById('title-cn').value,
                    en: document.getElementById('title-en').value,
                },
                description: {
                    zh: document.getElementById('description-cn').value,
                    en: document.getElementById('description-en').value,
                },
                categoryId: document.getElementById('category-dropdown').dataset.value,
                clientId: document.getElementById('client-dropdown').dataset.value,
                propertyIds: Array.from(document.querySelectorAll('.mls-id-input')).map(input => input.value).filter(Boolean),
                externalUrl: document.getElementById('url-link').value,
                publishNow: publishNow
            };

            try {
                if (videoFile && videoUploaderInstance.shouldUseChunkedUpload()) {
                    showTopProgress();
                    await handleChunkedUpload(metadata, videoFile, coverFile);
                } else {
                    await handleTraditionalUpload(metadata, videoFile, coverFile);
                }

                try {
                    showSuccess('操作成功');
                } catch (messageError) {
                    console.error('Error showing success message:', messageError);
                }
                navigateTo('/dashboard');
            } catch (error) {
                publishBtn.disabled = false;
                saveDraftBtn.disabled = false;

                ErrorHandler.handleError(error, ErrorTypes.BUSINESS, ErrorSeverity.MEDIUM, {
                    context: 'Video creation failed',
                    metadata: metadata
                });
            }
        };

        const handleTraditionalUpload = async (metadata, videoFile, coverFile) => {
            const formData = new FormData();

            if (videoFile) {formData.append('video', videoFile);}            
            if (coverFile) {formData.append('thumbnail', coverFile);}            

            formData.append('metadata', JSON.stringify(metadata));
            formData.append('publishNow', metadata.publishNow.toString());

            return await createVideo(formData);
        };

        const handleChunkedUpload = async (metadata, videoFile, coverFile) => {
            let coverUploadResult = null;
            if (coverFile) {
                try {
                    coverUploadResult = await uploadThumbnail(coverFile);
                } catch (error) {
                    throw new Error('封面图上传失败: ' + error.message);
                }
            }

            const videoUploadResult = await videoUploaderInstance.startChunkedUpload();
            updateTopProgress(100);
            hideTopProgress();

            const videoMetadata = {
                ...metadata,
                draftVideoGouploadPath: videoUploadResult.path,
                draftThumbGouploadPath: coverUploadResult ? coverUploadResult.path : undefined
            };

            return await createVideoFromChunkedUpload(videoMetadata);
        };

        form.addEventListener('input', checkFormValidityAndUpdateButton);
        publishBtn.addEventListener('click', (e) => {
            e.preventDefault();
            if (!publishBtn.disabled) {
                handleFormSubmit(true);
            }
        });
        saveDraftBtn.addEventListener('click', (e) => {
            e.preventDefault();
            if (!saveDraftBtn.disabled) {
                handleFormSubmit(false);
            }
        });
        
        checkFormValidityAndUpdateButton();

        setupCustomDropdown(
            'category-dropdown',
            async () => getCategoryList({ limit: businessConfig.categories.maxCategoryListLimit }),
            { addNoneOption: false }
        );

        const clientDropdownInstance = setupCustomDropdown(
            'client-dropdown',
            async () => getClientList({ limit: businessConfig.clients.maxClientListLimit }),
            { addNoneOption: true }
        );

        setPageCleanup(() => {
            if (videoUploaderInstance && videoUploaderInstance.cleanup) {
                videoUploaderInstance.cleanup();
            }
            hideTopProgress(0);
        });

    } catch (error) {
        console.error(`Error rendering upload page: ${error}`);
        const appContainer = document.getElementById('app-container');
        if (appContainer) {
            appContainer.innerHTML = `<div class="error-message">Error rendering page: ${error.message}</div>`;
        }
    }
};