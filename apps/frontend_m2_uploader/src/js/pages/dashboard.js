import Mustache from 'mustache';
import Hls from 'hls.js';
import { renderView } from '../viewRenderer';
import { getVideos, getStats, deleteVideo } from '../services/apiClient';
import { API_BASE_URL as _API_BASE_URL, uiConfig } from '../config'; // Import from the unified config system
import { navigateTo, setPageCleanup } from '../router';
import <PERSON>rrorHand<PERSON>, { showSuccess, showWarning, showError, showInfo, ErrorTypes, ErrorSeverity } from '../utils/errorHandler';

/**
 * 格式化日期为 YYYY-MM-DD
 */
const formatDate = (dateString) => {
  if (!dateString) {return '';}
  return new Date(dateString).toISOString().split('T')[0];
};

/**
 * 格式化秒为 MM:SS
 */
const formatDuration = (totalSeconds) => {
    if (isNaN(totalSeconds) || totalSeconds < 0) {return '00:00';}
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = parseInt(totalSeconds % 60, 10);
    return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
};

let allVideosData = []; // Cache for holding video data for the player
let currentPage = 1;
let totalPages = 1;
const videosPerPage = uiConfig.pagination.videosPerPage; // 使用配置化的分页大小

// 视频状态轮询相关变量
let statusPollingInterval = null;
const POLLING_INTERVAL = uiConfig.polling.statusInterval; // 使用配置化的轮询间隔



export const renderDashboard = async () => {
    const targetSelector = '#app-container';
    
    try {
        // Reset state for full page render
        currentPage = 1;
        allVideosData = [];

        // 先渲染页面骨架，并获取渲染后的根元素
        const targetElement = await renderView('dashboard', targetSelector);
        if (!targetElement) {
          console.error("Dashboard render failed: target element not found.");
          return;
        };

        const videoListContainer = targetElement.querySelector('.video-list-container');
        const analyticsContainer = targetElement.querySelector('.dashboard-analytics');

        // For a hash-based router, search params are after the '?' in the hash
        const hash = window.location.hash;
        const queryStringIndex = hash.indexOf('?');
        const queryString = queryStringIndex !== -1 ? hash.substring(queryStringIndex + 1) : '';
        const urlParams = new URLSearchParams(queryString);

        let apiParams = {};
        // Convert URLSearchParams to a plain object for our API functions
        for (const [key, value] of urlParams.entries()) {
            // For keys that can appear multiple times (like 'status'), group them into an array
            if (apiParams[key]) {
                if (Array.isArray(apiParams[key])) {
                    apiParams[key].push(value);
                } else {
                    apiParams[key] = [apiParams[key], value];
                }
            } else {
                apiParams[key] = value;
            }
        }
        
        // 当没有URL参数时，apiParams默认为空对象，以获取所有统计信息
        // 如果需要默认过滤，可以在这里设置，例如：
        // if (Object.keys(apiParams).length === 0) {
        //     apiParams = { status: 'Published' };
        // }

        // Define default analytics data for fallback
        const defaultAnalytics = {
            totalVideos: 0,
            totalViews: 0,
            totalLikes: 0,
            totalCollections: 0,
            overallCompletionRate: '0%'
        };

        // 并行获取分析数据和视频数据
        let [analyticsData, videoData] = await Promise.all([
            getStats(apiParams),
            getVideos(apiParams)
        ]);

        // Cache the video items data
        if (videoData && videoData.items) {
            allVideosData = videoData.items;
        }

        // Merge API data with defaults to ensure all keys exist
        let finalAnalyticsData = { ...defaultAnalytics, ...analyticsData };

        // Map the final data to the keys expected by the template
        const templateData = {
            videos: finalAnalyticsData.totalVideos,
            views: finalAnalyticsData.totalViews,
            likes: finalAnalyticsData.totalLikes,
            saves: finalAnalyticsData.totalCollections,
            completionRate: finalAnalyticsData.overallCompletionRate.replace('%', '') // Template adds the '%' sign
        };

        // 渲染分析数据 - 初始渲染使用Mustache模板
        if (analyticsContainer && templateData) {
            const analyticsHtml = Mustache.render(analyticsContainer.innerHTML, { analytics: templateData });
            analyticsContainer.innerHTML = analyticsHtml;
        }

        // 确保加载指示器是隐藏的（防止HTMX事件监听器的干扰）
        const initialLoadingIndicator = targetElement.querySelector('.loading-indicator');
        if (initialLoadingIndicator) {
            initialLoadingIndicator.style.display = 'flex';
        }

        // Initial video load
        await loadVideos(apiParams, true);

        // Setup event listeners that only need to be attached once
        setupPlayerModal();
        if (videoListContainer) {
            setupVideoListEventListeners(videoListContainer);
        }

        // 开始状态轮询（如果有Processing视频）
        startStatusPolling();

        // 设置页面清理函数
        setPageCleanup(() => {
            stopStatusPolling();
        });

    } catch (error) {
        ErrorHandler.handleError(error, ErrorTypes.SYSTEM, ErrorSeverity.HIGH, {
            context: 'Dashboard rendering failed',
            targetSelector: targetSelector
        });

        // 显示错误页面
        const errorTarget = document.querySelector(targetSelector);
        if (errorTarget) {
            errorTarget.innerHTML = '<div class="error-message">仪表盘加载失败，请刷新页面重试</div>';
        }
    }
};

/**
 * 更新analytics显示
 * @param {HTMLElement} analyticsContainer - analytics容器元素
 * @param {Object} templateData - 模板数据
 */
const updateAnalyticsDisplay = async (analyticsContainer, templateData) => {
    if (!analyticsContainer || !templateData) {return;}

    // 直接更新DOM元素的值，而不是重新渲染整个模板
    const analyticsItems = analyticsContainer.querySelectorAll('.analytics-item');

    if (analyticsItems.length >= 5) {
        // 更新视频数量
        const videoValueElement = analyticsItems[0].querySelector('.analytics-value');
        if (videoValueElement) {videoValueElement.textContent = templateData.videos;}

        // 更新观看数
        const viewsValueElement = analyticsItems[1].querySelector('.analytics-value');
        if (viewsValueElement) {viewsValueElement.textContent = templateData.views;}

        // 更新点赞数
        const likesValueElement = analyticsItems[2].querySelector('.analytics-value');
        if (likesValueElement) {likesValueElement.textContent = templateData.likes;}

        // 更新收藏数
        const savesValueElement = analyticsItems[3].querySelector('.analytics-value');
        if (savesValueElement) {savesValueElement.textContent = templateData.saves;}

        // 更新完成率
        const completionRateValueElement = analyticsItems[4].querySelector('.analytics-value');
        if (completionRateValueElement) {completionRateValueElement.textContent = `${templateData.completionRate}%`;}
    }
};

/**
 * 重新获取并更新analytics数据
 */
const refreshAnalytics = async () => {
    try {
        const analyticsContainer = document.querySelector('.dashboard-analytics');
        if (!analyticsContainer) {return;}

        // 获取当前的筛选参数
        const hash = window.location.hash;
        const queryStringIndex = hash.indexOf('?');
        const queryString = queryStringIndex !== -1 ? hash.substring(queryStringIndex + 1) : '';
        const urlParams = new URLSearchParams(queryString);

        let apiParams = {};
        for (const [key, value] of urlParams.entries()) {
            if (apiParams[key]) {
                if (Array.isArray(apiParams[key])) {
                    apiParams[key].push(value);
                } else {
                    apiParams[key] = [apiParams[key], value];
                }
            } else {
                apiParams[key] = value;
            }
        }

        // 重新获取analytics数据
        const analyticsData = await getStats(apiParams);

        // 默认analytics数据
        const defaultAnalytics = {
            totalVideos: 0,
            totalViews: 0,
            totalLikes: 0,
            totalCollections: 0,
            overallCompletionRate: '0%'
        };

        // 合并数据
        let finalAnalyticsData = { ...defaultAnalytics, ...analyticsData };

        // 映射到模板数据
        const templateData = {
            videos: finalAnalyticsData.totalVideos,
            views: finalAnalyticsData.totalViews,
            likes: finalAnalyticsData.totalLikes,
            saves: finalAnalyticsData.totalCollections,
            completionRate: finalAnalyticsData.overallCompletionRate.replace('%', '')
        };

        // 更新显示
        await updateAnalyticsDisplay(analyticsContainer, templateData);
    } catch (error) {
        console.error('Failed to refresh analytics:', error);
    }
};

/**
 * 检查是否有Processing状态的视频
 * @returns {boolean} 是否有Processing状态的视频
 */
const hasProcessingVideos = () => {
    return allVideosData.some(video =>
        video.status === 'Pending' ||
        video.status === 'Processing' ||
        video.status === 'processing'
    );
};

/**
 * 更新单个视频的状态显示
 * @param {Object} updatedVideo - 更新后的视频数据
 */
const updateVideoStatus = (updatedVideo) => {
    const videoElement = document.querySelector(`[data-video-id="${updatedVideo.id}"]`);
    if (!videoElement) {return;}

    // 更新缓存中的视频数据
    const videoIndex = allVideosData.findIndex(v => v.id === updatedVideo.id);
    if (videoIndex !== -1) {
        allVideosData[videoIndex] = updatedVideo;
    }

    // 更新状态显示
    const statusElement = videoElement.querySelector('.status-pill');
    if (statusElement) {
        let displayStatus = updatedVideo.status;
        let statusClass = updatedVideo.status.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();

        // 重新映射状态显示
        if (updatedVideo.status === 'Pending') {
            displayStatus = 'Processing';
            statusClass = 'processing';
        } else if (updatedVideo.status === 'ProcessingFailed') {
            displayStatus = 'Failed';
            statusClass = 'failed';
        }

        statusElement.textContent = displayStatus;
        statusElement.className = `status-pill status-${statusClass}`;
    }

    // 更新操作按钮的可见性
    const actionsContainer = videoElement.querySelector('.video-actions');
    if (actionsContainer) {
        const isProcessing = updatedVideo.status.toLowerCase() === 'processing' ||
                           updatedVideo.status.toLowerCase() === 'pending';
        if (isProcessing) {
            actionsContainer.classList.add('is-hidden');
        } else {
            actionsContainer.classList.remove('is-hidden');
        }
    }
};

/**
 * 轮询检查Processing视频的状态
 */
const pollProcessingVideos = async () => {
    try {
        if (!hasProcessingVideos()) {
            // 没有Processing视频，停止轮询
            stopStatusPolling();
            return;
        }

        // 获取当前的筛选参数
        const hash = window.location.hash;
        const queryStringIndex = hash.indexOf('?');
        const queryString = queryStringIndex !== -1 ? hash.substring(queryStringIndex + 1) : '';
        const urlParams = new URLSearchParams(queryString);

        let apiParams = {};
        for (const [key, value] of urlParams.entries()) {
            if (apiParams[key]) {
                if (Array.isArray(apiParams[key])) {
                    apiParams[key].push(value);
                } else {
                    apiParams[key] = [apiParams[key], value];
                }
            } else {
                apiParams[key] = value;
            }
        }

        // 获取最新的视频数据
        const videoData = await getVideos(apiParams);
        if (videoData && videoData.items) {
            let hasStatusChanged = false;

            // 检查每个Processing视频是否状态发生变化
            allVideosData.forEach(cachedVideo => {
                if (cachedVideo.status === 'Pending' ||
                    cachedVideo.status === 'Processing' ||
                    cachedVideo.status === 'processing') {

                    const updatedVideo = videoData.items.find(v => v.id === cachedVideo.id);
                    if (updatedVideo && updatedVideo.status !== cachedVideo.status) {
                        updateVideoStatus(updatedVideo);
                        hasStatusChanged = true;
                    }
                }
            });

            // 如果有状态变化，更新analytics
            if (hasStatusChanged) {
                await refreshAnalytics();
            }
        }
    } catch (error) {
        console.error('Failed to poll processing videos:', error);
    }
};

/**
 * 开始状态轮询
 */
const startStatusPolling = () => {
    if (statusPollingInterval) {
        clearInterval(statusPollingInterval);
    }

    if (hasProcessingVideos()) {
        statusPollingInterval = setInterval(pollProcessingVideos, POLLING_INTERVAL);
    }
};

/**
 * 停止状态轮询
 */
const stopStatusPolling = () => {
    if (statusPollingInterval) {
        clearInterval(statusPollingInterval);
        statusPollingInterval = null;
    }
};

const loadVideos = async (apiParams, isInitialLoad = false) => {
    const videoListContainer = document.querySelector('.video-list-container');
    const loadMoreContainer = document.querySelector('.load-more-container');

    try {
        const videoData = await getVideos({ ...apiParams, page: currentPage, limit: videosPerPage });
        
        if (videoData && videoData.items && videoData.items.length > 0) {
            allVideosData.push(...videoData.items);
            totalPages = videoData.pgn.totPgs;

            const renderedList = renderVideoItems(videoData.items);

            // 检查是否需要开始轮询（当加载更多视频时）
            if (!isInitialLoad) {
                startStatusPolling();
            }
            
            if (isInitialLoad) {
                const loadingIndicator = videoListContainer.querySelector('.loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
                
                const listWrapper = document.createElement('div');
                listWrapper.className = 'video-list';
                listWrapper.innerHTML = renderedList;
                videoListContainer.prepend(listWrapper);
            } else {
                const videoList = videoListContainer.querySelector('.video-list');
                if(videoList) {videoList.insertAdjacentHTML('beforeend', renderedList);}
            }
        } else if (isInitialLoad) {
            const loadingIndicator = videoListContainer.querySelector('.loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.textContent = 'No videos found.';
                loadingIndicator.style.display = 'flex'; // 确保显示
            }
        }
        
        updateLoadMoreButton();

    } catch (error) {
        console.error('Failed to load videos:', error);

        // 如果是初始加载失败，显示错误消息
        if (isInitialLoad) {
            const loadingIndicator = videoListContainer.querySelector('.loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.textContent = '加载视频失败，请刷新页面重试';
                loadingIndicator.className = 'loading-indicator error';
                loadingIndicator.style.display = 'flex'; // 确保显示错误消息
            }
        } else if (loadMoreContainer) {
            loadMoreContainer.innerHTML = `<p class="error-message">Failed to load more videos.</p>`;
        }
    }
};

const renderVideoItems = (items) => {
    const videoItemTemplate = `
        {{#items}}
        <div class="video-item" data-video-id="{{id}}">
            <div class="video-cover">
                <img src="{{previewThumbUrl}}" alt="{{title.zh}}" class="cover-image">
                <div class="play-icon-overlay">
                   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="48" height="48"><path d="M8 5v14l11-7z"></path></svg>
                </div>
                <span class="duration-badge">{{durationFormatted}}</span>
            </div>
            <div class="video-details">
                <p class="video-title-text">{{title.zh}}</p>
                <div class="video-meta-row">
                    <div class="uploader-info">
                        <img src="{{uploader.avatarUrl}}" alt="{{uploader.name}}" class="uploader-avatar">
                        <span>{{uploader.name}}</span>
                    </div>
                </div>
                <div class="video-meta-row">
                     <div class="video-stats">
                        <span class="stat-item"><img src="/icons/views/views.svg" class="stat-icon">{{stats.views}}</span>
                        <span class="stat-item"><img src="/icons/likes/likes.svg" class="stat-icon">{{stats.likes}}</span>
                        <span class="stat-item"><img src="/icons/fav/fav.svg" class="stat-icon">{{stats.collections}}</span>
                        <span class="publish-date">{{displayDate}}</span>
                    </div>
                </div>
            </div>
            <div class="video-status-container">
                <span class="status-pill status-{{statusClass}}">{{status}}</span>
            </div>
            <div class="video-actions {{actionsVisibilityClass}}" style="min-width: 140px; display: flex; justify-content: flex-end; align-items: center;">
                <button class="action-btn edit-btn"><img src="/icons/edit/edit.svg" alt="Edit"></button>
                <button class="action-btn delete-btn-initial"><img src="/icons/delete/delete.svg" alt="Delete"></button>
                <button class="action-btn confirm-delete-btn" style="display: none; background-color: #E03131; color: white; border: none; padding: 0 12px; line-height: 28px; height: 30px; border-radius: 4px; margin-right: 8px;">Delete</button>
                <button class="action-btn cancel-delete-btn" style="display: none; background: transparent; border: none; box-shadow: none; text-decoration: underline; padding: 0; color: #888;">Cancel</button>
            </div>
        </div>
        {{/items}}
    `;
    
    const formattedData = {
        items: items.map(item => {
            const displayDate = item.publishedAt || item.updatedAt || item.createdAt;
            const originalStatus = item.status || 'unknown';
            
            let statusClass = originalStatus.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
            let displayStatus = originalStatus;

            // Remap statuses for display
            if (originalStatus === 'Pending') {
                displayStatus = 'Processing';
                statusClass = 'processing';
            } else if (originalStatus === 'ProcessingFailed') {
                displayStatus = 'Failed';
                statusClass = 'failed';
            }

            // 处理预览缩略图URL
            let finalThumbUrl = item.previewThumbUrl;
            if (finalThumbUrl) {
                if (!finalThumbUrl.startsWith('http')) {
                    // 相对路径，使用nginx提供的媒体文件服务
                    finalThumbUrl = `${MEDIA_BASE_URL}${finalThumbUrl}`;
                } else {
                    // 绝对URL，检查是否为开发环境URL，需要转换为相对路径
                    const devUrlPattern = /^https?:\/\/[^\/]+(:3000)?/;
                    if (devUrlPattern.test(finalThumbUrl)) {
                        // 提取路径部分，去掉域名和端口
                        const urlPath = finalThumbUrl.replace(devUrlPattern, '');
                        finalThumbUrl = `${MEDIA_BASE_URL}${urlPath}`;
                    }
                }
            }

            return {
                ...item,
                status: displayStatus,
                durationFormatted: item.duration ? formatDuration(item.duration) : '00:00',
                displayDate: formatDate(displayDate),
                statusClass: statusClass,
                previewThumbUrl: finalThumbUrl,
                actionsVisibilityClass: (originalStatus.toLowerCase() === 'processing' || originalStatus.toLowerCase() === 'pending') ? 'is-hidden' : '',
                // Hardcoded uploader info as requested
                uploader: {
                    name: "RealMaster",
                    avatarUrl: `https://i.pravatar.cc/32?u=realmaster` // 可以考虑配置化头像服务
                }
            };
        })
    };

    return Mustache.render(videoItemTemplate, formattedData);
};

const updateLoadMoreButton = () => {
    const loadMoreContainer = document.querySelector('.load-more-container');
    if (!loadMoreContainer) {return;}

    loadMoreContainer.innerHTML = ''; // Clear previous button
    if (currentPage < totalPages) {
        const button = document.createElement('button');
        button.textContent = 'Load More';
        button.className = 'load-more-btn';
        button.addEventListener('click', async (e) => {
            e.target.disabled = true;
            e.target.textContent = 'Loading...';
            currentPage++;
            await loadVideos({}); // Assuming no new params for subsequent loads
            e.target.disabled = false;
            e.target.textContent = 'Load More';
        });
        loadMoreContainer.appendChild(button);
    }
};

const setupVideoListEventListeners = (videoListContainer) => {
    videoListContainer.addEventListener('click', async (event) => {
        const videoItem = event.target.closest('.video-item');
        if (!videoItem) {return;}

        const videoId = videoItem.dataset.videoId;

        // Handle play video logic
        if (event.target.closest('.video-cover')) {
            handleVideoPlay(videoId);
            return; // Stop further processing
        }
        
        // Handle edit button click
        const editBtn = event.target.closest('.edit-btn');
        if (editBtn) {
            navigateTo(`/edit-video/${videoId}`);
            return;
        }

        // Handle delete logic
        const initialDeleteBtn = event.target.closest('.delete-btn-initial');
        const cancelBtn = event.target.closest('.cancel-delete-btn');
        const confirmBtn = event.target.closest('.confirm-delete-btn');
        const actionsContainer = videoItem.querySelector('.video-actions');
        if (!actionsContainer) {return;}
        const editBtnInItem = actionsContainer.querySelector('.edit-btn');
        const initialDelBtn = actionsContainer.querySelector('.delete-btn-initial');
        const confirmDelBtn = actionsContainer.querySelector('.confirm-delete-btn');
        const cancelDelBtn = actionsContainer.querySelector('.cancel-delete-btn');
        if (initialDeleteBtn) {
            editBtnInItem.style.display = 'none';
            initialDelBtn.style.display = 'none';
            confirmDelBtn.style.display = 'inline-block';
            cancelDelBtn.style.display = 'inline-block';
        }
        if (cancelBtn) {
            editBtnInItem.style.display = '';
            initialDelBtn.style.display = '';
            confirmDelBtn.style.display = 'none';
            cancelDelBtn.style.display = 'none';
        }
        if (confirmBtn) {
            try {
                const response = await deleteVideo(videoId);
                if (response.ok) {
                    videoItem.remove();
                    // 重新获取并更新analytics数据
                    await refreshAnalytics();
                }
                else { showError(response.msg || 'Failed to delete video.'); }
            } catch (error) {
                ErrorHandler.handleError(error, ErrorTypes.BUSINESS, ErrorSeverity.MEDIUM, {
                    context: 'Delete video failed',
                    videoId: videoId
                });
                editBtnInItem.style.display = ''; initialDelBtn.style.display = ''; confirmDelBtn.style.display = 'none'; cancelDelBtn.style.display = 'none';
            }
        }
    });
};

// 全局变量存储当前的HLS实例
let currentHls = null;

const handleVideoPlay = (videoId) => {
    const video = allVideosData.find(v => v.id === videoId);
    if (!video) {
        console.error('Video not found for ID:', videoId);
        return;
    }

            if (video.status === 'Processing') {
        showInfo('视频正在处理中，请稍后再试。');
        return;
    }

    const modal = document.getElementById('video-player-modal');
    const videoPlayer = document.getElementById('video-player');

    // 清理之前的HLS实例
    if (currentHls) {
        currentHls.destroy();
        currentHls = null;
    }

    if (!video.previewVideoUrl) {
        showInfo('此视频没有可用的预览。');
        return;
    }

    // 处理预览视频URL
    let finalVideoUrl = video.previewVideoUrl;
    if (finalVideoUrl) {
        if (!finalVideoUrl.startsWith('http')) {
            // 相对路径，使用nginx提供的媒体文件服务
            finalVideoUrl = `${MEDIA_BASE_URL}${finalVideoUrl}`;
        } else {
            // 绝对URL，检查是否为开发环境URL，需要转换为相对路径
            const devUrlPattern = /^https?:\/\/[^\/]+(:3000)?/;
            if (devUrlPattern.test(finalVideoUrl)) {
                // 提取路径部分，去掉域名和端口
                const urlPath = finalVideoUrl.replace(devUrlPattern, '');
                finalVideoUrl = `${MEDIA_BASE_URL}${urlPath}`;
            }
        }
    }

    // 根据文件扩展名判断是否使用HLS播放器
    if (finalVideoUrl.endsWith('.m3u8')) {
        // HLS文件，使用HLS播放器
        if (Hls.isSupported()) {
            currentHls = new Hls();
            currentHls.loadSource(finalVideoUrl);
            currentHls.attachMedia(videoPlayer);
            currentHls.on(Hls.Events.MANIFEST_PARSED, () => {
                modal.classList.remove('hidden');
                videoPlayer.play();
            });
        } else if (videoPlayer.canPlayType('application/vnd.apple.mpegurl')) {
            // Safari原生支持HLS
            videoPlayer.src = finalVideoUrl;
            modal.classList.remove('hidden');
            videoPlayer.play();
        } else {
            console.error('HLS not supported in this browser');
            showWarning('您的浏览器不支持HLS播放');
            return;
        }
    } else {
        // MP4文件，使用普通video元素
        videoPlayer.src = finalVideoUrl;
        modal.classList.remove('hidden');
        videoPlayer.play();
    }
};

const setupPlayerModal = () => {
    const modal = document.getElementById('video-player-modal');
    const videoPlayer = document.getElementById('video-player');
    const closeBtn = modal.querySelector('.close-btn');

    const closeModal = () => {
        modal.classList.add('hidden');
        videoPlayer.pause();
        videoPlayer.src = ""; // Clear src to stop buffering

        // 清理HLS实例
        if (currentHls) {
            currentHls.destroy();
            currentHls = null;
        }
    };

    closeBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (event) => {
        // Close if clicking on the background overlay
        if (event.target === modal) {
            closeModal();
        }
    });
}; 