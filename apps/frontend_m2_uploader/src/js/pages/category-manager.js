import Mustache from 'mustache';
import { renderView } from '../viewRenderer';
import { getCategoryList, createCategory, updateCategory, deleteCategory } from '../services/apiClient';
import { invalidateDropdownCache } from '../components/customDropdown';
import { navigateTo } from '../router';
import Error<PERSON>and<PERSON>, { showSuccess, showWarning, showError, showInfo, ErrorTypes, ErrorSeverity } from '../utils/errorHandler';

// Store current page state
let currentPage = 1;

const fetchAndRenderCategories = async (page) => {
    try {
        const container = document.querySelector('.category-list-container');
        const footer = document.querySelector('.page-footer');

        if (!container || !footer) {
            console.error('Required elements not found for rendering categories.');
            return;
        }

        // Show loading indicator
        container.innerHTML = `<div class="loading-indicator">Loading categories...</div>`;
        footer.innerHTML = ''; // Clear old pagination

        const data = await getCategoryList({ page, limit: 10 });
        currentPage = data.pgn.currentPage;


        
        // Hide loading indicator after fetching
        container.querySelector('.loading-indicator').style.display = 'none';

        // Add a helper to the data to check if a category is 'None'
        const viewData = {
            items: data.items.map(item => ({
                ...item,
                isNoneCategory: item.name === 'None'
            })),
            pgn: data.pgn,
            // 提升分页字段到顶层供模板使用
            currentPage: data.pgn.currentPage,
            totalPages: data.pgn.totalPages,
            isFirstPage: data.pgn.currentPage === 1,
            isLastPage: data.pgn.currentPage === data.pgn.totalPages
        };
        
        const itemTemplate = `
            {{#items}}
            <div class="category-item" data-category-id="{{id}}">
                <span class="category-name">{{name}}</span>
                <div class="item-actions" style="min-width: 140px; display: flex; justify-content: flex-end; align-items: center;">
                    {{^isNoneCategory}}
                        <button class="action-btn edit-btn"><img src="/icons/edit/edit.svg" alt="Edit"></button>
                        <button class="action-btn delete-btn-initial"><img src="/icons/delete/delete.svg" alt="Delete"></button>
                        <button class="action-btn confirm-delete-btn" style="display: none; background-color: #d9534f; color: white; border: none; padding: 0 12px; line-height: 28px; height: 30px; border-radius: 4px; margin-right: 8px;">Delete</button>
                        <button class="action-btn cancel-delete-btn" style="display: none; background: transparent; border: none; box-shadow: none; text-decoration: underline; padding: 0; color: #888;">Cancel</button>
                    {{/isNoneCategory}}
                </div>
            </div>
            {{/items}}
        `;

        container.innerHTML = Mustache.render(itemTemplate, viewData);

        const paginationTemplate = `
            <div class="pagination-controls">
                <button class="pagination-btn prev-btn" {{#isFirstPage}}disabled{{/isFirstPage}}>◀</button>
                <span>{{currentPage}} / {{totalPages}}</span>
                <button class="pagination-btn next-btn" {{#isLastPage}}disabled{{/isLastPage}}>▶</button>
            </div>
        `;
        footer.innerHTML = Mustache.render(paginationTemplate, viewData);

        // Add event listeners for new pagination buttons
        document.querySelector('.prev-btn')?.addEventListener('click', () => {
            if (currentPage > 1) {
                fetchAndRenderCategories(currentPage - 1);
            }
        });
        document.querySelector('.next-btn')?.addEventListener('click', () => {
            if (currentPage < data.pgn.totalPages) {
                fetchAndRenderCategories(currentPage + 1);
            }
        });

    } catch (error) {
        console.error("Failed to fetch and render categories:", error);
        const container = document.querySelector('.category-list-container');
        if (container) {
            container.innerHTML = `<div class="error-message">Failed to load categories. Please try again.</div>`;
        }
    }
};

const setupCategoryEventListeners = (container) => {
    container.addEventListener('click', async (event) => {
        const target = event.target;
        const categoryItem = target.closest('.category-item[data-category-id]');
        if (!categoryItem) {return;}

        const initialDeleteBtn = target.closest('.delete-btn-initial');
        const cancelBtn = target.closest('.cancel-delete-btn');
        const confirmBtn = target.closest('.confirm-delete-btn');

        const actionsContainer = categoryItem.querySelector('.item-actions');
        if (!actionsContainer) {return;}
        
        const editBtn = actionsContainer.querySelector('.edit-btn');
        const initialDelBtn = actionsContainer.querySelector('.delete-btn-initial');
        const confirmDelBtn = actionsContainer.querySelector('.confirm-delete-btn');
        const cancelDelBtn = actionsContainer.querySelector('.cancel-delete-btn');

        if (initialDeleteBtn) {
            editBtn.style.display = 'none';
            initialDelBtn.style.display = 'none';
            confirmDelBtn.style.display = 'inline-block';
            cancelDelBtn.style.display = 'inline-block';
        }

        if (cancelBtn) {
            editBtn.style.display = '';
            initialDelBtn.style.display = '';
            confirmDelBtn.style.display = 'none';
            cancelDelBtn.style.display = 'none';
        }

        if (confirmBtn) {
            const categoryId = categoryItem.dataset.categoryId;
            try {
                const response = await deleteCategory(categoryId);
                if (response.ok) {
                    categoryItem.remove();
                    // 触发分类缓存失效事件
                    invalidateDropdownCache('categories-changed');
                } else {
                    showError(response.msg || 'Failed to delete category.');
                }
            } catch (error) {
                showError(`Error: ${error.message}`);
                editBtn.style.display = '';
                initialDelBtn.style.display = '';
                confirmDelBtn.style.display = 'none';
                cancelDelBtn.style.display = 'none';
            }
        }

        const editBtnClicked = target.closest('.edit-btn');
        if (editBtnClicked) {
            const categoryId = categoryItem.dataset.categoryId;
            const categoryName = categoryItem.querySelector('.category-name').textContent;
            openModalForEdit({ id: categoryId, name: categoryName });
        }
    });
};

const openModalForEdit = (category) => {
    const modal = document.getElementById('category-modal');
    document.getElementById('category-modal-title').textContent = 'Edit Category';
    document.getElementById('category-id').value = category.id;
    document.getElementById('category-name-input').value = category.name;
    modal.classList.remove('hidden');
};

const setupModal = () => {
    const modal = document.getElementById('category-modal');
    const newCategoryBtn = document.querySelector('.new-category-btn');
    const cancelBtn = document.getElementById('cancel-btn');
    const saveBtn = document.getElementById('save-btn');

    if (!modal || !newCategoryBtn || !cancelBtn || !saveBtn) {
        console.error('Modal components not found');
        return;
    }

    const openModalForCreate = () => {
        document.getElementById('category-modal-form').reset();
        document.getElementById('category-modal-title').textContent = 'New Category';
        document.getElementById('category-id').value = '';
        modal.classList.remove('hidden');
    };
    
    const closeModal = () => modal.classList.add('hidden');

    newCategoryBtn.addEventListener('click', openModalForCreate);
    cancelBtn.addEventListener('click', closeModal);
    
    saveBtn.addEventListener('click', async () => {
        const categoryId = document.getElementById('category-id').value;
        const nameInput = document.getElementById('category-name-input');
        const categoryName = nameInput.value.trim();

        if (!categoryName) {
            showWarning('Category name cannot be empty.');
            return;
        }
        
        const categoryData = { name: categoryName };

        try {
            if (categoryId) {
                await updateCategory(categoryId, categoryData);
            } else {
                await createCategory(categoryData);
            }
            closeModal();
            fetchAndRenderCategories(currentPage);
            // 触发分类缓存失效事件
            invalidateDropdownCache('categories-changed');
        } catch (error) {
            showError(`Failed to save category: ${error.message}`);
        }
    });

    // Also close modal if clicking on the overlay
    modal.addEventListener('click', (event) => {
        if (event.target === modal) {
            closeModal();
        }
    });
};

export const renderCategoryManager = async () => {
    try {
        await renderView('category-manager', '#app-container');
        await fetchAndRenderCategories(1); // Load initial page
        setupModal(); // Initialize modal event listeners
        const categoryContainer = document.querySelector('.category-list-container');
        if(categoryContainer) {
            setupCategoryEventListeners(categoryContainer);
        }
    } catch (error) {
        console.error("Failed to render category manager page:", error);
        const appContainer = document.querySelector('#app-container');
        if (appContainer) {
            appContainer.innerHTML = `<div class="error-message">Failed to load page layout. Please try again.</div>`;
        }
    }
}; 