import Mustache from 'mustache';
import { renderView } from '../viewRenderer';
import { getClientList, createClient as _createClient, updateClient as _updateClient, deleteClient } from '../services/apiClient';
import { initClientModal, openClientModal } from '../components/clientModal';
import { invalidateDropdownCache } from '../components/customDropdown';
import ErrorHand<PERSON>, { showSuccess, showWarning, showError, showInfo, ErrorTypes, ErrorSeverity } from '../utils/errorHandler';

let currentPage = 1;

// 去除额外的页面级成功通知，避免与全局/Modal 重复
const showClientCreatedNotification = null; // 不再使用
const showSimpleSuccessMessage = (message) => { showSuccess(message); };

const fetchAndRenderClients = async (page) => {
    try {
        const container = document.querySelector('.client-list-container');
        const tableBody = container?.querySelector('tbody');
        const footer = document.querySelector('.page-footer');

        if (!container || !tableBody || !footer) {
            console.error('Required elements not found for rendering clients.');
            return;
        }
        
        // Show loading indicator
        container.querySelector('.loading-indicator').style.display = 'block';
        tableBody.innerHTML = ''; // Clear old table data
        footer.innerHTML = '';

        const data = await getClientList({ page, limit: 8 });
        currentPage = data.pgn.currentPage;

        // Hide loading indicator
        container.querySelector('.loading-indicator').style.display = 'none';

        const rowTemplate = `
            {{#items}}
            <tr data-client-id="{{id}}">
                <td><img src="{{profilePictureUrl}}" alt="{{name}}" class="profile-picture"></td>
                <td>{{name}}</td>
                <td>{{email}}</td>
                <td>{{phone}}</td>
                <td class="memo-cell">{{memo}}</td>
                <td>
                    <div class="item-actions" style="min-width: 140px; display: flex; justify-content: flex-end; align-items: center;">
                        <button class="action-btn edit-btn"><img src="/icons/edit/edit.svg" alt="Edit"></button>
                        <button class="action-btn delete-btn-initial"><img src="/icons/delete/delete.svg" alt="Delete"></button>
                        <button class="action-btn confirm-delete-btn" style="display: none; background-color: #d9534f; color: white; border: none; padding: 0 12px; line-height: 28px; height: 30px; border-radius: 4px; margin-right: 8px;">Delete</button>
                        <button class="action-btn cancel-delete-btn" style="display: none; background: transparent; border: none; box-shadow: none; text-decoration: underline; padding: 0; color: #888;">Cancel</button>
                    </div>
                </td>
            </tr>
            {{/items}}
        `;
        tableBody.innerHTML = Mustache.render(rowTemplate, { items: data.items });

        const paginationTemplate = `
            <div class="pagination-controls">
                <button class="pagination-btn prev-btn" {{#isFirstPage}}disabled{{/isFirstPage}}>◀</button>
                <span>{{currentPage}} / {{totalPages}}</span>
                <button class="pagination-btn next-btn" {{#isLastPage}}disabled{{/isLastPage}}>▶</button>
            </div>
        `;
        const paginationData = {
            ...data.pgn,
            isFirstPage: data.pgn.currentPage === 1,
            isLastPage: data.pgn.currentPage === data.pgn.totalPages
        };

        footer.innerHTML = Mustache.render(paginationTemplate, paginationData);

        document.querySelector('.prev-btn')?.addEventListener('click', () => {
            if (currentPage > 1) {fetchAndRenderClients(currentPage - 1);} 
        });
        document.querySelector('.next-btn')?.addEventListener('click', () => {
            if (currentPage < data.pgn.totalPages) {fetchAndRenderClients(currentPage + 1);} 
        });

    } catch (error) {
        console.error("Failed to fetch and render clients:", error);
    }
};

const setupClientListEventListeners = (container) => {
    container.addEventListener('click', async (event) => {
        const target = event.target;
        const clientRow = target.closest('tr[data-client-id]');
        if (!clientRow) {return;}

        const clientId = clientRow.dataset.clientId;
        const clientName = clientRow.querySelector('td:nth-child(2)').textContent;

        const initialDeleteBtn = target.closest('.delete-btn-initial');
        const confirmBtn = target.closest('.confirm-delete-btn'); // Legacy confirm
        const cancelBtn = target.closest('.cancel-delete-btn'); // Legacy cancel

        // --- New Merge-Delete Logic ---
        if (initialDeleteBtn) {
            try {
                const allClientsData = await getClientList({ limit: 0 }); 
                if (allClientsData.pgn.totalItems <= 1) {
                    showWarning('This is the only client and cannot be deleted.');
                    return;
                }

                const mergeCandidates = allClientsData.items.filter(client => client.id !== clientId);
                showMergeModal(clientId, clientName, mergeCandidates);

            } catch (error) {
                console.error('Failed to initiate deletion process:', error);
                showError(`Error: ${error.message}`);
            }
            return; 
        }
        
        // --- Legacy inline confirmation logic (kept for safety) ---
        const actionsContainer = clientRow.querySelector('.item-actions');
        if (!actionsContainer) {return;}
        
        const editBtn = actionsContainer.querySelector('.edit-btn');
        const initialDelBtn = actionsContainer.querySelector('.delete-btn-initial');
        const confirmDelBtn = actionsContainer.querySelector('.confirm-delete-btn');
        const cancelDelBtn = actionsContainer.querySelector('.cancel-delete-btn');

        if (cancelBtn) {
            editBtn.style.display = '';
            initialDelBtn.style.display = '';
            confirmDelBtn.style.display = 'none';
            cancelDelBtn.style.display = 'none';
        }

        if (confirmBtn) {
            try {
                showInfo('Deletion must be done through the merge process.');
            } catch (error) {
                showError(`Error: ${error.message}`);
            }
        }
        // --- End of Legacy Logic ---

        const editBtnClicked = target.closest('.edit-btn');
        if (editBtnClicked) {
            const email = clientRow.querySelector('td:nth-child(3)').textContent;
            const phone = clientRow.querySelector('td:nth-child(4)').textContent;
            const memo = clientRow.querySelector('td.memo-cell').textContent;
            const avatarUrl = clientRow.querySelector('.profile-picture').src;
            
            openClientModal({ id: clientId, name: clientName, email, phone, memo, avatarUrl }, () => {
                fetchAndRenderClients(currentPage);
                // 统一在页面层给出成功提示即可，Modal 内不再提示
                showSuccess('操作成功');
            });
        }
    });
};

const showMergeModal = (sourceId, sourceName, mergeCandidates) => {
    const modal = document.getElementById('merge-client-modal');
    if (!modal) {return;}

    modal.querySelector('#source-client-name').textContent = sourceName;
    
    const dropdown = modal.querySelector('#target-client-dropdown');
    const selectedValueSpan = dropdown.querySelector('.selected-value');
    const optionsList = dropdown.querySelector('.options-list');

    optionsList.innerHTML = Mustache.render('{{#items}}<li data-value="{{id}}">{{name}}</li>{{/items}}', { items: mergeCandidates });

    const handleDropdownClick = () => { dropdown.classList.toggle('open'); };
    const handleOptionClick = (event) => {
        if (event.target.tagName === 'LI') {
            event.stopPropagation();
            const value = event.target.dataset.value;
            const text = event.target.textContent;
            selectedValueSpan.textContent = text;
            dropdown.dataset.value = value;
            dropdown.classList.remove('open');
        }
    };
    dropdown.addEventListener('click', handleDropdownClick);
    optionsList.addEventListener('click', handleOptionClick);
    
    modal.classList.remove('hidden');

    const handleConfirm = async () => {
        const targetId = dropdown.dataset.value;
        if (!targetId) {
            showInfo('Please select a client to merge into.');
            return;
        }
        try {
            await deleteClient(sourceId, targetId);
            modal.classList.add('hidden');
            fetchAndRenderClients(currentPage);
            invalidateDropdownCache('clients-changed');
            showSuccess('操作成功');
        } catch (error) {
            showError(`Failed to merge and delete client: ${error.message}`);
        } finally {
            cleanup();
        }
    };

    const handleCancel = () => { modal.classList.add('hidden'); cleanup(); };
    
    const confirmBtn = modal.querySelector('#confirm-merge-btn');
    const cancelBtn = modal.querySelector('#cancel-merge-btn');

    const cleanup = () => {
        dropdown.removeEventListener('click', handleDropdownClick);
        optionsList.removeEventListener('click', handleOptionClick);
        confirmBtn.removeEventListener('click', handleConfirm);
        cancelBtn.removeEventListener('click', handleCancel);
    };

    confirmBtn.addEventListener('click', handleConfirm, { once: true });
    cancelBtn.addEventListener('click', handleCancel, { once: true });
};

const setupPage = () => {
    const newClientBtn = document.querySelector('.new-client-btn');
    if (newClientBtn) {
        newClientBtn.addEventListener('click', () => {
            openClientModal(null, (newClientData) => {
                fetchAndRenderClients(currentPage).then(() => {
                    // 统一提示一次
                    showSuccess('操作成功');
                });
            });
        });
    }

    const clientContainer = document.querySelector('.client-list-container');
    if (clientContainer) {
        setupClientListEventListeners(clientContainer);
    }
    
    initClientModal();
};

export const renderClients = async () => {
    try {
        await renderView('clients', '#app-container');
        await fetchAndRenderClients(1);
        setupPage();
    } catch (error) {
        console.error("Failed to render clients page:", error);
    }
}; 