import { initiateChunkedUpload, uploadChunk, completeChunkedUpload } from '../services/apiClient.js';
import { uploadConfig } from '../config.js';

/**
 * 分块上传工具类
 * 处理大文件的分块上传逻辑
 */
export class ChunkedUploader {
  constructor(file, options = {}) {
    this.file = file;
    this.chunkSize = options.chunkSize || uploadConfig.chunkedUpload.chunkSize; // 使用配置化的分块大小
    this.maxRetries = options.maxRetries || uploadConfig.chunkedUpload.maxRetries; // 使用配置化的重试次数
    this.onProgress = options.onProgress || (() => {}); // 进度回调
    this.onChunkProgress = options.onChunkProgress || (() => {}); // 单个分块进度回调
    this.onStateChange = options.onStateChange || (() => {}); // 状态变化回调

    this.totalChunks = Math.ceil(file.size / this.chunkSize);
    this.uploadedChunks = 0;
    this.uploadId = null;
    this.isUploading = false;
    this.isPaused = false;
    this.isCompleted = false;
    this.uploadedBytes = 0;

    // 失败的分块队列
    this.failedChunks = new Set();

    // 控制并发和取消
    this.parallel = options.parallelUploads || uploadConfig.chunkedUpload.parallelUploads || 3;
    this.controllers = new Map(); // chunkNumber -> AbortController
  }

  /**
   * 开始分块上传
   * @returns {Promise<Object>} 上传完成后的结果，包含goupload路径
   */
  async upload() {
    if (this.isUploading) {
      throw new Error('Upload is already in progress');
    }

    this.isUploading = true;
    this.isPaused = false;

    try {
      // 1. 初始化分块上传
      const initResult = await initiateChunkedUpload(
        this.file.name,
        this.file.size,
        "video"  // 指定文件类型为视频
      );

      this.uploadId = initResult.uploadId;
      console.log(`分块上传初始化成功，uploadId: ${this.uploadId}`);

      // 2. 上传所有分块（并发池）
      await this.uploadAllChunks();

      // 3. 完成分块上传
      const completeResult = await completeChunkedUpload(this.uploadId, this.totalChunks);

      this.isCompleted = true;
      this.isUploading = false;

      console.log('分块上传完成:', completeResult);
      return completeResult;

    } catch (error) {
      this.isUploading = false;
      console.error('分块上传失败:', error);
      throw error;
    }
  }

  /**
   * 上传所有分块（基于并发池派发任务）
   */
  async uploadAllChunks() {
    const queue = [];
    for (let chunkNumber = 1; chunkNumber <= this.totalChunks; chunkNumber++) {
      queue.push(chunkNumber);
    }

    const workers = Array.from({ length: this.parallel }, () => this.worker(queue));
    await Promise.all(workers);
  }

  async worker(queue) {
    while (queue.length && !this.isPaused) {
      const chunkNumber = queue.shift();
      try {
        await this.uploadChunkWithRetry(chunkNumber, this.sliceChunk(chunkNumber));
      } catch (err) {
        // 记录失败分块，后续可用于断点续传
        this.failedChunks.add(chunkNumber);
        throw err;
      }
    }
  }

  sliceChunk(chunkNumber) {
    const start = (chunkNumber - 1) * this.chunkSize;
    const end = Math.min(start + this.chunkSize, this.file.size);
    return this.file.slice(start, end);
  }

  /**
   * 带重试机制的分块上传
   */
  async uploadChunkWithRetry(chunkNumber, chunk) {
    let retries = 0;

    while (retries < this.maxRetries) {
      try {
        await this.uploadSingleChunk(chunkNumber, chunk);
        return; // 成功上传，退出重试循环
      } catch (error) {
        retries++;
        console.warn(`分块 ${chunkNumber} 上传失败 (重试 ${retries}/${this.maxRetries}):`, error);

        if (retries >= this.maxRetries) {
          throw new Error(`分块 ${chunkNumber} 上传失败，已达到最大重试次数: ${error.message}`);
        }

        // 指数退避（含软上限）
        const base = uploadConfig.chunkedUpload.retryDelay;
        const backoff = Math.min(base * Math.pow(2, retries - 1), base * 16); // 最多放大16倍
        await this.delay(backoff);
      }
    }
  }

  /**
   * 上传单个分块
   */
  async uploadSingleChunk(chunkNumber, chunk) {
    const controller = new AbortController();
    this.controllers.set(chunkNumber, controller);

    const onChunkProgress = (progressEvent) => {
      const chunkProgress = (progressEvent.loaded / progressEvent.total) * 100;
      this.onChunkProgress(chunkNumber, chunkProgress);

      const completedBytes = (this.uploadedChunks * this.chunkSize) + progressEvent.loaded;
      const totalProgress = (completedBytes / this.file.size) * 100;
      this.uploadedBytes = completedBytes;
      this.onProgress(totalProgress, completedBytes, this.file.size);
    };

    await uploadChunk(this.uploadId, chunkNumber, chunk, onChunkProgress, controller.signal);

    this.controllers.delete(chunkNumber);
    this.uploadedChunks++;
    console.log(`分块 ${chunkNumber}/${this.totalChunks} 上传完成`);
  }

  /** 暂停上传 */
  pause() {
    this.isPaused = true;
    // 取消当前在途分块
    this.controllers.forEach((controller) => {
      try { controller.abort(); } catch {}
    });
    this.controllers.clear();
  }

  /** 恢复上传（简单实现：重新派发失败和未完成分块） */
  async resume() {
    if (!this.uploadId) {
      throw new Error('No upload session to resume');
    }
    this.isPaused = false;

    // 重新组装队列：优先失败分块，其次未完成分块
    const queue = [
      ...Array.from(this.failedChunks.values()),
      ...Array.from({ length: this.totalChunks }, (_, i) => i + 1).filter(n => n > this.uploadedChunks && !this.failedChunks.has(n))
    ];
    this.failedChunks.clear();

    const workers = Array.from({ length: this.parallel }, () => this.worker(queue));
    await Promise.all(workers);
  }

  /** 取消上传 */
  cancel() {
    this.isPaused = true;
    this.isUploading = false;
    this.controllers.forEach((controller) => {
      try { controller.abort(); } catch {}
    });
    this.controllers.clear();
  }

  /** 获取上传进度信息 */
  getProgress() {
    return {
      uploadedChunks: this.uploadedChunks,
      totalChunks: this.totalChunks,
      uploadedBytes: this.uploadedBytes,
      totalBytes: this.file.size,
      percentage: (this.uploadedBytes / this.file.size) * 100,
      isCompleted: this.isCompleted,
      isUploading: this.isUploading,
      isPaused: this.isPaused
    };
  }

  /** 延迟函数 */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /** 静态：是否需要分块上传 */
  static shouldUseChunkedUpload(file, threshold = uploadConfig.chunkedUpload.threshold) {
    return file.size > threshold;
  }
}

export default ChunkedUploader;
