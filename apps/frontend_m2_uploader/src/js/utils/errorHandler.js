import { uiConfig, developmentConfig } from '../config.js';

/**
 * 错误类型枚举
 */
export const ErrorTypes = {
  NETWORK: 'network',
  VALIDATION: 'validation',
  AUTHENTICATION: 'authentication',
  PERMISSION: 'permission',
  FILE_UPLOAD: 'file_upload',
  BUSINESS: 'business',
  SYSTEM: 'system',
  USER_INPUT: 'user_input'
};

/**
 * 错误严重程度枚举
 */
export const ErrorSeverity = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

/**
 * 统一错误处理工具类
 */
export class ErrorHandler {
  /**
   * 显示用户友好的错误消息
   * @param {string} message - 用户友好的错误消息
   * @param {string} type - 消息类型：'error', 'warning', 'success', 'info'
   * @param {number} duration - 显示时长（毫秒），0表示不自动隐藏
   */
  static showUserMessage(message, type = 'error', duration = null) {
    try {
      // 检查 DOM 是否可用
      if (typeof document === 'undefined' || !document.body) {
        console.log(`[${type.toUpperCase()}] ${message}`);
        return null;
      }

      // 移除现有的消息
      this.removeExistingMessages();

      // 创建消息元素
      const messageElement = document.createElement('div');
      messageElement.className = `user-message user-message--${type}`;

      // 安全的消息内容设置
      const safeMessage = String(message || '').replace(/</g, '&lt;').replace(/>/g, '&gt;');
      messageElement.innerHTML = `
        <div class="user-message__content">
          <span class="user-message__icon">${this.getMessageIcon(type)}</span>
          <span class="user-message__text">${safeMessage}</span>
          <button class="user-message__close" aria-label="关闭">×</button>
        </div>
      `;

      // 添加样式
      this.addMessageStyles();

      // 添加到页面
      document.body.appendChild(messageElement);

      // 绑定关闭事件
      const closeBtn = messageElement.querySelector('.user-message__close');
      if (closeBtn) {
        closeBtn.addEventListener('click', () => this.removeMessage(messageElement));
      }

      // 自动隐藏
      if (duration === null) {
        duration = this.getDefaultDuration(type);
      }

      if (duration > 0) {
        setTimeout(() => this.removeMessage(messageElement), duration);
      }

      return messageElement;
    } catch (error) {
      console.error('Error showing user message:', error);
      // 降级处理：使用控制台输出
      console.log(`[${type.toUpperCase()}] ${message}`);
      return null;
    }
  }

  /**
   * 处理并显示错误
   * @param {Error|string} error - 错误对象或错误消息
   * @param {string} errorType - 错误类型
   * @param {string} severity - 错误严重程度
   * @param {Object} context - 错误上下文信息
   */
  static handleError(error, errorType = ErrorTypes.SYSTEM, severity = ErrorSeverity.MEDIUM, context = {}) {
    const errorInfo = this.parseError(error);
    // 使用 context.originalMessage 避免未使用告警
    const { originalMessage: _originalMessage } = context;
    // 记录错误日志
    this.logError(errorInfo, errorType, severity, context);
    // 显示用户友好的错误消息
    const userMessage = this.getUserFriendlyMessage(errorType, errorInfo.message);
    this.showUserMessage(userMessage, 'error');
    // 在开发环境中显示详细错误信息
    if (developmentConfig.debug.enableConsoleLog) {
      console.group(`🚨 Error [${errorType}] - ${severity.toUpperCase()}`);
      console.error('Original Error:', error);
      console.log('Context:', context);
      console.log('Stack Trace:', errorInfo.stack);
      console.groupEnd();
    }
    // 严重错误需要特殊处理
    if (severity === ErrorSeverity.CRITICAL) {
      this.handleCriticalError(errorInfo, context);
    }
  }

  /**
   * 处理网络错误
   * @param {Error} error - 网络错误
   * @param {Object} requestInfo - 请求信息
   */
  static handleNetworkError(error, requestInfo = {}) {
    let errorType = ErrorTypes.NETWORK;

    if (error.response) {
      const status = error.response.status;
      switch (status) {
        case 400: errorType = ErrorTypes.VALIDATION; break;
        case 401: errorType = ErrorTypes.AUTHENTICATION; break;
        case 403: errorType = ErrorTypes.PERMISSION; break;
        case 404: break;
        case 429: break;
        case 500: break;
        default: break;
      }
    }

    this.handleError(error, errorType, ErrorSeverity.MEDIUM, {
      requestInfo,
      status: error.response?.status,
      url: requestInfo.url
    });
  }

  /**
   * 处理文件上传错误
   * @param {Error} error - 上传错误
   * @param {Object} fileInfo - 文件信息
   */
  static handleFileUploadError(error, fileInfo = {}) {
    this.handleError(error, ErrorTypes.FILE_UPLOAD, ErrorSeverity.MEDIUM, {
      fileInfo,
      fileName: fileInfo.name,
      fileSize: fileInfo.size
    });
  }

  /**
   * 处理表单验证错误
   * @param {Object} validationErrors - 验证错误对象
   * @param {string} formName - 表单名称
   */
  static handleValidationErrors(validationErrors, formName = '') {
    const errorMessages = Object.values(validationErrors).flat();
    const message = errorMessages.length > 1 
      ? `表单填写有误，请检查以下字段：${errorMessages.join('、')}`
      : errorMessages[0] || '表单填写有误，请检查输入信息';

    this.showUserMessage(message, 'warning');

    this.logError(
      { message: 'Form validation failed', validationErrors },
      ErrorTypes.VALIDATION,
      ErrorSeverity.LOW,
      { formName, errors: validationErrors }
    );
  }

  /**
   * 显示成功消息
   * @param {string} message - 成功消息
   */
  static showSuccess(message) {
    try {
      const defaultMessage = uiConfig?.notifications?.defaultSuccessMessage || '操作成功';
      this.showUserMessage(message || defaultMessage, 'success');
    } catch (error) {
      console.error('Error showing success message:', error);
      // 降级处理：使用简单的控制台输出
      console.log('Success:', message || '操作成功');
    }
  }

  /**
   * 显示警告消息
   * @param {string} message - 警告消息
   */
  static showWarning(message) {
    try {
      const defaultMessage = uiConfig?.notifications?.defaultWarningMessage || '请注意';
      this.showUserMessage(message || defaultMessage, 'warning');
    } catch (error) {
      console.error('Error showing warning message:', error);
      // 降级处理：使用简单的控制台输出
      console.warn('Warning:', message || '请注意');
    }
  }

  /**
   * 显示信息消息
   * @param {string} message - 信息消息
   */
  static showInfo(message) {
    this.showUserMessage(message, 'info');
  }

  // ==================== 私有方法 ====================

  /**
   * 解析错误对象
   * @private
   */
  static parseError(error) {
    if (typeof error === 'string') {
      return { message: error, stack: null };
    }
    
    if (error instanceof Error) {
      return {
        message: error.message,
        stack: error.stack,
        name: error.name
      };
    }

    return {
      message: String(error),
      stack: null
    };
  }

  /**
   * 记录错误日志
   * @private
   */
  static logError(errorInfo, errorType, severity, context) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      type: errorType,
      severity,
      message: errorInfo.message,
      context,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // 根据日志级别决定是否记录
    const logLevel = developmentConfig.debug.logLevel;
    const shouldLog = this.shouldLogError(severity, logLevel);

    if (shouldLog) {
      console.error(`[${errorType.toUpperCase()}] ${errorInfo.message}`, logEntry);
    }

    // 在生产环境中可以发送到日志服务
    if (!developmentConfig.debug.enableConsoleLog) {
      this.sendErrorToService(logEntry);
    }
  }

  /**
   * 获取用户友好的错误消息
   * @private
   */
  static getUserFriendlyMessage(errorType, _originalMessage) {
    const messageMap = {
      [ErrorTypes.NETWORK]: '网络连接失败，请稍后重试',
      [ErrorTypes.VALIDATION]: '输入信息有误，请检查后重试',
      [ErrorTypes.AUTHENTICATION]: '登录已过期，请重新登录',
      [ErrorTypes.PERMISSION]: '没有权限执行此操作',
      [ErrorTypes.FILE_UPLOAD]: '文件上传失败，请重试',
      [ErrorTypes.BUSINESS]: '操作失败，请稍后重试',
      [ErrorTypes.SYSTEM]: uiConfig.notifications.defaultErrorMessage,
      [ErrorTypes.USER_INPUT]: '输入信息有误，请检查后重试'
    };

    return messageMap[errorType] || uiConfig.notifications.defaultErrorMessage;
  }

  /**
   * 获取消息图标
   * @private
   */
  static getMessageIcon(type) {
    const icons = {
      error: '❌',
      warning: '⚠️',
      success: '✅',
      info: 'ℹ️'
    };
    return icons[type] || 'ℹ️';
  }

  /**
   * 获取默认显示时长
   * @private
   */
  static getDefaultDuration(type) {
    try {
      switch (type) {
        case 'error':
          return uiConfig?.notifications?.errorDisplayDuration || 3000;
        case 'warning':
          return uiConfig?.notifications?.warningDisplayDuration || 2500;
        case 'success':
          return uiConfig?.notifications?.successDisplayDuration || 2000;
        case 'info':
          return uiConfig?.notifications?.infoDisplayDuration || 4000;
        default:
          return 3000;
      }
    } catch (error) {
      console.error('Error getting default duration:', error);
      return 3000; // 安全的默认值
    }
  }

  /**
   * 移除现有消息
   * @private
   */
  static removeExistingMessages() {
    const existingMessages = document.querySelectorAll('.user-message');
    existingMessages.forEach(msg => this.removeMessage(msg));
  }

  /**
   * 移除单个消息
   * @private
   */
  static removeMessage(messageElement) {
    if (messageElement && messageElement.parentNode) {
      messageElement.classList.add('user-message--removing');
      setTimeout(() => {
        if (messageElement.parentNode) {
          messageElement.parentNode.removeChild(messageElement);
        }
      }, 300);
    }
  }

  /**
   * 添加消息样式
   * @private
   */
  static addMessageStyles() {
    if (document.getElementById('error-handler-styles')) {return;}

    const styles = document.createElement('style');
    styles.id = 'error-handler-styles';
    styles.textContent = `
      .user-message {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        animation: slideInRight 0.3s ease-out;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .user-message--error {
        background: #fee;
        border-left: 4px solid #dc3545;
        color: #721c24;
      }

      .user-message--warning {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        color: #856404;
      }

      .user-message--success {
        background: #d4edda;
        border-left: 4px solid #28a745;
        color: #155724;
      }

      .user-message--info {
        background: #d1ecf1;
        border-left: 4px solid #17a2b8;
        color: #0c5460;
      }

      .user-message__content {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        gap: 8px;
      }

      .user-message__icon {
        font-size: 16px;
        flex-shrink: 0;
      }

      .user-message__text {
        flex: 1;
        font-size: 14px;
        line-height: 1.4;
      }

      .user-message__close {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.7;
        transition: opacity 0.2s;
      }

      .user-message__close:hover {
        opacity: 1;
      }

      .user-message--removing {
        animation: slideOutRight 0.3s ease-in forwards;
      }

      @keyframes slideInRight {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }

      @keyframes slideOutRight {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(100%);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(styles);
  }

  /**
   * 处理严重错误
   * @private
   */
  static handleCriticalError(errorInfo, context) {
    // 严重错误的特殊处理逻辑
    console.error('🚨 CRITICAL ERROR:', errorInfo, context);
    
    // 可以在这里添加错误上报、页面重载等逻辑
    if (context.shouldReload) {
      setTimeout(() => {
        if (confirm('发生严重错误，是否重新加载页面？')) {
          window.location.reload();
        }
      }, 2000);
    }
  }

  /**
   * 判断是否应该记录错误
   * @private
   */
  static shouldLogError(severity, logLevel) {
    const levels = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };

    const severityLevels = {
      [ErrorSeverity.LOW]: 1,
      [ErrorSeverity.MEDIUM]: 2,
      [ErrorSeverity.HIGH]: 3,
      [ErrorSeverity.CRITICAL]: 3
    };

    return severityLevels[severity] >= (levels[logLevel] || 1);
  }

  /**
   * 发送错误到日志服务
   * @private
   */
  static sendErrorToService(logEntry) {
    // 在生产环境中可以实现错误上报
    // 例如发送到 Sentry、LogRocket 等服务
    if (typeof window !== 'undefined' && window.errorReportingService) {
      window.errorReportingService.report(logEntry);
    }
  }
}

// 导出便捷方法
export const showError = (message) => ErrorHandler.showUserMessage(message, 'error');
export const showSuccess = (message) => ErrorHandler.showSuccess(message);
export const showWarning = (message) => ErrorHandler.showWarning(message);
export const showInfo = (message) => ErrorHandler.showInfo(message);

// 全局错误处理 - 添加防护机制防止无限循环
let isHandlingGlobalError = false;
let globalErrorCount = 0;
const MAX_GLOBAL_ERRORS = 5;

window.addEventListener('error', (event) => {
  // 防止无限循环
  if (isHandlingGlobalError) {
    console.error('Prevented infinite error loop:', event.error);
    return;
  }

  // 限制错误处理次数
  globalErrorCount++;
  if (globalErrorCount > MAX_GLOBAL_ERRORS) {
    console.error('Too many global errors, stopping error handling:', event.error);
    return;
  }

  isHandlingGlobalError = true;

  try {
    ErrorHandler.handleError(event.error, ErrorTypes.SYSTEM, ErrorSeverity.HIGH, {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    });
  } catch (handlerError) {
    console.error('Error in global error handler:', handlerError);
  } finally {
    isHandlingGlobalError = false;
    // 重置计数器（延迟重置，避免短时间内大量错误）
    setTimeout(() => {
      if (globalErrorCount > 0) {
        globalErrorCount = Math.max(0, globalErrorCount - 1);
      }
    }, 5000);
  }
});

window.addEventListener('unhandledrejection', (event) => {
  // 防止无限循环
  if (isHandlingGlobalError) {
    console.error('Prevented infinite rejection loop:', event.reason);
    return;
  }

  isHandlingGlobalError = true;

  try {
    ErrorHandler.handleError(event.reason, ErrorTypes.SYSTEM, ErrorSeverity.HIGH, {
      type: 'unhandledPromiseRejection'
    });
  } catch (handlerError) {
    console.error('Error in unhandled rejection handler:', handlerError);
  } finally {
    isHandlingGlobalError = false;
  }
});

export default ErrorHandler;
