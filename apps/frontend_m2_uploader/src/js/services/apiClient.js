import axios from 'axios';
import { API_BASE_URL, MEDIA_BASE_URL, baseConfig } from '../config';
import jwtManager from './jwtManager';
import ErrorHandler, { ErrorTypes, ErrorSeverity } from '../utils/errorHandler';

// Create an axios instance with configuration from unified config system
const apiClient = axios.create({
  baseURL: API_BASE_URL, // 使用便捷访问器保持向后兼容
  timeout: baseConfig.requestTimeout, // 使用配置化的超时时间
  headers: {
    // 不设置默认的Content-Type，让每个请求根据数据类型自动设置
    'Accept': 'application/json'
  }
});

// 请求拦截器 - 自动添加 JWT token
apiClient.interceptors.request.use(
  async config => {
    // 自动获取并添加 JWT token
    try {
      const token = await jwtManager.getValidJWT();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      // 使用统一错误处理记录JWT获取失败
      ErrorHandler.handleError(error, ErrorTypes.AUTHENTICATION, ErrorSeverity.LOW, {
        context: 'JWT token retrieval failed',
        continueRequest: true
      });
      // 继续发送请求，让服务器处理未认证的情况
    }

    // 根据数据类型设置正确的Content-Type
    if (config.data instanceof FormData) {
      // 如果是FormData，删除Content-Type让浏览器自动设置为multipart/form-data
      delete config.headers['Content-Type'];
    } else if (config.data && typeof config.data === 'object') {
      // 如果是普通对象，设置为application/json
      config.headers['Content-Type'] = 'application/json';
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理认证失败和自动重试
apiClient.interceptors.response.use(
  response => {
    return response.data;
  },
  async error => {
    const originalRequest = error.config;

    // 处理 401 认证失败
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // 尝试刷新 JWT
        const refreshed = await jwtManager.refreshJWT();
        if (refreshed) {
          // 重新获取 token 并重试请求
          const token = await jwtManager.getValidJWT();
          if (token) {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return apiClient(originalRequest);
          }
        }
      } catch (refreshError) {
        // 使用统一错误处理记录JWT刷新失败
        ErrorHandler.handleError(refreshError, ErrorTypes.AUTHENTICATION, ErrorSeverity.HIGH, {
          context: 'JWT refresh failed',
          originalRequest: originalRequest.url
        });
        // 刷新失败，清除 tokens
        jwtManager.logout();
      }
    }

    // 使用统一错误处理处理其他错误
    ErrorHandler.handleNetworkError(error, {
      url: originalRequest?.url,
      method: originalRequest?.method,
      data: originalRequest?.data
    });
    return Promise.reject(error);
  }
);

// A helper function to extract user-friendly error messages from API responses.
const getApiErrorMessage = (error) => {
  if (error.response && error.response.data && error.response.data.err) {
    // This is our custom error format from the Go backend {ok, err, data}
    return error.response.data.err;
  }
  // Fallback for network errors or other unexpected structures
  return error.message || 'An unexpected error occurred.';
};



/**
 * 获取视频列表
 * @param {Object} params - 查询参数 (page, limit, status, etc.)
 * @returns {Promise<Object>} 返回视频列表数据
 */
export const getVideos = async (params = {}) => {
  try {
    const response = await apiClient.get('/video/admin/videos/', { params });
    if (response && response.ok && response.data) {
      return response.data;
    }
    console.warn('getVideos response is not as expected:', response);
    return { items: [], pgn: {} };
  } catch (error) {
    console.error('Failed to fetch videos:', error);
    return { items: [], pgn: {} };
  }
};

/**
 * 获取聚合统计数据
 * @param {Object} params - 查询参数 (status, categoryId, etc.)
 * @returns {Promise<Object>} 返回统计数据
 */
export const getStats = async (params = {}) => {
  try {
    const response = await apiClient.get('/video/admin/videos/stats', { params });
    if (response && response.ok && response.data) {
      return response.data;
    }
    console.warn('getStats response is not as expected:', response);
    return {}; // Return empty object on failure
  } catch (error) {
    console.error('Failed to fetch stats:', error);
    return {};
  }
};

/**
 * 获取分类列表 (模拟)
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 返回分类列表数据
 */
export const getCategoryList = async (params = {}) => {
  try {
    // The interceptor already handles response.data, so we get the body directly.
    const response = await apiClient.get('/video/admin/categories/', { params });
    
    // Check if the API call was successful and data exists
    if (response && response.ok && response.data && Array.isArray(response.data.items)) {
      // Map the response to consistent field names
      return {
        items: response.data.items, // Categories already have correct field names
        pgn: {
          totalItems: response.data.pgn.totItms,
          totalPages: response.data.pgn.totPgs,
          currentPage: response.data.pgn.currPg,
          limit: response.data.pgn.lim,
        },
      };
    } else {
      console.warn('getCategoryList response is not as expected:', response);
      // Return a default structure on failure to prevent UI errors
      return { items: [], pgn: {} };
    }
  } catch (error) {
    console.error('Failed to fetch category list:', error);
    // Return a default structure on network/request error
    return { items: [], pgn: {} };
  }
};

/**
 * 获取客户列表 (模拟)
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 返回客户列表数据
 */
export const getClientList = async (params = {}) => {
  try {
    const response = await apiClient.get('/video/admin/advertisers', { params });
    
    if (response && response.ok && response.data && Array.isArray(response.data.items)) {
      // The backend for advertisers returns short names, so we map them to more descriptive frontend names.
      const mappedItems = response.data.items.map(item => {
        // Construct the full avatar URL by prepending the MEDIA base URL if the avatarUrl is a relative path.
        // If avatarUrl is missing or empty, use a default SVG icon.
        let finalAvatarUrl = '/icons/head/head.svg'; // Default avatar
        if (item.avatarUrl) {
          // Check if it's already a full URL
          if (!item.avatarUrl.startsWith('http')) {
            finalAvatarUrl = `${MEDIA_BASE_URL}${item.avatarUrl}`;
          } else {
            finalAvatarUrl = item.avatarUrl;
          }
        }
        
        return {
          ...item,
          name: item.nm,
          email: item.em,
          phone: item.ph,
          memo: item.rem,
          profilePictureUrl: finalAvatarUrl
        };
      });
      
      return {
        items: mappedItems,
        pgn: {
          totalItems: response.data.pgn.totItms,
          totalPages: response.data.pgn.totPgs,
          currentPage: response.data.pgn.currPg,
          limit: response.data.pgn.lim,
        },
      };
    } else {
      console.warn('getClientList response is not as expected:', response);
      return { items: [], pgn: {} };
    }
  } catch (error) {
    console.error('Failed to fetch client list:', error);
    return { items: [], pgn: {} };
  }
};

/**
 * Searches for MLS properties based on a keyword.
 * @param {string} keyword - The search term.
 * @returns {Promise<Array>} A promise that resolves to an array of property objects.
 */
export const searchMlsProperties = async (keyword) => {
  try {
    const response = await apiClient.post('/video/admin/properties/search', {
      s: keyword
    });
    // According to the response structure {ok, msg, data}, we return data.
    if (response && response.ok && Array.isArray(response.data)) {
      return response.data;
    }
    console.warn('searchMlsProperties response is not as expected:', response);
    return []; // Return empty array on failure or unexpected format
  } catch (error) {
    console.error('Failed to search MLS properties:', error);
    // In case of error, return an empty array to prevent breaking the calling code.
    return [];
  }
};

/**
 * Creates a new video entry by uploading form data.
 * @param {FormData} formData - The form data containing video details, files, etc.
 * @returns {Promise<Object>} A promise that resolves to the newly created video object.
 */
export const createVideo = async (formData) => {
  try {
    const response = await apiClient.post('/video/admin/videos/', formData, {
      // 不设置Content-Type，让拦截器根据FormData自动处理
    });
    if (response && response.ok) {
      return response.data;
    } else {
      throw new Error(response?.msg || 'Failed to create video.');
    }
  } catch (error) {
    throw new Error(getApiErrorMessage(error));
  }
};

// === 分块上传相关API ===

/**
 * 初始化分块上传
 * @param {string} filename - 文件名
 * @param {number} totalSize - 文件总大小
 * @param {string} fileType - 文件类型 ("video" 或 "thumbnail")
 * @returns {Promise<Object>} 包含uploadId的响应
 */
export const initiateChunkedUpload = async (filename, totalSize, fileType = "video") => {
  try {
    const response = await apiClient.post('/video/admin/videos/chunked-upload/initiate', {
      filename,
      totalSize,
      fileType
    });
    if (response && response.ok) {
      return response.data;
    } else {
      throw new Error(response?.msg || 'Failed to initiate chunked upload.');
    }
  } catch (error) {
    throw new Error(getApiErrorMessage(error));
  }
};

/**
 * 上传单个分块
 * @param {string} uploadId - 上传ID
 * @param {number} chunkNumber - 分块编号
 * @param {Blob} chunkData - 分块数据
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise<Object>} 上传结果
 */
export const uploadChunk = async (uploadId, chunkNumber, chunkData, onProgress, signal) => {
  try {
    // 将分块数据包装为FormData格式，后端期望multipart/form-data
    const formData = new FormData();
    // 直接使用chunkData，它已经是一个Blob对象（来自file.slice()）
    // 不需要再用new Blob([chunkData])包装，这会导致数据格式错误
    formData.append('chunk', chunkData);

    const response = await apiClient.post(
      `/video/admin/videos/chunked-upload/${uploadId}/chunk/${chunkNumber}`,
      formData,
      {
        // 不设置Content-Type，让浏览器自动设置为multipart/form-data
        onUploadProgress: onProgress,
        signal,
      }
    );
    if (response && response.ok) {
      return response.data;
    } else {
      throw new Error(response?.msg || 'Failed to upload chunk.');
    }
  } catch (error) {
    throw new Error(getApiErrorMessage(error));
  }
};

/**
 * 完成分块上传
 * @param {string} uploadId - 上传ID
 * @param {number} expectedChunks - 期望的分块数
 * @returns {Promise<Object>} 完成结果，包含goupload路径
 */
export const completeChunkedUpload = async (uploadId, expectedChunks) => {
  try {
    const response = await apiClient.post('/video/admin/videos/chunked-upload/complete', {
      uploadId,
      expectedChunks
    });
    if (response && response.ok) {
      return response.data;
    } else {
      throw new Error(response?.msg || 'Failed to complete chunked upload.');
    }
  } catch (error) {
    throw new Error(getApiErrorMessage(error));
  }
};

/**
 * 上传缩略图文件（用于分块上传场景）
 * @param {File} thumbnailFile - 缩略图文件
 * @returns {Promise<Object>} 上传结果，包含path字段
 */
export const uploadThumbnail = async (thumbnailFile) => {
  try {
    const formData = new FormData();
    formData.append('thumbnail', thumbnailFile);

    const response = await apiClient.post('/video/admin/videos/upload-thumbnail', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data; // 返回 { path, filename, size }
  } catch (error) {
    console.error('上传缩略图失败:', error);
    throw error;
  }
};

/**
 * 使用分块上传结果创建视频记录
 * @param {Object} metadata - 视频元数据，包含draftVideoGouploadPath
 * @returns {Promise<Object>} 创建的视频对象
 */
export const createVideoFromChunkedUpload = async (metadata) => {
  try {
    const response = await apiClient.post('/video/admin/videos/', metadata, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    if (response && response.ok) {
      return response.data;
    } else {
      throw new Error(response?.msg || 'Failed to create video from chunked upload.');
    }
  } catch (error) {
    throw new Error(getApiErrorMessage(error));
  }
};

/**
 * Creates a new advertiser (client) by uploading form data.
 * @param {FormData} formData - The form data containing client details and an avatar.
 * @returns {Promise<Object>} A promise that resolves to the newly created client object.
 */
export const createClient = async (formData) => {
  try {
    const response = await apiClient.post('/video/admin/advertisers', formData, {
      // 不设置Content-Type，让拦截器根据FormData自动处理
    });
    if (response && response.ok) {
      return response;
    } else {
      throw new Error(response?.msg || 'Failed to create client.');
    }
  } catch (error) {
    throw new Error(getApiErrorMessage(error));
  }
};

/**
 * Creates a new video category.
 * @param {Object} categoryData - The data for the new category.
 * @param {string} categoryData.name - The name of the category.
 * @returns {Promise<Object>} A promise that resolves to the newly created category object.
 */
export const createCategory = async (categoryData) => {
  try {
    const response = await apiClient.post('/video/admin/categories/', categoryData);
    if (response && response.ok) {
      return response;
    } else {
      throw new Error(response?.msg || 'Failed to create category.');
    }
  } catch (error) {
    throw new Error(getApiErrorMessage(error));
  }
};

/**
 * Deletes a client. If a targetAdvertiserId is provided, the request will include it
 * in the body, asking the backend to merge videos before deleting.
 * @param {string} clientId - The ID of the client to delete.
 * @param {string|null} targetAdvertiserId - The ID of the client to merge videos into.
 * @returns {Promise<{ok: boolean, msg: string}>} A promise that resolves to the result of the operation.
 */
export const deleteClient = async (clientId, targetAdvertiserId = null) => {
  try {
    const config = {};
    if (targetAdvertiserId) {
      config.data = { target_advertiser_id: targetAdvertiserId };
    }
    const response = await apiClient.delete(`/video/admin/advertisers/${clientId}`, config);
    if (response && response.ok) {
      return { ok: true, msg: response.msg || 'Client deleted successfully.' };
    } else {
      // This path is less likely if the interceptor always throws.
      throw new Error(response?.msg || 'An unknown error occurred');
    }
  } catch (error) {
    throw new Error(getApiErrorMessage(error));
  }
};

/**
 * Deletes a category by its ID.
 * @param {string} categoryId - The ID of the category to delete.
 * @returns {Promise<Object>} A promise that resolves to the API response.
 */
export const deleteCategory = async (categoryId, targetCategoryId = null) => {
    try {
        const config = {};
        if (targetCategoryId) {
            config.data = { target_category_id: targetCategoryId };
        }
        const response = await apiClient.delete(`/video/admin/categories/${categoryId}`, config);
        if (response && response.ok) {
            return { ok: true, msg: response.msg || 'Category deleted successfully.' };
        } else {
            throw new Error(response?.msg || 'An unknown error occurred.');
        }
    } catch (error) {
        throw new Error(getApiErrorMessage(error));
    }
};

/**
 * Deletes a video by its ID.
 * @param {string} videoId - The ID of the video to delete.
 * @returns {Promise<Object>} A promise that resolves to the API response.
 */
export const deleteVideo = async (videoId) => {
  try {
    const response = await apiClient.delete(`/video/admin/videos/${videoId}`);
    if (response && response.ok) {
      return response;
    } else {
      throw new Error(response?.msg || 'Failed to delete video.');
    }
  } catch (error) {
    throw new Error(getApiErrorMessage(error));
  }
};

/**
 * Updates an existing advertiser (client) with form data.
 * @param {string} clientId - The ID of the client to update.
 * @param {FormData} formData - The form data containing the fields to update.
 * @returns {Promise<Object>} A promise that resolves to the API response.
 */
export const updateClient = async (clientId, formData) => {
  try {
    // Use PATCH for partial updates, as intended by the backend route
    const response = await apiClient.patch(`/video/admin/advertisers/${clientId}`, formData, {
      // 不设置Content-Type，让拦截器根据FormData自动处理
    });
    if (response && response.ok) {
      return response;
    } else {
      throw new Error(response?.msg || 'Failed to update client.');
    }
  } catch (error) {
    throw new Error(getApiErrorMessage(error));
  }
};

/**
 * Updates an existing category.
 * @param {string} categoryId - The ID of the category to update.
 * @param {Object} categoryData - The data for the updated category.
 * @param {string} categoryData.name - The new name of the category.
 * @returns {Promise<Object>} A promise that resolves to the API response.
 */
export const updateCategory = async (categoryId, categoryData) => {
  try {
    const response = await apiClient.put(`/video/admin/categories/${categoryId}`, categoryData);
     if (response && response.ok) {
      return response;
    } else {
      throw new Error(response?.msg || 'Failed to update category.');
    }
  } catch (error) {
    throw new Error(getApiErrorMessage(error));
  }
};

/**
 * Retrieves a single video by its ID.
 * @param {string} videoId - The ID of the video to fetch.
 * @returns {Promise<Object>} A promise that resolves to the video data.
 */
export const getVideoById = async (videoId) => {
  try {
    const response = await apiClient.get(`/video/admin/videos/${videoId}`);
    if (response && response.ok) {
      return response.data;
    } else {
      throw new Error(response?.msg || 'Failed to fetch video details.');
    }
  } catch (error) {
    throw new Error(getApiErrorMessage(error));
  }
};

/**
 * Updates an existing video with new data.
 * This can be a partial update (metadata) or include file uploads.
 * The backend will determine the exact action based on the 'action' field in the FormData.
 * @param {string} videoId - The ID of the video to update.
 * @param {FormData} formData - The FormData object containing updates.
 * @returns {Promise<Object>} A promise that resolves to the updated video object.
 */
export const updateVideo = async (videoId, formData) => {
    try {
        const response = await apiClient.patch(`/video/admin/videos/${videoId}`, formData, {
            // 不设置Content-Type，让拦截器根据FormData自动处理
        });
        if (response && response.ok) {
            return response.data;
        } else {
            throw new Error(response?.msg || 'Failed to update video.');
        }
    } catch (error) {
        throw new Error(getApiErrorMessage(error));
    }
};

/**
 * 使用分块上传结果更新视频记录
 * @param {string} videoId - 视频ID
 * @param {Object} metadata - 视频元数据，包含draftVideoGouploadPath等
 * @returns {Promise<Object>} 更新后的视频对象
 */
export const updateVideoFromChunkedUpload = async (videoId, metadata) => {
    try {
        const response = await apiClient.patch(`/video/admin/videos/${videoId}`, metadata, {
            headers: {
                'Content-Type': 'application/json',
            },
        });
        if (response && response.ok) {
            return response.data;
        } else {
            throw new Error(response?.msg || 'Failed to update video from chunked upload.');
        }
    } catch (error) {
        throw new Error(getApiErrorMessage(error));
    }
};

export const publishVideo = async (videoId) => {
    try {
        const response = await apiClient.post(`/video/admin/videos/${videoId}/publish`);
        if (response && response.ok) {
            return response;
        } else {
            throw new Error(response?.msg || 'Failed to publish video.');
        }
    } catch (error) {
        throw new Error(getApiErrorMessage(error));
    }
};

/**
 * 获取当前用户信息（从 JWT 中解析）
 * @returns {Object|null} 用户信息或 null
 */
export const getCurrentUser = () => {
  return jwtManager.getUserInfo();
};

/**
 * 手动刷新 JWT token
 * @returns {Promise<boolean>} 是否刷新成功
 */
export const refreshAuth = async () => {
  return await jwtManager.refreshJWT();
};

/**
 * 登出（清除所有认证信息）
 */
export const logout = () => {
  jwtManager.logout();
};

/**
 * 检查是否已认证
 * @returns {boolean} 是否有有效的 JWT
 */
export const isAuthenticated = () => {
  const userInfo = jwtManager.getUserInfo();
  return userInfo !== null;
};

export default {
  getVideos,
  getStats,
  getCategoryList,
  getClientList,
  searchMlsProperties,
  createVideo,
  // 分块上传相关
  uploadThumbnail,
  initiateChunkedUpload,
  uploadChunk,
  completeChunkedUpload,
  createVideoFromChunkedUpload,
  createClient,
  createCategory,
  deleteClient,
  deleteCategory,
  deleteVideo,
  updateClient,
  updateCategory,
  getVideoById,
  updateVideo,
  updateVideoFromChunkedUpload,
  publishVideo,
  // JWT 相关功能
  getCurrentUser,
  refreshAuth,
  logout,
  isAuthenticated,
};