/**
 * 应用程序统一配置管理
 * 遵循驼峰命名规范，支持环境变量覆盖
 *
 * <AUTHOR> Uploader Team
 * @version 1.0.0
 */

// ==================== 基础配置 ====================
export const baseConfig = {
  // API 服务配置（优先根 .env，其次 Vite 前缀，最后开发回退）
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL ?? import.meta.env.API_BASE_URL ?? 'http://localhost:8080',
  mediaBaseUrl: import.meta.env.VITE_MEDIA_BASE_URL ?? import.meta.env.MEDIA_BASE_URL ?? 'http://localhost:3000',

  // 请求配置
  requestTimeout: parseInt(import.meta.env.VITE_REQUEST_TIMEOUT ?? import.meta.env.REQUEST_TIMEOUT) || 10000,
  maxRetries: parseInt(import.meta.env.VITE_MAX_RETRIES ?? import.meta.env.MAX_RETRIES) || 3,

  // 应用信息
  appName: import.meta.env.VITE_APP_NAME || 'M2 Uploader',
  appVersion: import.meta.env.VITE_APP_VERSION || '1.0.0',
};

// 在模块加载时对生产环境关键变量进行提示
(() => {
  const isProd = import.meta.env.PROD;
  if (isProd) {
    if (!baseConfig.apiBaseUrl) {
      // 生产环境强提示
       
      console.error('Missing API_BASE_URL (or VITE_API_BASE_URL) for production. Configure it in root .env.production or CI.');
    }
  }
})();

// ==================== UI 配置 ====================
export const uiConfig = {
  // 分页配置
  pagination: {
    videosPerPage: parseInt(import.meta.env.VITE_VIDEOS_PER_PAGE) || 20,
    defaultPage: 1,
    maxPageSize: parseInt(import.meta.env.VITE_MAX_PAGE_SIZE) || 100,
  },

  // 轮询配置
  polling: {
    statusInterval: parseInt(import.meta.env.VITE_STATUS_POLLING_INTERVAL) || 10000,
    enableAutoRefresh: import.meta.env.VITE_ENABLE_AUTO_REFRESH !== 'false',
    maxPollingAttempts: parseInt(import.meta.env.VITE_MAX_POLLING_ATTEMPTS) || 10,
  },

  // 消息提示配置
  notifications: {
    errorDisplayDuration: parseInt(import.meta.env.VITE_ERROR_DISPLAY_DURATION) || 3000,
    successDisplayDuration: parseInt(import.meta.env.VITE_SUCCESS_DISPLAY_DURATION) || 2000,
    warningDisplayDuration: parseInt(import.meta.env.VITE_WARNING_DISPLAY_DURATION) || 2500,
    infoDisplayDuration: parseInt(import.meta.env.VITE_INFO_DISPLAY_DURATION) || 4000,
    autoHideNotifications: import.meta.env.VITE_AUTO_HIDE_NOTIFICATIONS !== 'false',
    // 默认消息文本
    defaultErrorMessage: import.meta.env.VITE_DEFAULT_ERROR_MESSAGE || '操作失败，请稍后重试',
    defaultSuccessMessage: import.meta.env.VITE_DEFAULT_SUCCESS_MESSAGE || '操作成功',
    defaultWarningMessage: import.meta.env.VITE_DEFAULT_WARNING_MESSAGE || '请注意',
    defaultInfoMessage: import.meta.env.VITE_DEFAULT_INFO_MESSAGE || '提示信息',
    // 错误处理配置
    enableGlobalErrorHandler: import.meta.env.VITE_ENABLE_GLOBAL_ERROR_HANDLER !== 'false',
    enableErrorReporting: import.meta.env.VITE_ENABLE_ERROR_REPORTING === 'true',
    maxErrorsPerSession: parseInt(import.meta.env.VITE_MAX_ERRORS_PER_SESSION) || 10,
  },

  // 模态框配置
  modal: {
    closeOnOverlayClick: import.meta.env.VITE_MODAL_CLOSE_ON_OVERLAY !== 'false',
    enableEscapeKey: import.meta.env.VITE_MODAL_ENABLE_ESCAPE !== 'false',
    animationDuration: parseInt(import.meta.env.VITE_MODAL_ANIMATION_DURATION) || 300,
  },

  // 加载状态配置
  loading: {
    showLoadingSpinner: import.meta.env.VITE_SHOW_LOADING_SPINNER !== 'false',
    minLoadingTime: parseInt(import.meta.env.VITE_MIN_LOADING_TIME) || 500,
    loadingTimeout: parseInt(import.meta.env.VITE_LOADING_TIMEOUT) || 30000,
  },
};

// ==================== 文件上传配置 ====================
export const uploadConfig = {
  // 分块上传配置
  chunkedUpload: {
    chunkSize: parseInt(import.meta.env.VITE_CHUNK_SIZE) || 5 * 1024 * 1024, // 5MB
    maxRetries: parseInt(import.meta.env.VITE_UPLOAD_MAX_RETRIES) || 3,
    threshold: parseInt(import.meta.env.VITE_CHUNKED_UPLOAD_THRESHOLD) || 10 * 1024 * 1024, // 10MB
    retryDelay: parseInt(import.meta.env.VITE_UPLOAD_RETRY_DELAY) || 1000,
    parallelUploads: parseInt(import.meta.env.VITE_PARALLEL_UPLOADS) || 3,
  },

  // 文件限制配置
  fileLimits: {
    maxVideoSize: parseInt(import.meta.env.VITE_MAX_VIDEO_SIZE) || 2 * 1024 * 1024 * 1024, // 2GB
    maxImageSize: parseInt(import.meta.env.VITE_MAX_IMAGE_SIZE) || 50 * 1024 * 1024, // 50MB
    allowedVideoTypes: (import.meta.env.VITE_ALLOWED_VIDEO_TYPES || 'mp4,avi,mov,wmv,mkv,flv').split(','),
    allowedImageTypes: (import.meta.env.VITE_ALLOWED_IMAGE_TYPES || 'jpg,jpeg,png,gif,webp,bmp').split(','),
    maxFileNameLength: parseInt(import.meta.env.VITE_MAX_FILE_NAME_LENGTH) || 255,
  },

  // 进度显示配置
  progress: {
    updateInterval: parseInt(import.meta.env.VITE_PROGRESS_UPDATE_INTERVAL) || 100,
    showPercentage: import.meta.env.VITE_SHOW_PROGRESS_PERCENTAGE !== 'false',
    showSpeed: import.meta.env.VITE_SHOW_UPLOAD_SPEED !== 'false',
    showEta: import.meta.env.VITE_SHOW_UPLOAD_ETA !== 'false',
    showFileSize: import.meta.env.VITE_SHOW_FILE_SIZE !== 'false',
  },

  // 预览配置
  preview: {
    enableVideoPreview: import.meta.env.VITE_ENABLE_VIDEO_PREVIEW !== 'false',
    enableImagePreview: import.meta.env.VITE_ENABLE_IMAGE_PREVIEW !== 'false',
    maxPreviewSize: parseInt(import.meta.env.VITE_MAX_PREVIEW_SIZE) || 100 * 1024 * 1024, // 100MB
    previewTimeout: parseInt(import.meta.env.VITE_PREVIEW_TIMEOUT) || 10000,
  },
};

// ==================== JWT 认证配置 ====================
export const authConfig = {
  // Token 存储配置
  tokenStorage: {
    jwtKey: import.meta.env.VITE_JWT_STORAGE_KEY || 'dev_jwt',
    refreshTokenKey: import.meta.env.VITE_REFRESH_TOKEN_STORAGE_KEY || 'dev_refresh_token',
    userTypeKey: import.meta.env.VITE_USER_TYPE_STORAGE_KEY || 'dev_user_type',
    userInfoKey: import.meta.env.VITE_USER_INFO_STORAGE_KEY || 'dev_user_info',
  },

  // 刷新策略配置
  refreshStrategy: {
    refreshBeforeExpiry: parseInt(import.meta.env.VITE_JWT_REFRESH_BEFORE_EXPIRY) || 300, // 5分钟
    maxRefreshAttempts: parseInt(import.meta.env.VITE_MAX_REFRESH_ATTEMPTS) || 3,
    refreshRetryDelay: parseInt(import.meta.env.VITE_REFRESH_RETRY_DELAY) || 1000,
    autoRefresh: import.meta.env.VITE_AUTO_REFRESH_JWT !== 'false',
  },

  // 开发环境配置
  development: {
    enableDevJwt: import.meta.env.VITE_ENABLE_DEV_JWT !== 'false',
    defaultUserType: import.meta.env.VITE_DEFAULT_USER_TYPE || 'default',
    jwtExpiration: parseInt(import.meta.env.VITE_DEV_JWT_EXPIRATION) || 24 * 3600, // 24小时
    mockUsers: (import.meta.env.VITE_MOCK_USERS || 'admin,user,realtor').split(','),
  },

  // 安全配置
  security: {
    enableCsrfProtection: import.meta.env.VITE_ENABLE_CSRF_PROTECTION !== 'false',
    csrfTokenName: import.meta.env.VITE_CSRF_TOKEN_NAME || 'X-CSRF-Token',
    requireHttps: import.meta.env.VITE_REQUIRE_HTTPS === 'true',
  },
};

// ==================== 业务配置 ====================
export const businessConfig = {
  // 搜索配置
  search: {
    minSearchLength: parseInt(import.meta.env.VITE_MIN_SEARCH_LENGTH) || 2,
    searchDebounceDelay: parseInt(import.meta.env.VITE_SEARCH_DEBOUNCE_DELAY) || 300,
    maxSearchResults: parseInt(import.meta.env.VITE_MAX_SEARCH_RESULTS) || 10,
    enableSearchHistory: import.meta.env.VITE_ENABLE_SEARCH_HISTORY !== 'false',
    maxSearchHistoryItems: parseInt(import.meta.env.VITE_MAX_SEARCH_HISTORY_ITEMS) || 10,
  },

  // 翻译配置
  translation: {
    apiUrl: import.meta.env.VITE_TRANSLATION_API_URL || 'https://api.mymemory.translated.net/get',
    defaultSourceLang: import.meta.env.VITE_DEFAULT_SOURCE_LANG || 'zh-CN',
    defaultTargetLang: import.meta.env.VITE_DEFAULT_TARGET_LANG || 'en-GB',
    requestTimeout: parseInt(import.meta.env.VITE_TRANSLATION_TIMEOUT) || 5000,
    enableTranslation: import.meta.env.VITE_ENABLE_TRANSLATION !== 'false',
    maxTranslationLength: parseInt(import.meta.env.VITE_MAX_TRANSLATION_LENGTH) || 1000,
  },

  // 视频播放配置
  videoPlayer: {
    autoplay: import.meta.env.VITE_VIDEO_AUTOPLAY === 'true',
    controls: import.meta.env.VITE_VIDEO_CONTROLS !== 'false',
    preload: import.meta.env.VITE_VIDEO_PRELOAD || 'metadata',
    muted: import.meta.env.VITE_VIDEO_MUTED === 'true',
    loop: import.meta.env.VITE_VIDEO_LOOP === 'true',
    volume: parseFloat(import.meta.env.VITE_VIDEO_DEFAULT_VOLUME) || 0.8,
  },

  // 分类管理配置
  categories: {
    maxCategoryNameLength: parseInt(import.meta.env.VITE_MAX_CATEGORY_NAME_LENGTH) || 50,
    maxCategoryDepth: parseInt(import.meta.env.VITE_MAX_CATEGORY_DEPTH) || 3,
    maxCategoryListLimit: parseInt(import.meta.env.VITE_MAX_CATEGORY_LIST_LIMIT) || 150, // 分类列表最大数量
    enableCategoryIcons: import.meta.env.VITE_ENABLE_CATEGORY_ICONS !== 'false',
    defaultCategoryColor: import.meta.env.VITE_DEFAULT_CATEGORY_COLOR || '#007bff',
  },

  // 客户管理配置
  clients: {
    maxClientNameLength: parseInt(import.meta.env.VITE_MAX_CLIENT_NAME_LENGTH) || 100,
    maxClientListLimit: parseInt(import.meta.env.VITE_MAX_CLIENT_LIST_LIMIT) || 999, // 客户列表最大数量
    enableClientAvatars: import.meta.env.VITE_ENABLE_CLIENT_AVATARS !== 'false',
    maxAvatarSize: parseInt(import.meta.env.VITE_MAX_AVATAR_SIZE) || 5 * 1024 * 1024, // 5MB
    allowedAvatarTypes: (import.meta.env.VITE_ALLOWED_AVATAR_TYPES || 'jpg,jpeg,png,gif').split(','),
    defaultAvatarService: import.meta.env.VITE_DEFAULT_AVATAR_SERVICE || 'https://i.pravatar.cc',
  },

  // MLS 房源配置
  mls: {
    enableMlsIntegration: import.meta.env.VITE_ENABLE_MLS_INTEGRATION !== 'false',
    maxPropertyIds: parseInt(import.meta.env.VITE_MAX_PROPERTY_IDS) || 10,
    searchTimeout: parseInt(import.meta.env.VITE_MLS_SEARCH_TIMEOUT) || 5000,
    cacheExpiration: parseInt(import.meta.env.VITE_MLS_CACHE_EXPIRATION) || 300000, // 5分钟
  },
};

// ==================== 开发配置 ====================
export const developmentConfig = {
  // 调试配置
  debug: {
    enableConsoleLog: import.meta.env.VITE_ENABLE_CONSOLE_LOG !== 'false',
    enablePerformanceLog: import.meta.env.VITE_ENABLE_PERFORMANCE_LOG === 'true',
    logLevel: import.meta.env.VITE_LOG_LEVEL || 'info', // debug, info, warn, error
    enableStackTrace: import.meta.env.VITE_ENABLE_STACK_TRACE === 'true',
    maxLogEntries: parseInt(import.meta.env.VITE_MAX_LOG_ENTRIES) || 1000,
  },

  // Mock 配置
  mock: {
    enableMockData: import.meta.env.VITE_ENABLE_MOCK_DATA === 'true',
    mockDelay: parseInt(import.meta.env.VITE_MOCK_DELAY) || 500,
    mockErrorRate: parseFloat(import.meta.env.VITE_MOCK_ERROR_RATE) || 0.1, // 10%
    enableMockAuth: import.meta.env.VITE_ENABLE_MOCK_AUTH === 'true',
  },

  // 热重载配置
  hotReload: {
    enableHotReload: import.meta.env.VITE_ENABLE_HOT_RELOAD !== 'false',
    reloadDelay: parseInt(import.meta.env.VITE_RELOAD_DELAY) || 100,
    enableCssHotReload: import.meta.env.VITE_ENABLE_CSS_HOT_RELOAD !== 'false',
  },

  // 开发工具配置
  devTools: {
    enableDevTools: import.meta.env.VITE_ENABLE_DEV_TOOLS === 'true',
    showPerformanceMetrics: import.meta.env.VITE_SHOW_PERFORMANCE_METRICS === 'true',
    enableApiMocking: import.meta.env.VITE_ENABLE_API_MOCKING === 'true',
    showDebugInfo: import.meta.env.VITE_SHOW_DEBUG_INFO === 'true',
  },
};

// ==================== 性能配置 ====================
export const performanceConfig = {
  // 缓存配置
  cache: {
    enableApiCache: import.meta.env.VITE_ENABLE_API_CACHE !== 'false',
    apiCacheExpiration: parseInt(import.meta.env.VITE_API_CACHE_EXPIRATION) || 300000, // 5分钟
    enableImageCache: import.meta.env.VITE_ENABLE_IMAGE_CACHE !== 'false',
    maxCacheSize: parseInt(import.meta.env.VITE_MAX_CACHE_SIZE) || 100 * 1024 * 1024, // 100MB
  },

  // 懒加载配置
  lazyLoading: {
    enableLazyLoading: import.meta.env.VITE_ENABLE_LAZY_LOADING !== 'false',
    lazyLoadingThreshold: parseInt(import.meta.env.VITE_LAZY_LOADING_THRESHOLD) || 100,
    enableImageLazyLoading: import.meta.env.VITE_ENABLE_IMAGE_LAZY_LOADING !== 'false',
    enableVideoLazyLoading: import.meta.env.VITE_ENABLE_VIDEO_LAZY_LOADING !== 'false',
  },

  // 优化配置
  optimization: {
    enableGzip: import.meta.env.VITE_ENABLE_GZIP !== 'false',
    enableMinification: import.meta.env.VITE_ENABLE_MINIFICATION !== 'false',
    enableTreeShaking: import.meta.env.VITE_ENABLE_TREE_SHAKING !== 'false',
    chunkSizeLimit: parseInt(import.meta.env.VITE_CHUNK_SIZE_LIMIT) || 500 * 1024, // 500KB
  },

  // 异步处理配置
  async: {
    defaultTimeout: parseInt(import.meta.env.VITE_DEFAULT_ASYNC_TIMEOUT) || 30000, // 30秒
    maxConcurrentRequests: parseInt(import.meta.env.VITE_MAX_CONCURRENT_REQUESTS) || 3,
    retryDelay: parseInt(import.meta.env.VITE_RETRY_DELAY) || 1000,
    enableRequestCancellation: import.meta.env.VITE_ENABLE_REQUEST_CANCELLATION !== 'false',
    enableLoadingStates: import.meta.env.VITE_ENABLE_LOADING_STATES !== 'false',
    debounceDelay: parseInt(import.meta.env.VITE_DEBOUNCE_DELAY) || 300,
    throttleDelay: parseInt(import.meta.env.VITE_THROTTLE_DELAY) || 300,
    batchSize: parseInt(import.meta.env.VITE_BATCH_SIZE) || 5,
  },
};

// ==================== 导出统一配置对象 ====================
export const appConfig = {
  base: baseConfig,
  ui: uiConfig,
  upload: uploadConfig,
  auth: authConfig,
  business: businessConfig,
  development: developmentConfig,
  performance: performanceConfig,
};

// ==================== 便捷访问器（向后兼容） ====================
// 为了向后兼容和便捷访问，保留原有的导出
export const API_BASE_URL = baseConfig.apiBaseUrl;
export const MEDIA_BASE_URL = baseConfig.mediaBaseUrl;

// 常用配置的便捷访问
export const REQUEST_TIMEOUT = baseConfig.requestTimeout;
export const VIDEOS_PER_PAGE = uiConfig.pagination.videosPerPage;
export const CHUNK_SIZE = uploadConfig.chunkedUpload.chunkSize;
export const POLLING_INTERVAL = uiConfig.polling.statusInterval;

// ==================== 配置验证函数 ====================
/**
 * 验证配置的有效性
 * @returns {Object} 验证结果 { isValid: boolean, errors: string[], warnings: string[] }
 */
export const validateConfig = () => {
  const errors = [];
  const warnings = [];

  // 验证必需的配置
  if (!baseConfig.apiBaseUrl) {
    errors.push('API_BASE_URL is required');
  }

  if (!baseConfig.mediaBaseUrl) {
    warnings.push('MEDIA_BASE_URL not configured, using default');
  }

  // 验证URL格式
  try {
    new URL(baseConfig.apiBaseUrl);
  } catch {
    errors.push('API_BASE_URL is not a valid URL');
  }

  try {
    new URL(baseConfig.mediaBaseUrl);
  } catch {
    warnings.push('MEDIA_BASE_URL is not a valid URL');
  }

  // 验证数值配置
  if (uploadConfig.chunkedUpload.chunkSize < 1024 * 1024) {
    warnings.push('Chunk size is less than 1MB, this may cause performance issues');
  }

  if (uploadConfig.chunkedUpload.chunkSize > 50 * 1024 * 1024) {
    warnings.push('Chunk size is greater than 50MB, this may cause memory issues');
  }

  if (uiConfig.polling.statusInterval < 1000) {
    warnings.push('Status polling interval is less than 1 second, this may cause performance issues');
  }

  if (uiConfig.pagination.videosPerPage > 100) {
    warnings.push('Videos per page is greater than 100, this may cause performance issues');
  }

  // 验证文件大小限制
  if (uploadConfig.fileLimits.maxVideoSize < uploadConfig.chunkedUpload.threshold) {
    errors.push('Max video size should be greater than chunked upload threshold');
  }

  // 验证认证配置
  if (authConfig.refreshStrategy.refreshBeforeExpiry < 60) {
    warnings.push('JWT refresh before expiry is less than 1 minute, this may cause frequent refreshes');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

// ==================== 环境检测 ====================
export const environment = {
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
  mode: import.meta.env.MODE,
  baseUrl: import.meta.env.BASE_URL,
  command: import.meta.env.COMMAND, // 'build' | 'serve'
};

// ==================== 配置初始化和验证 ====================
/**
 * 初始化配置并进行验证
 * 在应用启动时调用
 */
export const initializeConfig = () => {
  const validation = validateConfig();

  if (environment.isDevelopment) {
    console.group('🔧 Configuration Validation');

    if (validation.isValid) {
      console.log('✅ Configuration is valid');
    } else {
      console.error('❌ Configuration validation failed');
      validation.errors.forEach(error => console.error(`  • ${error}`));
    }

    if (validation.warnings.length > 0) {
      console.warn('⚠️ Configuration warnings:');
      validation.warnings.forEach(warning => console.warn(`  • ${warning}`));
    }

    console.log('📋 Current configuration:', {
      apiBaseUrl: baseConfig.apiBaseUrl,
      mediaBaseUrl: baseConfig.mediaBaseUrl,
      environment: environment.mode,
      videosPerPage: uiConfig.pagination.videosPerPage,
      chunkSize: `${Math.round(uploadConfig.chunkedUpload.chunkSize / 1024 / 1024)}MB`,
      pollingInterval: `${uiConfig.polling.statusInterval}ms`,
    });

    console.groupEnd();
  }

  return validation;
};

// ==================== 配置工具函数 ====================
/**
 * 获取格式化的文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化的文件大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) {return '0 Bytes';}
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 获取配置值的类型安全访问器
 * @param {string} path - 配置路径，如 'ui.pagination.videosPerPage'
 * @param {any} defaultValue - 默认值
 * @returns {any} 配置值
 */
export const getConfigValue = (path, defaultValue = null) => {
  const keys = path.split('.');
  let current = appConfig;

  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return defaultValue;
    }
  }

  return current;
};

// ==================== 默认导出 ====================
export default appConfig;