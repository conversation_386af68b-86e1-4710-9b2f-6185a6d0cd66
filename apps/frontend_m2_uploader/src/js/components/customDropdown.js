import { uiConfig } from '../config';

// 全局缓存失效事件系统
const dropdownInstances = new Map(); // 存储所有下拉菜单实例

// 缓存失效事件
const CACHE_INVALIDATION_EVENTS = {
    CATEGORIES_CHANGED: 'categories-changed',
    CLIENTS_CHANGED: 'clients-changed'
};

// 发送缓存失效事件（支持跨标签页）
export const invalidateDropdownCache = (eventType) => {
    // 本标签页内的事件
    document.dispatchEvent(new CustomEvent(eventType));

    // 跨标签页通信：通过localStorage触发storage事件
    const storageKey = `dropdown-cache-invalidation-${eventType}`;
    const timestamp = Date.now();
    localStorage.setItem(storageKey, timestamp.toString());

    // 立即清除，避免localStorage积累过多数据
    setTimeout(() => {
        localStorage.removeItem(storageKey);
    }, 100);
};

// 根据API函数判断事件类型
const getEventTypeForAPI = (apiFetchFunction) => {
    const funcString = apiFetchFunction.toString();
    if (funcString.includes('getCategoryList')) {
        return CACHE_INVALIDATION_EVENTS.CATEGORIES_CHANGED;
    } else if (funcString.includes('getClientList')) {
        return CACHE_INVALIDATION_EVENTS.CLIENTS_CHANGED;
    }
    return null;
};

export const setupCustomDropdown = (containerOrId, fetchOptionsFn, options = {}) => {
    const container = typeof containerOrId === 'string' ? document.getElementById(containerOrId) : containerOrId;
    if (!container) {return;}

    const selectedValueSpan = container.querySelector('.selected-value');
    const optionsList = container.querySelector('.options-list');
    let areItemsLoaded = false;
    let itemsCache = []; // Cache the loaded items

    // Function to close the dropdown
    const closeDropdown = () => container.classList.remove('open');

    // 刷新缓存的函数
    const refreshCache = async () => {
        areItemsLoaded = false;
        itemsCache = [];
        if (container.classList.contains('open')) {
            // 如果下拉菜单当前是打开的，重新加载数据
            optionsList.innerHTML = '<li>Loading...</li>';
            try {
                const data = await fetchOptionsFn({ limit: uiConfig.pagination.maxPageSize });
                itemsCache = data.items;
                populateOptions();
                areItemsLoaded = true;
            } catch (error) {
                console.error('Failed to refresh items for dropdown:', error);
                optionsList.innerHTML = '<li>Error loading</li>';
            }
        }
    };
    
    const populateOptions = () => {
        optionsList.innerHTML = ''; // Clear existing
        let initialItemFound = false;
        
        // Add "None" option if needed
        if (options.addNoneOption) {
            const noneOption = document.createElement('li');
            noneOption.textContent = 'None';
            noneOption.dataset.value = '';
            optionsList.appendChild(noneOption);
            // If the dropdown's initialId is empty or not set, 'None' should be selected.
            if (!container.dataset.initialId) {
                 noneOption.classList.add('selected');
                 selectedValueSpan.textContent = 'None';
                 initialItemFound = true;
            }
        }

        // Populate with items from cache
        itemsCache.forEach(item => {
            const option = document.createElement('li');
            option.textContent = item.name;
            option.dataset.value = item.id;
            optionsList.appendChild(option);

            if (container.dataset.initialId === String(item.id)) {
                option.classList.add('selected');
                selectedValueSpan.textContent = item.name;
                container.dataset.value = item.id;
                initialItemFound = true;
            }
        });

        // If no initial item was found and we don't have a 'None' option, select the first item.
        if (!initialItemFound && itemsCache.length > 0 && !options.addNoneOption) {
            const firstOption = optionsList.querySelector('li');
            if (firstOption) {
                firstOption.classList.add('selected');
                selectedValueSpan.textContent = firstOption.textContent;
                container.dataset.value = firstOption.dataset.value;
            }
        }
    };

    container.addEventListener('click', async (event) => {
        event.stopPropagation();
        if (event.target.closest('.dropdown-options')) {return;}

        const isOpen = container.classList.contains('open');

        if (!isOpen) {
            container.classList.add('open');
            if (!areItemsLoaded) {
                optionsList.innerHTML = '<li>Loading...</li>';
                try {
                    const data = await fetchOptionsFn({ limit: 100 });
                    itemsCache = data.items; // Cache the results
                    populateOptions(); // Populate based on the new data
                    areItemsLoaded = true;
                } catch (error) {
                    console.error('Failed to load items for dropdown:', error);
                    optionsList.innerHTML = '<li>Error loading</li>';
                }
            }
        } else {
            closeDropdown();
        }
    });

    optionsList.addEventListener('click', (event) => {
        if (event.target.tagName === 'LI') {
            const value = event.target.dataset.value;
            const text = event.target.textContent;
            
            selectedValueSpan.textContent = text;
            container.dataset.value = value;

            // Update selected class
            optionsList.querySelectorAll('li').forEach(li => li.classList.remove('selected'));
            event.target.classList.add('selected');
            
            closeDropdown();
        }
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', (event) => {
        if (!container.contains(event.target)) {
            closeDropdown();
        }
    });

    // 监听缓存失效事件
    const eventType = getEventTypeForAPI(fetchOptionsFn);
    if (eventType) {
        const eventHandler = refreshCache;

        // 监听本标签页内的事件
        document.addEventListener(eventType, eventHandler);

        // 监听跨标签页的storage事件
        const storageKey = `dropdown-cache-invalidation-${eventType}`;
        const storageHandler = (e) => {
            if (e.key === storageKey && e.newValue) {
                refreshCache();
            }
        };
        window.addEventListener('storage', storageHandler);

        // 存储实例信息（如果需要清理，可由调用方管理）
        // 此处不再按 dropdownId 存储，避免未使用变量
    }

    // If an initial ID is set, we might need to pre-fetch data.
    if (container.dataset.initialId) {
         (async () => {
             if (!areItemsLoaded) {
                try {
                    const data = await fetchOptionsFn({ limit: 100 });
                    itemsCache = data.items;
                    populateOptions();
                    areItemsLoaded = true;
                } catch(e) { console.error(e)}
             } else {
                 populateOptions();
             }
         })();
    }

    // 返回清理函数
    return {
        refresh: refreshCache,
        cleanup: () => {
            // 无状态清理，目前仅移除事件监听，如有需要可扩展
            // 注意：这里的事件是用匿名函数注册（事件类型常量），外部页面切换会刷新 DOM
        }
    };
};

// 清理所有下拉菜单实例的函数
export const cleanupAllDropdowns = () => {
    dropdownInstances.forEach((instance, _dropdownId) => {
        document.removeEventListener(instance.eventType, instance.eventHandler);
        if (instance.storageHandler) {
            window.removeEventListener('storage', instance.storageHandler);
        }
    });
    dropdownInstances.clear();
};