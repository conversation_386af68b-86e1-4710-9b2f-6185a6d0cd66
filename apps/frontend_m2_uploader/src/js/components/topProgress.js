export const initTopProgress = () => {
  const el = document.getElementById('top-upload-progress');
  if (!el) {
    return null;
  }
  const bar = el.querySelector('.top-progress__bar');
  const label = el.querySelector('.top-progress__label');
  return { el, bar, label };
};

let cached = null;
const getRefs = () => {
  if (!cached) {
    cached = initTopProgress();
  }
  return cached;
};

export const showTopProgress = () => {
  const refs = getRefs();
  if (!refs || !refs.el) return;
  refs.el.classList.remove('hidden');
  if (refs.bar) refs.bar.style.width = '0%';
  if (refs.label) refs.label.textContent = 'Uploading 0%';
};

export const updateTopProgress = (percentage) => {
  const refs = getRefs();
  if (!refs || !refs.el) return;
  const pct = Math.max(0, Math.min(100, Number(percentage) || 0));
  if (refs.bar) refs.bar.style.width = `${pct}%`;
  if (refs.label) refs.label.textContent = `Uploading ${pct.toFixed(1)}%`;
};

export const hideTopProgress = (delayMs = 600) => {
  const refs = getRefs();
  if (!refs || !refs.el) return;
  // 小延迟以便用户看到 100%
  setTimeout(() => {
    refs.el.classList.add('hidden');
    if (refs.bar) refs.bar.style.width = '0%';
    if (refs.label) refs.label.textContent = 'Uploading 0%';
  }, delayMs);
}; 