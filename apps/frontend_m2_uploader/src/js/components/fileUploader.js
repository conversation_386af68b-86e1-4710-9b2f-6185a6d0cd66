import ChunkedUploader from '../utils/chunkedUploader.js';

/**
 * Creates a stateful file uploader component.
 * @param {string} uploaderId The ID of the uploader container element.
 * @param {object} [options] Optional parameters.
 * @param {object} [options.existingMedia] Information about an already existing media file.
 * @param {string} [options.existingMedia.thumbUrl] The URL for the thumbnail image.
 * @param {string} [options.existingMedia.previewUrl] The URL for the full preview (video or full-size image).
 * @param {string} [options.existingMedia.name] The name of the existing media.
 * @param {function} [options.onPreview] Callback function to trigger the preview modal.
 * @param {function} [options.onFileChange] Callback function to trigger when a new file is selected.
 * @param {function} [options.onUploadProgress] Callback function for upload progress (for chunked uploads).
 * @returns {object} An object with methods to interact with the uploader.
 */
export const setupFileUploader = (uploaderId, options = {}) => {
    const uploader = document.getElementById(uploaderId);
    if (!uploader) {return null;}

    let currentFile = null; // Holds the new File object if user uploads one
    let existingMediaThumbUrl = options.existingMedia?.thumbUrl || null;
    let existingMediaPreviewUrl = options.existingMedia?.previewUrl || null;
    let currentObjectURL = null; // Track the current object URL for cleanup

    // 分块上传相关状态
    let chunkedUploader = null;
    let isChunkedUpload = false;
    let uploadProgress = 0;

    const fileInput = uploader.querySelector('.file-input');
    const uploaderBox = uploader.querySelector('.uploader-box');
    const fileDetails = uploader.querySelector('.file-details');
    const fileNameSpan = fileDetails.querySelector('.file-name');
    const previewImage = uploader.querySelector('.image-preview'); // Get the preview img tag
    const previewBtn = uploader.querySelector('.preview-btn');
    const deleteBtn = uploader.querySelector('.delete-btn');
    
    const showFileDetails = (name, imageUrl) => {
        fileNameSpan.textContent = name;

        // For video uploader, we always show the default video icon
        // For other uploaders (like cover), we might still use the preview image
        const videoDefaultIcon = fileDetails.querySelector('.video-default-icon');
        if (videoDefaultIcon) {
            // This is a video uploader, show default icon
            videoDefaultIcon.style.display = 'block';
            if (previewImage) {
                previewImage.style.display = 'none';
            }
        } else if (previewImage && imageUrl) {
            // This is not a video uploader, show preview image
            previewImage.src = imageUrl;
            previewImage.style.display = 'block';
        }

        fileNameSpan.style.display = 'none'; // Hide the file name span
        uploaderBox.classList.add('hidden');
        fileDetails.classList.remove('hidden');
        previewBtn.disabled = false;
    };

    const resetToInitialState = () => {
        // Clean up object URL to prevent memory leaks
        if (currentObjectURL) {
            URL.revokeObjectURL(currentObjectURL);
            currentObjectURL = null;
        }

        currentFile = null;
        fileInput.value = '';
        existingMediaThumbUrl = options.existingMedia?.thumbUrl || null;
        existingMediaPreviewUrl = options.existingMedia?.previewUrl || null;

        // Hide video default icon
        const videoDefaultIcon = fileDetails.querySelector('.video-default-icon');
        if (videoDefaultIcon) {
            videoDefaultIcon.style.display = 'none';
        }

        if (previewImage) {
            previewImage.src = '';
            previewImage.style.display = 'none';
        }
        fileNameSpan.style.display = 'block';
        uploaderBox.classList.remove('hidden'); // Ensure the upload box is visible again
        fileDetails.classList.add('hidden');
        previewBtn.disabled = true;
    };

    // Initialize with existing media if provided
    if (existingMediaThumbUrl) {
        showFileDetails(options.existingMedia.name || 'existing_file', existingMediaThumbUrl);
    }

    // Event Listeners
    uploaderBox.addEventListener('click', () => fileInput.click());

    fileInput.addEventListener('change', (event) => {
        const file = event.target.files[0];
        if (!file) {
            // If user cancels file selection, it might be considered a change.
            if (typeof options.onFileChange === 'function') {
                options.onFileChange(null);
            }
            return;
        }

        // Clean up previous object URL to prevent memory leaks
        if (currentObjectURL) {
            URL.revokeObjectURL(currentObjectURL);
            currentObjectURL = null;
        }

        currentFile = file;
        existingMediaPreviewUrl = null; // A new file overrides the existing one

        // Create a local URL for instant preview of the new file
        const localUrl = URL.createObjectURL(file);
        currentObjectURL = localUrl; // Track for cleanup
        showFileDetails(file.name, localUrl);

        if (typeof options.onFileChange === 'function') {
            options.onFileChange(file);
        }
    });

    deleteBtn.addEventListener('click', () => {
        const wasFilePreviouslySelected = !!currentFile;
        resetToInitialState();
        if (wasFilePreviouslySelected && typeof options.onFileChange === 'function') {
            options.onFileChange(null); // Notify that the file has been removed
        }
    });

    previewBtn.addEventListener('click', () => {
        if (typeof options.onPreview !== 'function') {return;}

        if (currentFile && currentObjectURL) {
            // Use the existing object URL instead of creating a new one
            options.onPreview(currentObjectURL);
        } else if (existingMediaPreviewUrl) {
            // Preview the existing media from its preview URL
            options.onPreview(existingMediaPreviewUrl);
        }
    });

    return {
        /** @returns {File|null} The new file object, or null if no new file was selected. */
        getFile: () => currentFile,
        reset: resetToInitialState,
        /** Clean up any object URLs to prevent memory leaks */
        cleanup: () => {
            if (currentObjectURL) {
                URL.revokeObjectURL(currentObjectURL);
                currentObjectURL = null;
            }
        },

        // 分块上传相关方法
        /** @returns {boolean} 是否需要使用分块上传 */
        shouldUseChunkedUpload: () => {
            return currentFile && ChunkedUploader.shouldUseChunkedUpload(currentFile);
        },

        /** 开始分块上传 */
        startChunkedUpload: async () => {
            if (!currentFile) {
                throw new Error('No file selected for chunked upload');
            }

            isChunkedUpload = true;
            chunkedUploader = new ChunkedUploader(currentFile, {
                onProgress: (percentage, uploadedBytes, totalBytes) => {
                    uploadProgress = percentage;
                    if (options.onUploadProgress) {
                        options.onUploadProgress(percentage, uploadedBytes, totalBytes);
                    }
                },
                onChunkProgress: (chunkNumber, chunkProgress) => {
                    console.log(`分块 ${chunkNumber} 进度: ${chunkProgress.toFixed(1)}%`);
                }
            });

            return await chunkedUploader.upload();
        },

        /** @returns {number} 获取上传进度 (0-100) */
        getUploadProgress: () => uploadProgress,

        /** 获取更详细的进度（包含字节数等） */
        getDetailedProgress: () => (chunkedUploader && typeof chunkedUploader.getProgress === 'function') ? chunkedUploader.getProgress() : null,

        /** @returns {boolean} 是否正在使用分块上传 */
        isUsingChunkedUpload: () => isChunkedUpload,

        /** 暂停分块上传 */
        pauseChunkedUpload: () => {
            if (chunkedUploader) {
                chunkedUploader.pause();
            }
        },

        /** 恢复分块上传 */
        resumeChunkedUpload: () => {
            if (chunkedUploader) {
                chunkedUploader.resume();
            }
        },

        /** 取消分块上传 */
        cancelChunkedUpload: () => {
            if (chunkedUploader) {
                chunkedUploader.cancel();
                chunkedUploader = null;
                isChunkedUpload = false;
                uploadProgress = 0;
            }
        }
    };
}; 