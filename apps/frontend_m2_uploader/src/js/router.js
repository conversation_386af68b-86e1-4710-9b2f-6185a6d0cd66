import { renderDashboard } from './pages/dashboard.js';
import { renderCategoryManager } from './pages/category-manager.js';
import { renderFilter } from './pages/filter.js';
import { renderClients } from './pages/clients.js';
import { renderUpload } from './pages/upload.js';
import { renderEditVideo } from './pages/edit-video.js';
import { renderDevTools, initDevTools } from './pages/dev-tools.js';
import { developmentConfig } from './config.js';

/**
 * 路由配置
 * @type {Object.<string, {viewName: string, templatePath: string, apiPath: string, apiParams?: Object}>}
 */
const routes = {
  '/': renderDashboard,
  '/dashboard': renderDashboard,
  '/categories': renderCategoryManager,
  '/filter': renderFilter,
  '/clients': renderClients,
  '/upload': renderUpload,
  '/edit-video/:id': renderEditVideo,
  '/dev-tools': renderDevTools,
};

// 默认路由
const defaultRoute = '/dashboard';

// 当前页面的清理函数
let currentPageCleanup = null;

/**
 * 获取当前路由的hash部分
 * @returns {string} 当前路由的hash
 */
const _getCurrentHash = () => {
  const hash = window.location.hash.slice(1) || '/';
  return hash;
};

/**
 * 获取路由配置
 * @param {string} hash - 路由hash
 * @returns {Object} 路由配置
 */
const _getRouteConfig = (hash) => {
  return routes[hash] || routes[defaultRoute];
};

/**
 * 处理路由变化
 * @param {string} hash - 路由hash
 */
const handleRouteChange = async () => {
    // 清理当前页面
    if (currentPageCleanup) {
        currentPageCleanup();
        currentPageCleanup = null;
    }

    const hash = window.location.hash.slice(1) || '/';

    // Find a matching route, including dynamic routes
    let match = null;
    for (const routePath in routes) {
        const routeRegex = new RegExp(`^${routePath.replace(/:\w+/g, '([\\w-]+)')}$`);
        const result = hash.match(routeRegex);

        if (result) {
            const params = {};
            const paramNames = (routePath.match(/:\w+/g) || []).map(name => name.substring(1));
            paramNames.forEach((name, index) => {
                params[name] = result[index + 1];
            });

            match = {
                render: routes[routePath],
                params: params,
            };
            break;
        }
    }

    // If a route is matched, render it. Otherwise, render the default.
    if (match) {
        const result = await match.render(match.params);
        // 如果页面返回了清理函数，保存它
        if (result && typeof result.cleanup === 'function') {
            currentPageCleanup = result.cleanup;
        }

        // 如果是开发工具页面，初始化开发工具功能
        if (hash === '/dev-tools') {
            setTimeout(() => initDevTools(), developmentConfig.hotReload.reloadDelay);
        }
    } else {
        const result = await routes[defaultRoute]();
        if (result && typeof result.cleanup === 'function') {
            currentPageCleanup = result.cleanup;
        }
    }
};

const handleNavigation = (event) => {
    const target = event.target.closest('[data-route]');
    if (target) {
        event.preventDefault();
        const route = target.getAttribute('data-route');
        navigateTo(route);
    }
};

/**
 * 初始化路由
 */
export const initializeRouter = () => {
  const appContainer = document.getElementById('app-container');
  if (!appContainer) {return;}

  appContainer.addEventListener('click', handleNavigation);
  window.addEventListener('hashchange', handleRouteChange);
  window.addEventListener('load', handleRouteChange); // Handle initial page load
};

/**
 * 导航到指定路由
 * @param {string} path - 目标路由路径
 */
export const navigateTo = (path) => {
  if (window.location.hash.slice(1) !== path) {
      window.location.hash = path;
  }
};

/**
 * 设置当前页面的清理函数
 * @param {Function} cleanupFn - 清理函数
 */
export const setPageCleanup = (cleanupFn) => {
  currentPageCleanup = cleanupFn;
};

export default {
  initializeRouter,
  navigateTo,
  setPageCleanup
};