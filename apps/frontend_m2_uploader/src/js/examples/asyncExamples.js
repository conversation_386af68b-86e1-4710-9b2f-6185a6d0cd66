/**
 * 异步处理规范化使用示例
 * 这个文件展示了如何正确使用异步处理工具
 */

import { asyncManager, debounce, throttle, batchProcess, ConcurrencyController } from '../utils/asyncUtils.js';
import { getVideos, createVideo } from '../services/apiClient.js';
import ErrorHandler, { ErrorTypes, ErrorSeverity } from '../utils/errorHandler.js';

// ==================== 基础异步操作示例 ====================

/**
 * 示例1: 基础API调用
 */
export const basicApiCallExample = async () => {
  try {
    const videos = await asyncManager.execute('loadVideos', async () => {
      return await getVideos({ page: 1, limit: 10 });
    }, {
      showLoading: true,
      loadingText: '加载视频列表中...',
      timeout: 10000,
      retries: 3
    });
    
    console.log('视频列表加载成功:', videos);
    return videos;
  } catch (error) {
    ErrorHandler.handleError(error, ErrorTypes.NETWORK, ErrorSeverity.MEDIUM, {
      context: 'Load videos failed'
    });
  }
};

/**
 * 示例2: 带状态监听的异步操作
 */
export const asyncWithStateExample = async () => {
  const statusElement = document.getElementById('operation-status');
  
  await asyncManager.execute('complexOperation', async () => {
    // 模拟复杂操作
    await new Promise(resolve => setTimeout(resolve, 3000));
    return { success: true, data: 'Operation completed' };
  }, {
    onStateChange: (status, _data) => {
      switch (status) {
        case 'loading':
          statusElement.textContent = '操作进行中...';
          statusElement.className = 'status loading';
          break;
        case 'success':
          statusElement.textContent = '操作成功完成';
          statusElement.className = 'status success';
          break;
        case 'error':
          statusElement.textContent = '操作失败';
          statusElement.className = 'status error';
          break;
      }
    }
  });
};

// ==================== 防抖和节流示例 ====================

/**
 * 示例3: 防抖搜索
 */
export const setupDebouncedSearch = () => {
  const searchInput = document.getElementById('search-input');
  const resultsContainer = document.getElementById('search-results');
  
  const debouncedSearch = debounce(async (query) => {
    if (!query.trim()) {
      resultsContainer.innerHTML = '';
      return;
    }
    
    try {
      const results = await asyncManager.execute('search', async () => {
        return await getVideos({ search: query });
      }, {
        cancelPrevious: true, // 取消之前的搜索
        showLoading: false,
        timeout: 5000
      });
      
      // 更新搜索结果
      resultsContainer.innerHTML = results.items.map(item => 
        `<div class="search-result">${item.title.zh}</div>`
      ).join('');
      
    } catch (error) {
      if (error.message !== 'Operation cancelled') {
        ErrorHandler.handleError(error, ErrorTypes.BUSINESS, ErrorSeverity.LOW, {
          context: 'Search failed',
          query
        });
      }
    }
  }, 300);
  
  searchInput.addEventListener('input', (e) => {
    debouncedSearch(e.target.value);
  });
};

/**
 * 示例4: 节流滚动处理
 */
export const setupThrottledScroll = () => {
  const throttledScrollHandler = throttle(() => {
    const scrollPosition = window.scrollY;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;
    
    // 检查是否接近底部
    if (scrollPosition + windowHeight >= documentHeight - 100) {
      loadMoreContent();
    }
  }, 200);
  
  window.addEventListener('scroll', throttledScrollHandler);
  
  // 清理函数
  return () => {
    window.removeEventListener('scroll', throttledScrollHandler);
  };
};

// ==================== 并发控制示例 ====================

/**
 * 示例5: 并发控制的文件上传
 */
export const concurrentUploadExample = async (files) => {
  const controller = new ConcurrencyController(3); // 最多3个并发上传
  const progressContainer = document.getElementById('upload-progress');
  
  const uploadPromises = files.map((file, index) => 
    controller.execute(async () => {
      const progressElement = document.createElement('div');
      progressElement.className = 'upload-item';
      progressElement.innerHTML = `
        <span>${file.name}</span>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 0%"></div>
        </div>
      `;
      progressContainer.appendChild(progressElement);
      
      try {
        const result = await asyncManager.execute(`upload-${index}`, async () => {
          // 模拟文件上传
          return await uploadFileWithProgress(file, (progress) => {
            const progressFill = progressElement.querySelector('.progress-fill');
            progressFill.style.width = `${progress}%`;
          });
        }, {
          timeout: 60000, // 1分钟超时
          retries: 2
        });
        
        progressElement.classList.add('success');
        return result;
      } catch (error) {
        progressElement.classList.add('error');
        throw error;
      }
    })
  );
  
  try {
    const results = await Promise.all(uploadPromises);
    console.log('所有文件上传完成:', results);
    return results;
  } catch (error) {
    ErrorHandler.handleError(error, ErrorTypes.FILE_UPLOAD, ErrorSeverity.HIGH, {
      context: 'Batch upload failed',
      fileCount: files.length
    });
  }
};

// ==================== 批处理示例 ====================

/**
 * 示例6: 批量数据处理
 */
export const batchProcessExample = async (dataArray) => {
  const { results, errors } = await batchProcess(
    dataArray,
    async (item, index) => {
      // 模拟处理单个数据项
      await new Promise(resolve => setTimeout(resolve, 100));
      
      if (Math.random() < 0.1) { // 10% 的失败率用于演示
        throw new Error(`Processing item ${index} failed`);
      }
      
      return { ...item, processed: true, processedAt: new Date().toISOString() };
    },
    {
      batchSize: 10,
      onProgress: (completed, total) => {
        const progressPercent = (completed / total) * 100;
        console.log(`批处理进度: ${progressPercent.toFixed(1)}%`);
        
        // 更新UI进度条
        const progressBar = document.getElementById('batch-progress');
        if (progressBar) {
          progressBar.style.width = `${progressPercent}%`;
        }
      },
      onBatchComplete: (batchResults, batchNumber) => {
        console.log(`批次 ${batchNumber} 完成:`, batchResults);
      },
      continueOnError: true // 即使有错误也继续处理
    }
  );
  
  console.log(`批处理完成: ${results.length} 成功, ${errors.length} 失败`);
  
  if (errors.length > 0) {
    console.warn('处理失败的项目:', errors);
  }
  
  return { results, errors };
};

// ==================== 复杂场景示例 ====================

/**
 * 示例7: 复杂的视频创建流程
 */
export const complexVideoCreationExample = async (videoData, videoFile, thumbnailFile) => {
  const steps = [
    { name: '验证数据', weight: 10 },
    { name: '上传缩略图', weight: 20 },
    { name: '上传视频文件', weight: 60 },
    { name: '创建视频记录', weight: 10 }
  ];
  
  let currentProgress = 0;
  const updateProgress = (stepProgress, stepIndex) => {
    const stepWeight = steps[stepIndex].weight;
    const baseProgress = steps.slice(0, stepIndex).reduce((sum, step) => sum + step.weight, 0);
    const totalProgress = baseProgress + (stepProgress * stepWeight / 100);
    
    const progressElement = document.getElementById('creation-progress');
    if (progressElement) {
      progressElement.style.width = `${totalProgress}%`;
    }
  };
  
  try {
    // 步骤1: 验证数据
    await asyncManager.execute('validateData', async () => {
      await new Promise(resolve => setTimeout(resolve, 500));
      if (!videoData.title || !videoFile) {
        throw new Error('缺少必要的视频数据');
      }
    });
    updateProgress(100, 0);
    
    // 步骤2: 上传缩略图
    let thumbnailUrl = null;
    if (thumbnailFile) {
      thumbnailUrl = await asyncManager.execute('uploadThumbnail', async () => {
        return await uploadFileWithProgress(thumbnailFile, (progress) => {
          updateProgress(progress, 1);
        });
      }, {
        timeout: 30000,
        retries: 3
      });
    } else {
      updateProgress(100, 1);
    }
    
    // 步骤3: 上传视频文件
    const videoUrl = await asyncManager.execute('uploadVideo', async () => {
      return await uploadFileWithProgress(videoFile, (progress) => {
        updateProgress(progress, 2);
      });
    }, {
      timeout: 300000, // 5分钟超时
      retries: 2
    });
    
    // 步骤4: 创建视频记录
    const video = await asyncManager.execute('createVideoRecord', async () => {
      return await createVideo({
        ...videoData,
        videoUrl,
        thumbnailUrl
      });
    });
    updateProgress(100, 3);
    
    console.log('视频创建成功:', video);
    return video;
    
  } catch (error) {
    ErrorHandler.handleError(error, ErrorTypes.BUSINESS, ErrorSeverity.HIGH, {
      context: 'Video creation failed',
      videoData,
      step: currentProgress
    });
    throw error;
  }
};

// ==================== 工具函数 ====================

/**
 * 模拟带进度的文件上传
 */
async function uploadFileWithProgress(file, onProgress) {
  return new Promise((resolve, reject) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 20;
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        onProgress(100);
        resolve({
          url: `https://example.com/uploads/${file.name}`,
          size: file.size,
          type: file.type
        });
      } else {
        onProgress(progress);
      }
    }, 200);
    
    // 模拟随机失败
    if (Math.random() < 0.05) { // 5% 失败率
      setTimeout(() => {
        clearInterval(interval);
        reject(new Error('Upload failed'));
      }, 1000);
    }
  });
}

/**
 * 模拟加载更多内容
 */
let currentPage = 1;
async function loadMoreContent() {
  try {
    await asyncManager.execute('loadMore', async () => {
      const moreData = await getVideos({ page: currentPage + 1 });
      appendToVideoList(moreData.items);
      currentPage++;
    }, {
      showLoading: true,
      loadingText: '加载更多内容...'
    });
  } catch (error) {
    ErrorHandler.handleError(error, ErrorTypes.NETWORK, ErrorSeverity.LOW);
  }
}

/**
 * 添加视频到列表
 */
function appendToVideoList(videos) {
  const container = document.getElementById('video-list');
  videos.forEach(video => {
    const element = document.createElement('div');
    element.className = 'video-item';
    element.innerHTML = `<h3>${video.title.zh}</h3>`;
    container.appendChild(element);
  });
}

// 导出所有示例
export default {
  basicApiCallExample,
  asyncWithStateExample,
  setupDebouncedSearch,
  setupThrottledScroll,
  concurrentUploadExample,
  batchProcessExample,
  complexVideoCreationExample
};
