/**
 * 简单的模板渲染器，支持 Mustache 或原生占位符
 */
export const renderTemplate = (template, data = {}) => {
  // 如果运行环境提供 Mustache，则优先使用
  if (typeof window !== 'undefined' && window.Mustache && typeof window.Mustache.render === 'function') {
    return window.Mustache.render(template, data);
  }
  // 兜底：最简单的 {{key}} 替换
  return template.replace(/{{\s*(\w+)\s*}}/g, (_, key) => (data[key] ?? ''));
};

export const renderView = async (templateName, containerSelector = '#app-container') => {
  const container = document.querySelector(containerSelector);
  if (!container) {throw new Error('Container not found');}

  const resp = await fetch(`/templates/${templateName}.html`);
  const html = await resp.text();
  container.innerHTML = html;

  return container;
}; 