<div class="upload-page">
    <header class="page-header">
        <button class="back-btn" data-route="/dashboard">
            <img src="/icons/back/back.svg" alt="Back">
        </button>
        <h1 class="header-title">Edit Video</h1>
        <div id="video-status-container" class="status-container">
             <span id="video-status-badge" class="status-badge"></span>
        </div>
    </header>
    <main class="upload-form-container">
        <form id="edit-video-form" class="upload-form">

            <!-- Failure Reason Display Area -->
            <div id="failure-reason-container" class="failure-reason-box hidden">
                <strong>Failed:</strong> <span id="failure-reason-text"></span>
            </div>

            <div class="form-grid">
                <!-- Video Uploader -->
                <div class="form-group">
                    <label for="video-upload">Video<span class="required">*</span></label>
                    <div id="video-uploader" class="file-uploader">
                        <input type="file" class="file-input" accept="video/*">
                        <div class="uploader-box">
                            <span>+</span>
                        </div>
                        <div class="file-details hidden">
                            <img class="video-default-icon" src="/icons/video/default.svg" alt="Video">
                            <span class="file-name"></span>
                        </div>
                        <div class="file-uploader-controls">
                            <button type="button" class="preview-btn" disabled>预览</button>
                            <button type="button" class="delete-btn"><img src="/icons/delete/delete.svg" alt="delete"></button>
                        </div>
                    </div>
                </div>

                <!-- Cover Uploader -->
                <div class="form-group">
                    <label for="cover-upload">Cover<span class="required">*</span></label>
                    <div id="cover-uploader" class="file-uploader">
                        <input type="file" class="file-input" accept="image/*">
                        <div class="uploader-box">
                           <span>+</span>
                        </div>
                        <div class="file-details hidden">
                            <img class="image-preview" src="" alt="Cover preview">
                            <span class="file-name"></span>
                        </div>
                         <div class="file-uploader-controls">
                           <button type="button" class="preview-btn" disabled>预览</button>
                           <button type="button" class="delete-btn"><img src="/icons/delete/delete.svg" alt="delete"></button>
                        </div>
                    </div>
                </div>

                <!-- Top progress inside grid -->
                <div class="form-group full-span">
                    <div id="top-upload-progress" class="top-progress hidden">
                        <div class="top-progress__track">
                            <div class="top-progress__bar"></div>
                        </div>
                        <div class="top-progress__label">Uploading 0%</div>
                    </div>
                </div>

                <!-- Title Fields -->
                <div class="form-group full-span">
                    <label for="title-cn">Tittle<span class="required">*</span></label>
                    <div class="input-with-btn">
                        <input type="text" id="title-cn" placeholder="CN">
                        <button type="button" class="btn-translate">Translate</button>
                    </div>
                    <input type="text" id="title-en" placeholder="EN" class="input-secondary">
                </div>

                <!-- Description Fields -->
                <div class="form-group full-span">
                    <label for="description-cn">Description<span class="required">*</span></label>
                    <div class="input-with-btn">
                        <input type="text" id="description-cn" placeholder="CN">
                        <button type="button" class="btn-translate">Translate</button>
                    </div>
                    <input type="text" id="description-en" placeholder="EN" class="input-secondary">
                </div>

                <!-- Category -->
                <div class="form-group full-span">
                    <label for="category-select">Category</label>
                    <div id="category-dropdown" class="custom-dropdown" tabindex="0">
                        <div class="dropdown-selected">
                            <span class="selected-value">None</span>
                            <div class="dropdown-arrow"></div>
                        </div>
                        <div class="dropdown-options">
                            <ul class="options-list">
                                <!-- Options will be populated by JS -->
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Client -->
                 <div class="form-group full-span">
                    <div class="label-row">
                        <label for="client-select">Client</label>
                    </div>
                    <div id="client-dropdown" class="custom-dropdown" tabindex="0">
                        <div class="dropdown-selected">
                            <span class="selected-value">None</span>
                            <div class="dropdown-arrow"></div>
                        </div>
                        <div class="dropdown-options">
                            <ul class="options-list">
                                <!-- Options will be populated by JS -->
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Publisher -->
                <div class="form-group full-span">
                    <div class="label-row">
                        <label for="publisher-select">Publisher</label>
                    </div>
                    <select id="publisher-select">
                        <option value="">All</option>
                    </select>
                </div>

                <!-- MLS ID Container -->
                <div class="form-group full-span">
                     <div class="label-row">
                        <label for="mls-id-input">MLS ID</label>
                         <button type="button" class="btn-new" id="add-mls-id-btn">+ Add MLS ID</button>
                    </div>
                    <div id="mls-id-container">
                        <!-- MLS ID rows will be dynamically inserted here -->
                    </div>
                </div>
                
                 <!-- URL Link -->
                <div class="form-group full-span">
                    <label for="url-link">URL Link</label>
                    <input type="text" id="url-link" placeholder="Enter Associated MLS ID">
                </div>
            </div>
        </form>
    </main>
    <footer class="upload-footer">
        <button type="button" id="submit-changes-btn" class="btn-draft">Submit Changes</button>
        <button type="button" id="status-action-btn" class="btn-publish" disabled>Action</button>
    </footer>

    <!-- Universal Preview Modal -->
    <div id="preview-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <button id="close-preview-btn" class="close-modal-btn">&times;</button>
            <div id="preview-content"></div>
        </div>
    </div>

    <!-- Template for a single MLS ID row -->
    <template id="mls-id-row-template">
        <div class="mls-id-row">
            <div class="searchable-input-container">
                <input type="text" class="mls-id-input" placeholder="Enter keywords to search...">
                <div class="search-results-dropdown">
                    <ul>
                        <!-- Search results will be populated here -->
                    </ul>
                </div>
            </div>
            <button type="button" class="delete-icon-btn delete-mls-id-btn">
                <img src="/icons/delete/delete.svg" alt="delete">
            </button>
        </div>
    </template>
</div> 