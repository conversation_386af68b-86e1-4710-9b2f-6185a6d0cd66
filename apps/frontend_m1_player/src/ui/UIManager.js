// src/ui/UIManager.js

import { createVideoElement } from '../data/VideoData.js'; // 从我们的videos模块导入

/**
 * 初始化用户界面，将视频元素渲染到指定的容器中。
 * @param {string} feedContainerSelector - 用于放置视频Feed的DOM元素的选择器 (例如 '#video-feed-container').
 * @param {Array<object>} videosDataArray - 从getVideoData()获取的视频数据数组.
 * @returns {Array<HTMLElement>} - 创建并添加到DOM中的视频项元素数组.
 */
export function initializeUI(feedContainerSelector, videosDataArray) {
    const feedContainer = document.querySelector(feedContainerSelector);
    if (!feedContainer) {
        console.error(`UI Error: Feed container "${feedContainerSelector}" not found.`);
        return [];
    }

    // 清空容器，以防重复初始化 (虽然MVP中通常只初始化一次)
    feedContainer.innerHTML = '';

    const videoDOMElements = [];

    videosDataArray.forEach(videoData => {
        const videoItemElement = createVideoElement(videoData);
        feedContainer.appendChild(videoItemElement);
        videoDOMElements.push(videoItemElement);
    });

    // 初始时，确保第一个视频在视图中 (transformY = 0)
    // 如果视频项是直接子元素，且父容器（feedContainer）进行transform，则不需要对子元素做额外操作
    // 我们将在main.js中调用 updateFeedAppearance 来设置初始位置

    return videoDOMElements;
}

/**
 * 更新视频Feed容器的视觉外观，使其滚动到指定的视频索引。
 * @param {HTMLElement} feedContainerElement - 视频Feed的容器DOM元素.
 * @param {number} videoIndex - 要滚动到的视频的索引 (0-based).
 */
export function updateFeedAppearance(feedContainerElement, videoIndex) {
    if (!feedContainerElement) {
        console.error('UI Error: Cannot update appearance, feed container element is invalid.');
        return;
    }
    // 使用像素单位，与拖动时的单位保持一致
    const translateYValue = -videoIndex * 100; // 单位是 vh
    feedContainerElement.style.transform = `translateY(${translateYValue}vh)`;
} 