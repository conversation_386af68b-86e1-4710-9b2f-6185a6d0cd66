import { getGlobalMuteState, setGlobalMuteState } from './globalPlayerState.js';

/**
 * 格式化时间（秒）为 mm:ss 或 hh:mm:ss 格式
 * @param {number} totalSeconds - 总秒数
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(totalSeconds) {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = Math.floor(totalSeconds % 60);

    const paddedSeconds = seconds.toString().padStart(2, '0');
    const paddedMinutes = minutes.toString().padStart(2, '0');

    if (hours > 0) {
        const paddedHours = hours.toString().padStart(2, '0');
        return `${paddedHours}:${paddedMinutes}:${paddedSeconds}`;
    }
    return `${paddedMinutes}:${paddedSeconds}`;
}

/**
 * 初始化并管理单个视频播放器的自定义控件。
 * @param {HTMLVideoElement} videoElement - 视频DOM元素。
 * @param {HTMLElement} videoItemElement - 包含此视频及其所有控件的父级DOM元素。
 */
export function initializePlayerControls(videoElement, videoItemElement) {
    if (!videoElement || !videoItemElement) {
        console.error('PlayerControls Error: videoElement or videoItemElement not provided.');
        return;
    }

    // 设置初始音量状态
    videoElement.volume = 1; // 设置初始音量为最大

    videoElement.muted = getGlobalMuteState(); 

    // 从 videoItemElement 中获取控件元素
    const playPauseButton = videoItemElement.querySelector('.play-pause-button');
    const progressBarContainer = videoItemElement.querySelector('.progress-bar-container');
    const progressBar = videoItemElement.querySelector('.progress-bar');
    const progressFill = videoItemElement.querySelector('.progress-fill');
    const currentTimeDisplay = videoItemElement.querySelector('.current-time');
    const totalDurationDisplay = videoItemElement.querySelector('.total-duration');

    // 获取菜单相关元素
    const menuOverlay = videoItemElement.querySelector('.menu-overlay');
    const muteOption = videoItemElement.querySelector('.menu-option-mute');
    const playbackRateOption = videoItemElement.querySelector('.menu-option-playback-rate');
    const playbackRateButtons = videoItemElement.querySelector('.menu-option-playback-rate-buttons');

    // 确保所有必要的控件元素都存在
    if (!playPauseButton || !progressBarContainer || !progressBar || !progressFill || !currentTimeDisplay || !totalDurationDisplay) {
        console.error('PlayerControls Error: One or more control elements not found in videoItemElement for video:', videoElement.src);
        return;
    }

    // --- 辅助函数：更新播放/暂停按钮的视觉状态 ---
    function updatePlayPauseButtonVisual() {
        if (videoElement.paused || videoElement.ended) {
            playPauseButton.innerHTML = '▶️'; // 显示播放图标
            playPauseButton.classList.remove('hidden'); // 显示按钮
        } else {
            playPauseButton.innerHTML = '⏸️'; // 显示暂停图标
            playPauseButton.classList.add('hidden'); // 隐藏按钮
        }
    }

    // --- 辅助函数：更新总时长显示 ---
    function updateTotalDurationDisplay() {
        if (totalDurationDisplay && videoElement.duration && isFinite(videoElement.duration)) {
            totalDurationDisplay.textContent = formatTime(videoElement.duration);
            totalDurationDisplay.style.visibility = 'visible';
            if (progressBar) {
                progressBar.setAttribute('aria-valuemax', videoElement.duration);
            }
        } else if (totalDurationDisplay) {
            totalDurationDisplay.textContent = '0:00';
            totalDurationDisplay.style.visibility = 'visible';
            if (progressBar) {
                progressBar.setAttribute('aria-valuemax', '0');
            }
        }
        if (currentTimeDisplay) {
            currentTimeDisplay.textContent = formatTime(videoElement.currentTime || 0);
            currentTimeDisplay.style.visibility = 'visible';
        }
    }

    // --- 视频事件监听 ---

    // 1. 当视频元数据加载完毕时 (获取总时长)
    videoElement.addEventListener('loadedmetadata', () => {
        updateTotalDurationDisplay();
        updatePlayPauseButtonVisual();
    });

    // 新增：在初始化时，如果元数据已经可用，也尝试更新总时长
    if (videoElement.readyState >= HTMLMediaElement.HAVE_METADATA) {
        updateTotalDurationDisplay();
    } else {
        if (totalDurationDisplay) totalDurationDisplay.textContent = '0:00';
        if (progressBar) progressBar.setAttribute('aria-valuemax', '0');
    }

    // 2. 当视频播放时间更新时 (更新进度条和当前时间)
    videoElement.addEventListener('timeupdate', () => {
        if (progressFill && videoElement.duration) {
            const progressPercent = (videoElement.currentTime / videoElement.duration) * 100;
            progressFill.style.width = `${progressPercent}%`;
        }
        if (currentTimeDisplay) {
            currentTimeDisplay.textContent = formatTime(videoElement.currentTime);
            currentTimeDisplay.style.visibility = 'visible';
        }
        if (progressBar) {
            progressBar.setAttribute('aria-valuenow', videoElement.currentTime);
            progressBar.setAttribute('aria-valuetext', `${Math.round(videoElement.currentTime)} seconds`);
        }
    });

    // 3. 当视频开始播放时
    videoElement.addEventListener('play', () => {
        updatePlayPauseButtonVisual();
    });

    // 4. 当视频暂停时
    videoElement.addEventListener('pause', () => {
        updatePlayPauseButtonVisual();
    });

    // 5. 当视频播放结束时
    videoElement.addEventListener('ended', () => {
        updatePlayPauseButtonVisual();
    });


    // --- 新增：底部操作按钮的事件监听和基础逻辑 ---
    const likeButton = videoItemElement.querySelector('.action-like-btn');
    const commentButton = videoItemElement.querySelector('.action-comment-btn');
    const collectButton = videoItemElement.querySelector('.action-collect-btn');
    const shareButton = videoItemElement.querySelector('.action-share-btn');

    // 从 dataset 读取初始状态
    let isLiked = videoItemElement.dataset.isLiked === 'true';
    let isCollected = videoItemElement.dataset.isCollected === 'true';

    function updateLikeButtonVisual() {
        if (likeButton) {
            likeButton.innerHTML = isLiked ? '❤️' : '🤍'; // 实心/空心
            likeButton.classList.toggle('liked', isLiked);
        }
    }

    function updateCollectButtonVisual() {
        if (collectButton) {
            collectButton.innerHTML = isCollected ? '🌟' : '⭐'; // 实星/空星
            collectButton.classList.toggle('collected', isCollected);
        }
    }

    // 初始化按钮视觉状态
    updateLikeButtonVisual();
    updateCollectButtonVisual();

    if (likeButton) {
        likeButton.addEventListener('click', (event) => {
            event.stopPropagation(); // 阻止事件冒泡到父元素，避免触发播放/暂停等
            isLiked = !isLiked;
            videoItemElement.dataset.isLiked = isLiked; // 更新dataset状态
            updateLikeButtonVisual();
            console.log(`视频 ${videoItemElement.dataset.videoId} 点赞状态: ${isLiked}`);
            // TODO: 调用 API -> POST /video/v1/videos/{videoId}/like, body: { lk: isLiked }
            //       根据API返回的 currLks 和 isLk 更新真实状态和数量
        });
    }

    if (commentButton) {
        commentButton.addEventListener('click', (event) => {
            event.stopPropagation();
            console.log(`视频 ${videoItemElement.dataset.videoId} 点击评论`);
            // TODO: 实现打开评论面板的逻辑
        });
    }

    if (collectButton) {
        collectButton.addEventListener('click', (event) => {
            event.stopPropagation();
            isCollected = !isCollected;
            videoItemElement.dataset.isCollected = isCollected; // 更新dataset状态
            updateCollectButtonVisual();
            console.log(`视频 ${videoItemElement.dataset.videoId} 收藏状态: ${isCollected}`);
            // TODO: 调用 API -> POST /video/v1/videos/{videoId}/collect, body: { clt: isCollected }
            //       根据API返回的 currClts 和 isClt 更新真实状态和数量
        });
    }

    if (shareButton) {
        shareButton.addEventListener('click', (event) => {
            event.stopPropagation();
            console.log(`视频 ${videoItemElement.dataset.videoId} 点击转发`);
            // TODO: 实现转发/分享逻辑
        });
    }



    if (playbackRateOption && playbackRateButtons) {
        // 更新倍速选项文本
        function updatePlaybackRateText() {
            playbackRateOption.textContent = `倍速 ${videoElement.playbackRate}x`;
        }

        // 更新倍速按钮的激活状态
        function updatePlaybackRateButtons() {
            const buttons = playbackRateButtons.querySelectorAll('.playback-rate-btn');
            buttons.forEach(btn => {
                const rate = parseFloat(btn.dataset.rate);
                btn.classList.toggle('active', Math.abs(rate - videoElement.playbackRate) < 0.1); // 使用近似比较，避免浮点数精度问题
            });
        }

        // 设置倍速
        function setPlaybackRate(rate) {
            // 确保倍速在有效范围内
            const validRates = [0.5, 0.75, 1.0, 1.5, 2.0, 3.0];
            if (!validRates.includes(rate)) {
                console.warn(`Invalid playback rate: ${rate}, using default 1.0x`);
                rate = 1.0;
            }
            videoElement.playbackRate = rate;
            updatePlaybackRateText();
            updatePlaybackRateButtons();
            console.log(`Video playback rate set to: ${rate}x`);
        }


        // 初始化倍速按钮状态
        updatePlaybackRateText();
        updatePlaybackRateButtons();
    }

    // 初始更新菜单项文本
    if (videoElement && muteOption) {
        muteOption.textContent = videoElement.muted ? '取消静音' : '静音';
    }

    // 初始状态更新
    updatePlayPauseButtonVisual();
    updateTotalDurationDisplay();

    return {
        getVideoElement: () => videoElement
        // 未来可以暴露更多控制方法
    };
} 