/**
 * 页面管理器
 * 管理应用内所有页面的切换和状态
 */
import { VideoDataManager, createVideoElement } from '../data/VideoData.js';
import { createInteractionState } from '../state/InteractionState.js';
import { initializeUI, updateFeedAppearance } from '../ui/UIManager.js';
import { VideoPlayer } from './VideoPlayer.js';
import { initializePlayerControls } from './PlayerControls.js';
import { initSwipeGestures } from '../interaction/gestures/GestureDetector.js';
import { GestureManager } from '../interaction/gestures/GestureManager.js';
import { initFullscreenListener } from '../interaction/menu/MenuManager.js';
import { closeMenu } from '../interaction/menu/MenuManager.js';

export class PageManager {
    constructor() {
        // 页面元素
        this.feedPage = document.getElementById('page-feed');
        this.searchPage = document.getElementById('page-search');
        this.searchFeedPage = document.getElementById('page-search-feed');
        
        // 交互元素
        this.searchIcon = document.querySelector('.search-icon-container');
        this.searchbackButton = document.querySelector('.search-back-button');
        this.searchFeedBackButton = document.querySelector('.search-feed-back-button');
        this.searchInput = document.querySelector('.search-input');
        this.searchButton = document.querySelector('.search-button');
        this.searchResults = document.querySelector('.search-results');

        // 交互状态
        this.interactionState = createInteractionState();

        // 视频播放器
        this.videoPlayer = new VideoPlayer();

        // 搜索状态
        this.currentKeyword = '';

        this.searchManager = new VideoDataManager({
            limit: 10,
            defaultFilter: null
        });

        // 手势管理器 - 移到 searchManager 初始化之后
        this.gestureManager = new GestureManager(this.videoPlayer, this.interactionState, this.searchManager);

        this.initEventListeners();
    }

    initEventListeners() {
        // 搜索图标点击事件
        this.searchIcon.addEventListener('click', () => {
            console.log('Search icon clicked');
            // 暂停当前视频
            const feedVideoPlayer = window.feedVideoPlayer; // 从全局获取 feed 页面的 videoPlayer
            if (feedVideoPlayer && feedVideoPlayer.currentVideoElement) {
                feedVideoPlayer.currentVideoElement.pause();
            }
            this.showSearchPage();
        });

        // 搜索页返回按钮点击事件
        this.searchbackButton.addEventListener('click', () => {
            console.log('Search back button clicked');
            this.showFeedPage();
        });

        // 搜索结果视频流页返回按钮点击事件
        this.searchFeedBackButton.addEventListener('click', () => {
            console.log('Search feed back button clicked');
            this.showSearchPage();
        });

        // 搜索按钮点击事件
        this.searchButton.addEventListener('click', () => {
            console.log('Search button clicked');
            this.handleSearch();
        });

        // 回车键搜索
        this.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                console.log('Enter key pressed in search input');
                this.handleSearch();
            }
        });

        // 监听滚动事件，实现无限加载
        this.searchResults.addEventListener('scroll', () => {
            if (this.searchManager.isLoadingVideos() || !this.searchManager.videoDataState.hasMore) return;
            
            const { scrollTop, scrollHeight, clientHeight } = this.searchResults;
            if (scrollTop + clientHeight >= scrollHeight - 100) {
                console.log('Reached bottom of search results, loading more...');
                this.loadMoreResults();
            }
        });

        // 监听搜索结果卡片点击事件
        this.searchResults.addEventListener('click', (e) => {
            console.log('Search result card clicked');
            const card = e.target.closest('.search-result-card');
            if (card) {
                const cardImage = card.querySelector('.card-image img');
                const cardTitle = card.querySelector('.card-title');
                
                console.log('Card data:', {
                    imageUrl: cardImage?.src,
                    title: cardTitle?.textContent
                });
                
                this.showSearchFeedPage();
            }
        });
    }

    showSearchPage() {
        console.log('Showing search page');
        
        this.feedPage.classList.remove('active');
        this.searchPage.classList.add('active');
        this.searchFeedPage.classList.remove('active');
        this.searchInput.focus();

        // 清理视频播放器状态
        if (this.videoPlayer.currentVideoElement) {
            this.videoPlayer.currentVideoElement.pause();
            this.videoPlayer.currentVideoElement.currentTime = 0;
            this.videoPlayer.currentVideoElement = null;
        }
        this.videoPlayer.currentPlayerControls = null;
        this.videoPlayer.allVideoDOMElements = [];
        this.videoPlayer.feedContainerElement = null;

        // 如果是从 searchFeedPage 返回，保持已加载的视频数据
        if (this.searchManager.videoDataState.items.length > 0) {
            // 清空搜索结果容器
            this.searchResults.innerHTML = '';
            // 重新渲染已加载的视频数据
            this.renderSearchResults(this.searchManager.videoDataState.items);
        }
    }

    showFeedPage() {
        console.log('Showing feed page');
        this.searchPage.classList.remove('active');
        this.feedPage.classList.add('active');
        this.searchInput.value = '';
        this.resetSearch();
    }

    resetSearch() {
        console.log('Resetting search state');
        this.currentKeyword = '';
        this.searchResults.innerHTML = '';
        // 清空 searchManager 中的视频数据
        this.searchManager.videoDataState.items = [];
        this.searchManager.videoDataState.pagination = {
            totItms: 0,
            totPgs: 0,
            currPg: 1,
            lim: 10
        };
    }

    showSearchFeedPage() {
        console.log('Showing search feed page');
        
        // 清理菜单状态
        if (this.interactionState.activeMenuVideoItem) {
            closeMenu(this.interactionState);
        }
        
        // 获取视频容器元素
        const searchFeedContainer = document.getElementById('video-search-feed-container');
        if (!searchFeedContainer) {
            console.error("Search feed container not found");
            return;
        }

        // 添加 contextmenu 事件监听器
        searchFeedContainer.addEventListener('contextmenu', (event) => {
            if (this.interactionState.longPressTimer || 
                this.interactionState.isLongPress || 
                this.interactionState.activeMenuVideoItem || 
                this.interactionState.isDraggingProgressBar || 
                this.interactionState.isVerticalSwiping) {
                event.preventDefault();
            }
        });

        // 获取已加载的视频数据
        const videosData = this.searchManager.videoDataState.items;
        if (!videosData || videosData.length === 0) {
            console.error("No video data available in search results");
            return;
        }

        // 使用 UIManager 初始化 UI，渲染视频列表
        const allVideoDOMElements = initializeUI('#video-search-feed-container', videosData);
        if (allVideoDOMElements.length === 0) {
            console.error("Failed to create video elements");
            return;
        }

        // 找到被点击视频的索引
        const clickedCard = document.querySelector('.search-result-card.selected');
        if (!clickedCard) {
            console.error("No selected video card found");
            return;
        }

        const videoId = clickedCard.dataset.videoId;
        const initialIndex = videosData.findIndex(video => video.vidId === videoId);
        if (initialIndex === -1) {
            console.error("Could not find clicked video in data");
            return;
        }

        // 初始化视频播放器
        this.videoPlayer.initialize(searchFeedContainer, allVideoDOMElements);

        // 初始化当前视频的控件和播放状态
        this.videoPlayer.setActiveVideo(initialIndex, (videoElement, videoItem) => {
            return initializePlayerControls(videoElement, videoItem);
        });

        // 设置初始视频的视觉位置
        updateFeedAppearance(searchFeedContainer, this.videoPlayer.getCurrentVideoIndex());

        // 初始化滑动手势监听
        initSwipeGestures(searchFeedContainer, {
            onGestureStart: (data) => this.gestureManager.handleGestureStart(data),
            onGestureMove: (data) => this.gestureManager.handleGestureMove(data),
            onGestureEnd: (data) => this.gestureManager.handleGestureEnd(data)
        });

        // 初始化全屏状态监听器
        initFullscreenListener(this.interactionState);

        this.searchPage.classList.remove('active');
        this.searchFeedPage.classList.add('active');
    }

    async handleSearch() {
        const keywords = this.searchInput.value.trim();
        if (!keywords) {
            console.log('Empty search keywords, ignoring search');
            return;
        }

        console.log('Starting search with keywords:', keywords);
        this.currentKeyword = keywords;
        this.searchResults.innerHTML = '';
        
        try {
            // 获取搜索结果并更新 videoDataState
            const results = await this.searchManager.initializeVideoData({ keywords });
            console.log('Search results received:', results.length);
            
            // 确保结果被正确存储
            if (results && results.length > 0) {
                this.searchManager.videoDataState.items = results;
            }
            
            this.renderSearchResults(results);
        } catch (error) {
            console.error('Search failed:', error);
        }
    }

    async loadMoreResults() {
        if (this.searchManager.isLoadingVideos() || !this.searchManager.videoDataState.hasMore) return;
        
        try {
            const newResults = await this.searchManager.loadMoreVideos({ keywords: this.currentKeyword });
            
            // 只渲染新结果，不重复更新状态
            if (newResults && newResults.length > 0) {
                this.renderSearchResults(newResults);
            }
        } catch (error) {
            console.error('Load more failed:', error);
        }
    }

    renderSearchResults(results) {
        const fragment = document.createElement('div');
        fragment.className = 'search-results-grid';
        
        results.forEach(video => {
            const card = document.createElement('div');
            card.className = 'search-result-card';
            card.dataset.videoId = video.vidId;
            card.innerHTML = `
                <div class="card-image">
                    <img src="${video.coverImageUrl}" alt="${video.title}">
                </div>
                <div class="card-title">${video.title}</div>
            `;
            
            card.addEventListener('click', () => {
                document.querySelectorAll('.search-result-card').forEach(c => c.classList.remove('selected'));
                card.classList.add('selected');
            });
            
            fragment.appendChild(card);
        });
        
        this.searchResults.appendChild(fragment);
    }
} 