import '../style.css'

import { VideoDataManager, createVideoElement } from '../data/VideoData.js'
import { initializeUI, updateFeedAppearance } from '../ui/UIManager.js'
import { initSwipeGestures, swipeThreshold as verticalSwipeThreshold } from '../interaction/gestures/GestureDetector.js'
import { initializePlayerControls, formatTime } from './PlayerControls.js'
import { createInteractionState, clearInteractionState, resetDragState, INTERACTION_CONSTANTS } from '../state/InteractionState.js'
import { initFullscreenListener } from '../interaction/menu/MenuManager.js'
import { VideoPlayer } from './VideoPlayer.js'
import { GestureManager } from '../interaction/gestures/GestureManager.js'
import { PageManager } from './pageManager.js'

// 创建页面管理器实例
const pageManager = new PageManager();

// 创建视频播放器实例
const videoPlayer = new VideoPlayer();
// 暴露到全局，供 PageManager 使用
window.feedVideoPlayer = videoPlayer;

// 创建视频数据管理器实例
const videoDataManager = new VideoDataManager({
    limit: 10,
    defaultFilter: null
});

// 应用状态变量
let currentVideoIndex = 0
let allVideoDOMElements = [] // 存储所有视频的DOM元素引用
let feedContainerElement = null // 视频Feed容器的引用

// 新增：存储当前视频在拖动开始时的基础 transformY 值 (以像素为单位)
let currentVideoBasePixelOffset = 0;

// --- 新增：手势交互状态变量 ---
const interactionState = createInteractionState();

const FEED_CONTAINER_SELECTOR = '#video-feed-container' // 从HTML中获取容器的选择器

// 创建手势管理器实例
const gestureManager = new GestureManager(videoPlayer, interactionState, videoDataManager);

/**
 * 初始化应用程序
 */
async function initializeApp() {
    try {
        // 1. 获取视频数据
        const videosData = await videoDataManager.initializeVideoData();
        if (!videosData || videosData.length === 0) {
            console.error("App Init Error: No video data available.")
            return
        }

        // 2. 获取 Feed 容器 DOM 元素
        feedContainerElement = document.querySelector(FEED_CONTAINER_SELECTOR)
        if (!feedContainerElement) {
            console.error(`App Init Error: Feed container "${FEED_CONTAINER_SELECTOR}" not found.`)
            return
        }

        // --- 开始：新增 contextmenu 事件监听器 ---
        feedContainerElement.addEventListener('contextmenu', (event) => {
            if (interactionState.longPressTimer || 
                interactionState.isLongPress || 
                interactionState.activeMenuVideoItem || 
                interactionState.isDraggingProgressBar || 
                interactionState.isVerticalSwiping) {
                event.preventDefault();
                
            }
        });
        // --- 结束：新增 contextmenu 事件监听器 ---

        // 3. 初始化UI，渲染视频列表，并获取所有视频的DOM元素
        allVideoDOMElements = initializeUI(FEED_CONTAINER_SELECTOR, videosData)
        if (allVideoDOMElements.length === 0) {
            console.error("App Init Error: UI initialization failed to create video elements.")
            return
        }

        // 4. 初始化视频播放器
        videoPlayer.initialize(feedContainerElement, allVideoDOMElements);

        // 5. 设置初始视频的视觉位置 (第一个视频)
        updateFeedAppearance(feedContainerElement, videoPlayer.getCurrentVideoIndex())

        // 6. 初始化第一个视频的控件和播放状态
        if (allVideoDOMElements.length > 0) {
            const firstVideoItem = allVideoDOMElements[0];
            const firstVideoElement = firstVideoItem.querySelector('video');
            if (firstVideoElement) {
                videoPlayer.setActiveVideo(0, (videoElement, videoItem) => {
                    return initializePlayerControls(videoElement, videoItem);
                });
            }
        }

        // 7. 初始化滑动手势监听
        initSwipeGestures(feedContainerElement, {
            onGestureStart: (data) => gestureManager.handleGestureStart(data),
            onGestureMove: (data) => gestureManager.handleGestureMove(data),
            onGestureEnd: (data) => gestureManager.handleGestureEnd(data)
        });

        // 8. 初始化全屏状态监听器
        initFullscreenListener(interactionState);

        console.log("Application initialized with contextmenu prevention!");
    } catch (error) {
        console.error("Error initializing app:", error);
    }
}

// 当DOM加载完毕后，或者由于 type="module" 脚本默认延迟执行，可以直接调用初始化
document.addEventListener('DOMContentLoaded', initializeApp)


