// 视频播放器核心类
export class VideoPlayer {
    constructor() {
        this.currentVideoIndex = 0;
        this.allVideoDOMElements = [];
        this.feedContainerElement = null;
        this.currentVideoBasePixelOffset = 0;
        this.currentPlayerControls = null;
        this.currentVideoElement = null;
    }

    /**
     * 初始化视频播放器
     * @param {HTMLElement} feedContainer - 视频容器元素
     * @param {Array} videoElements - 视频元素数组
     */
    initialize(feedContainer, videoElements) {
        this.feedContainerElement = feedContainer;
        this.allVideoDOMElements = videoElements;
        this.calculateAndSetBasePixelOffset();
    }

    /**
     * 计算并设置当前视频的基础像素偏移量
     */
    calculateAndSetBasePixelOffset() {
        this.currentVideoBasePixelOffset = -this.currentVideoIndex * window.innerHeight;
    }

    /**
     * 切换到指定索引的视频
     * @param {number} newIndex - 新的视频索引
     * @param {Function} onVideoChange - 视频切换时的回调函数
     */
    setActiveVideo(newIndex, onVideoChange) {
        if (newIndex < 0 || newIndex >= this.allVideoDOMElements.length) {
            return;
        }

        if (newIndex === this.currentVideoIndex && this.currentPlayerControls) {
            return;
        }

        // 保存当前视频的播放状态
        const wasPlaying = this.currentVideoElement && !this.currentVideoElement.paused;
        
        // 暂停当前视频
        if (this.currentVideoElement) {
            this.currentVideoElement.pause();
        }

        this.currentVideoIndex = newIndex;
        const currentVideoItem = this.allVideoDOMElements[this.currentVideoIndex];
        this.currentVideoElement = currentVideoItem.querySelector('video');

        if (this.currentVideoElement) {
            this.currentPlayerControls = onVideoChange(this.currentVideoElement, currentVideoItem);
            
            // 通知React Native当前视频已切换
            if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'VIDEO_CHANGED',
                    videoId: currentVideoItem.dataset.videoId,
                    index: newIndex
                }));
            }

            // 如果之前是播放状态，尝试播放新视频
            if (true) {
                const playPromise = this.currentVideoElement.play();
                if (playPromise !== undefined) {
                    playPromise.catch(error => {
                        console.warn("Error attempting to play new video in setActiveVideo:", error);
                        // 如果自动播放失败，通知React Native
                        if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
                            window.ReactNativeWebView.postMessage(JSON.stringify({
                                type: 'VIDEO_PLAY_ERROR',
                                videoId: currentVideoItem.dataset.videoId,
                                error: error.message
                            }));
                        }
                    });
                }
            }
        } else {
            console.error("Could not find video element for new active video.");
        }
    }

    /**
     * 获取当前视频的控件元素
     * @param {HTMLElement} videoItemElement - 视频项元素
     * @returns {Object} 控件元素对象
     */
    getControlsFromVideoItem(videoItemElement) {
        return {
            playPauseButton: videoItemElement.querySelector('.play-pause-button'),
            progressBarContainer: videoItemElement.querySelector('.progress-bar-container'),
            progressBar: videoItemElement.querySelector('.progress-bar'),
            progressFill: videoItemElement.querySelector('.progress-fill'),
            currentTimeDisplay: videoItemElement.querySelector('.current-time'),
            totalDurationDisplay: videoItemElement.querySelector('.total-duration')
        };
    }

    /**
     * 获取当前视频索引
     * @returns {number} 当前视频索引
     */
    getCurrentVideoIndex() {
        return this.currentVideoIndex;
    }

    /**
     * 获取当前视频元素
     * @returns {HTMLVideoElement} 当前视频元素
     */
    getCurrentVideoElement() {
        return this.currentVideoElement;
    }

    /**
     * 获取所有视频元素
     * @returns {Array} 所有视频元素数组
     */
    getAllVideoElements() {
        return this.allVideoDOMElements;
    }

    /**
     * 更新视频元素列表
     * @param {Array} newVideoElements - 新的视频元素数组
     */
    updateVideoElements(newVideoElements) {
        this.allVideoDOMElements = newVideoElements;
    }
} 