/* src/style.css */

/* 1. 基础和全局样式 */
@import './css/base/_reset.css';
/* @import './css/base/_typography.css'; (如果创建了) */

/* 2. 布局 */
@import './css/layout/_main.css';
@import './css/layout/_pages.css';

/* 3. 组件 */
@import './css/components/_video-item.css';
@import './css/components/_player-ui.css';
@import './css/components/_menu.css';
@import './css/components/_seek-indicator.css';
@import './css/components/_search-icon.css';
@import './css/components/_search-page.css';

/* 4. 状态特定样式 */
@import './css/states/_fullscreen.css';

/* 5. 工具类 */
@import './css/utils/_helpers.css';