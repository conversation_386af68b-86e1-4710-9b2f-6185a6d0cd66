// Video-related functionality
console.log('=== VideoData.js loaded ===');

import { createMenuPanel } from '../interaction/menu/MenuManager.js';
import { getFeed } from '../services/apiService.js';

function formatNumber(num) {
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

export class VideoDataManager {
    constructor(config = {}) {
        this.videoDataState = {
            items: [],
            pagination: {
                totItms: 0,    // 总条目数
                totPgs: 0,     // 总页数
                currPg: 1,     // 当前页码
                lim: config.limit || 10  // 每页限制
            },
            isLoading: false,
            hasMore: true,
            error: null,
        };
        this.defaultFilter = config.defaultFilter || null;
    }

    /**
     * 转换单个视频项数据
     * @param {object} apiItem - API 返回的原始视频项数据
     * @returns {object} - 转换后的视频项数据
     */
    #transformVideoItem(apiItem) {
        return {
            vidId: apiItem.vidId,
            title: apiItem.ttl?.zh || apiItem.ttl?.en || '未知标题',
            description: apiItem.dsc?.zh || apiItem.dsc?.en || '',
            coverImageUrl: apiItem.cvrImgUrl,
            videoUrl: apiItem.vidUrl,
            duration: apiItem.dur, // 秒
            uploader: {
                id: apiItem.uld.id,
                nickname: apiItem.uld.nknm,
                avatarUrl: apiItem.uld.avtUrl,
            },
            stats: {
                likes: apiItem.sts.lks,
                comments: apiItem.sts.cmts,
                collections: apiItem.sts.cltsCnt,
            },
            associatedContent: apiItem.ascCont,
            userInteraction: apiItem.usrInt ? {
                isLiked: apiItem.usrInt.isLk,
                isCollected: apiItem.usrInt.isClt,
            } : { isLiked: false, isCollected: false },
            createdAt: apiItem.cAt ? new Date(apiItem.cAt) : null,
        };
    }

    async initializeVideoData(filterParams = null) {
        if (this.videoDataState.isLoading) return [];

        this.videoDataState.isLoading = true;
        this.videoDataState.error = null;

        try {
            const data = await getFeed(1, this.videoDataState.pagination.lim, filterParams);

            // 转换数据
            this.videoDataState.items = data.items.map(item => this.#transformVideoItem(item));
            
            this.videoDataState.pagination = {
                ...this.videoDataState.pagination,
                totItms: data.pgn.totItms,
                totPgs: data.pgn.totPgs,
                currPg: data.pgn.currPg,
            };
            this.videoDataState.hasMore = data.pgn.currPg < data.pgn.totPgs;

            return this.videoDataState.items;

        } catch (error) {
            console.error('Error initializing video data:', error.message);
            this.videoDataState.error = error.message;
            this.videoDataState.items = [];
            this.videoDataState.hasMore = false;
            return [];
        } finally {
            this.videoDataState.isLoading = false;
        }
    }

    async loadMoreVideos(filterParams = null) {
        if (this.videoDataState.isLoading || !this.videoDataState.hasMore) return [];

        this.videoDataState.isLoading = true;
        this.videoDataState.error = null;
        const nextPage = this.videoDataState.pagination.currPg + 1;

        try {
            const data = await getFeed(nextPage, this.videoDataState.pagination.lim, filterParams);

            // 转换新数据并合并
            const newItems = data.items.map(item => this.#transformVideoItem(item));
            this.videoDataState.items = [...this.videoDataState.items, ...newItems];
            
            this.videoDataState.pagination = {
                ...this.videoDataState.pagination,
                totItms: data.pgn.totItms,
                totPgs: data.pgn.totPgs,
                currPg: data.pgn.currPg,
            };
            this.videoDataState.hasMore = data.pgn.currPg < data.pgn.totPgs;

            return newItems;

        } catch (error) {
            console.error('Error loading more videos:', error.message);
            this.videoDataState.error = error.message;
            return [];
        } finally {
            this.videoDataState.isLoading = false;
        }
    }

    getVideoData() {
        return this.videoDataState.items;
    }

    hasMoreVideos() {
        return this.videoDataState.hasMore;
    }

    isLoadingVideos() {
        return this.videoDataState.isLoading;
    }
}

export function createVideoElement(videoData) {
    const item = document.createElement('div');
    item.classList.add('video-item');
    item.dataset.videoId = videoData.vidId;
    item.dataset.isLiked = videoData.userInteraction.isLiked;
    item.dataset.isCollected = videoData.userInteraction.isCollected;

    const video = document.createElement('video');
    video.src = videoData.videoUrl;
    video.loop = true;
    video.muted = false;
    video.preload = 'auto';
    video.playsInline = true;
    video.setAttribute('playsinline', '');
    video.setAttribute('webkit-playsinline', '');
    video.setAttribute('x5-playsinline', '');
    video.setAttribute('x5-video-player-type', 'h5');
    video.setAttribute('x5-video-player-fullscreen', 'true');
    video.setAttribute('x5-video-orientation', 'portrait');
    video.setAttribute('webkit-playsinline', 'true');
    video.setAttribute('playsinline', 'true');
    video.setAttribute('x5-playsinline', 'true');
    video.setAttribute('x5-video-player-type', 'h5-page');
    video.controls = false;

    // 添加全屏状态变化事件监听
    video.addEventListener('webkitbeginfullscreen', () => {
        if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'FULLSCREEN_CHANGE',
                isFullscreen: true,
                videoId: videoData.vidId,
                orientation: 'landscape'
            }));
        }
    });

    video.addEventListener('webkitendfullscreen', () => {
        if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'FULLSCREEN_CHANGE',
                isFullscreen: false,
                videoId: videoData.vidId,
                orientation: 'portrait'
            }));
        }
    });

    // 添加React Native WebView相关的事件处理
    video.addEventListener('play', () => {
        if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'VIDEO_PLAY',
                videoId: videoData.vidId
            }));
        }
    });

    video.addEventListener('pause', () => {
        if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'VIDEO_PAUSE',
                videoId: videoData.vidId
            }));
        }
    });

    video.addEventListener('ended', () => {
        if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'VIDEO_ENDED',
                videoId: videoData.vidId
            }));
        }
    });

    const videoPlayerControlsOverlay = document.createElement('div');
    videoPlayerControlsOverlay.classList.add('video-player-controls-overlay');

    const playPauseButton = document.createElement('button');
    playPauseButton.classList.add('play-pause-button', 'control-btn');
    playPauseButton.setAttribute('aria-label', '播放/暂停');
    playPauseButton.innerHTML = '▶️';
    playPauseButton.classList.add('hidden');

    // --- 底部控件条：只包含进度条 ---
    const bottomControlsBar = document.createElement('div');
    bottomControlsBar.classList.add('bottom-controls-bar');

    const progressBarContainer = document.createElement('div');
    progressBarContainer.classList.add('progress-bar-container');

    const currentTimeDisplay = document.createElement('span');
    currentTimeDisplay.classList.add('time-display', 'current-time');
    currentTimeDisplay.textContent = '0:00';

    const progressBar = document.createElement('div');
    progressBar.classList.add('progress-bar');
    const progressFill = document.createElement('div');
    progressFill.classList.add('progress-fill');
    progressBar.appendChild(progressFill);

    const totalDurationDisplay = document.createElement('span');
    totalDurationDisplay.classList.add('time-display', 'total-duration');
    totalDurationDisplay.textContent = '0:00';

    progressBarContainer.appendChild(currentTimeDisplay);
    progressBarContainer.appendChild(progressBar);
    progressBarContainer.appendChild(totalDurationDisplay);

    bottomControlsBar.appendChild(progressBarContainer);

    // --- 新的右侧竖排操作按钮容器 ---
    const sideActionButtons = document.createElement('div');
    sideActionButtons.classList.add('side-action-buttons');

    // 点赞按钮和计数
    const likeContainer = document.createElement('div');
    likeContainer.classList.add('action-container');
    const likeButton = document.createElement('button');
    likeButton.classList.add('control-btn', 'action-like-btn');
    likeButton.setAttribute('aria-label', '点赞');
    likeButton.innerHTML = videoData.userInteraction.isLiked ? '❤️' : '🤍';
    const likeCount = document.createElement('span');
    likeCount.classList.add('action-count');
    likeCount.textContent = formatNumber(videoData.stats.likes);
    likeContainer.appendChild(likeButton);
    likeContainer.appendChild(likeCount);

    // 评论按钮和计数
    const commentContainer = document.createElement('div');
    commentContainer.classList.add('action-container');
    const commentButton = document.createElement('button');
    commentButton.classList.add('control-btn', 'action-comment-btn');
    commentButton.setAttribute('aria-label', '评论');
    commentButton.innerHTML = '💬';
    const commentCount = document.createElement('span');
    commentCount.classList.add('action-count');
    commentCount.textContent = formatNumber(videoData.stats.comments);
    commentContainer.appendChild(commentButton);
    commentContainer.appendChild(commentCount);

    // 收藏按钮和计数
    const collectContainer = document.createElement('div');
    collectContainer.classList.add('action-container');
    const collectButton = document.createElement('button');
    collectButton.classList.add('control-btn', 'action-collect-btn');
    collectButton.setAttribute('aria-label', '收藏');
    collectButton.innerHTML = videoData.userInteraction.isCollected ? '🌟' : '⭐';
    const collectCount = document.createElement('span');
    collectCount.classList.add('action-count');
    collectCount.textContent = formatNumber(videoData.stats.collections);
    collectContainer.appendChild(collectButton);
    collectContainer.appendChild(collectCount);

    // 分享按钮
    const shareButton = document.createElement('button');
    shareButton.classList.add('control-btn', 'action-share-btn');
    shareButton.setAttribute('aria-label', '转发');
    shareButton.innerHTML = '↪️';

    sideActionButtons.appendChild(likeContainer);
    sideActionButtons.appendChild(commentContainer);
    sideActionButtons.appendChild(collectContainer);
    sideActionButtons.appendChild(shareButton);

    // 将播放按钮、底部条和侧边操作按钮添加到主播放器控件浮层
    videoPlayerControlsOverlay.appendChild(playPauseButton);
    videoPlayerControlsOverlay.appendChild(bottomControlsBar);
    videoPlayerControlsOverlay.appendChild(sideActionButtons);

    // 用户信息和描述浮层
    const infoOverlay = document.createElement('div');
    infoOverlay.classList.add('video-info-overlay');
    infoOverlay.innerHTML = `
        <div class="video-info">
            <p class="username">${videoData.uploader.nickname}</p>
            <p class="description">${videoData.description}</p>
        </div>
    `;

    item.appendChild(video);
    item.appendChild(videoPlayerControlsOverlay);
    item.appendChild(infoOverlay);

    // 添加菜单浮层
    const menuOverlay = document.createElement('div');
    menuOverlay.classList.add('menu-overlay');
    
    // 使用 MenuManager 的 createMenuPanel 函数创建菜单面板
    const menuPanel = createMenuPanel();
    menuOverlay.appendChild(menuPanel);
    
    // 将菜单添加到视频项
    item.appendChild(menuOverlay);

    return item;
}

/**
 * 管理视频播放：播放当前索引的视频，暂停其他所有视频
 * @param {Array<HTMLElement>} videoDOMElements - 包含所有视频项 DOM 元素的数组
 * @param {number} currentIndex - 当前应该播放的视频的索引
 */
export function manageVideoPlayback(videoDOMElements, currentIndex) {
    videoDOMElements.forEach((videoItem, index) => {
        const videoPlayer = videoItem.querySelector('video');
        if (videoPlayer) {
            if (index === currentIndex) {
                videoPlayer.play().catch(error => {
                    console.warn(`Video ${videoPlayer.src} autoplay was prevented:`, error);
                });
            } else {
                videoPlayer.pause();
            }
        }
    });
}

export const videoUtils = {
    formatNumber
};

export default VideoDataManager; 