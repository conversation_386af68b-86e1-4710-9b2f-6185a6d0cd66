import { getGlobalMuteState, setGlobalMuteState } from '../../core/globalPlayerState.js';

// 菜单配置
const MENU_CONFIG = {
    options: [
        { id: 'mute', className: 'menu-option-mute', text: '静音', action: 'toggleMute' },
        { id: 'fullscreen', className: 'menu-option-fullscreen', text: '进入全屏', action: 'toggleFullscreen' },
        { id: 'playbackRate', className: 'menu-option-playback-rate', text: '倍速 1.0x', isLabel: true }
    ],
    playbackRates: [0.5, 0.75, 1.0, 1.5, 2.0, 3.0],
    reportOption: { id: 'report', className: 'menu-option-report', text: '举报', action: 'report' }
};

// 全局保存当前菜单的上下文
let _currentMenuContext = { videoItem: null, videoElement: null, interactionState: null };

// 菜单动作处理器
const menuActionHandlers = {
    toggleMute: (videoElement, option, videoItem, interactionState) => {
        
            // 如果当前是静音状态，则取消静音
        const currentGlobalMute = getGlobalMuteState();
        const newGlobalMute = !currentGlobalMute;
        setGlobalMuteState(newGlobalMute, videoElement);
        videoElement.muted = newGlobalMute;
        
        option.textContent = '静音';
        if (newGlobalMute) {
            option.textContent = '取消静音';
            // videoElement.volume = 0;
        }else{
            option.textContent = '静音';
            // videoElement.volume = videoElement.dataset.lastVolume || 1;
        }
    },

    toggleFullscreen: (videoElement, option, videoItem, interactionState) => {
        // 在RN环境中，我们完全依赖RN处理全屏
        if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
            const isFullscreen = !document.body.classList.contains('h5-in-fullscreen');
            
            // 更新UI状态
            if (isFullscreen) {
                document.body.classList.add('h5-in-fullscreen');
                // 移除其他视频项的全屏类
                document.querySelectorAll('.video-item').forEach(item => {
                    if (item !== videoItem) {
                        item.classList.remove('rn-fullscreen');
                    }
                });
                videoItem.classList.add('rn-fullscreen');
                option.textContent = '退出全屏';
            } else {
                document.body.classList.remove('h5-in-fullscreen');
                videoItem.classList.remove('rn-fullscreen');
                option.textContent = '进入全屏';
            }
            console.log(document.body.classList);

            // 发送全屏状态变化消息给RN
            window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'FULLSCREEN_CHANGE',
                isFullscreen: isFullscreen,
                videoId: videoItem.dataset.videoId || '',
                orientation: isFullscreen ? 'landscape' : 'portrait',
                videoRect: videoItem.getBoundingClientRect()
            }));


            // 关闭菜单
            closeMenu(interactionState);
        } else {
            // 如果不是在RN环境中，使用浏览器的全屏API
            if (!document.fullscreenElement) {
                videoItem.requestFullscreen()
                    .then(() => {
                        option.textContent = '退出全屏';
                        document.body.classList.add('h5-in-fullscreen');
                        closeMenu(interactionState);
                    })
                    .catch(err => {
                        console.error(`请求全屏失败: ${err.message} (${err.name})`);
                    });
            } else {
                document.exitFullscreen()
                    .then(() => {
                        option.textContent = '进入全屏';
                        document.body.classList.remove('h5-in-fullscreen');
                        closeMenu(interactionState);
                    });
            }
        }
    },
    
    report: (videoElement, option) => {
        // TODO: 实现举报功能
        console.log('Report option clicked');
    }
};

/**
 * 创建菜单选项元素
 * @param {Object} option - 菜单选项配置
 * @returns {HTMLElement} 菜单选项元素
 */
function createMenuOption(option) {
    const element = document.createElement('div');
    element.className = `menu-option ${option.className}`;
    element.textContent = option.text;
    if (option.action) {
        element.dataset.action = option.action;
    }
    if (option.isLabel) {
        element.classList.add('menu-label');
    }
    return element;
}

/**
 * 创建倍速选择按钮
 * @returns {HTMLElement} 倍速按钮容器
 */
function createPlaybackRateButtons() {
    const container = document.createElement('div');
    container.classList.add('menu-option-playback-rate-buttons');
    
    MENU_CONFIG.playbackRates.forEach(rate => {
        const button = document.createElement('button');
        button.classList.add('playback-rate-btn');
        if (rate === 1.0) button.classList.add('active');
        button.textContent = `${rate}x`;
        button.dataset.rate = rate;
        container.appendChild(button);
    });
    
    return container;
}

/**
 * 创建菜单面板
 * @returns {HTMLElement} 菜单面板元素
 */
export const createMenuPanel = () => {
    const menuPanel = document.createElement('div');
    menuPanel.className = 'menu-panel';
    
    MENU_CONFIG.options.forEach(optionConfig => {
        menuPanel.appendChild(createMenuOption(optionConfig));
    });
    menuPanel.appendChild(createPlaybackRateButtons());
    menuPanel.appendChild(createMenuOption(MENU_CONFIG.reportOption));

    // 在 menuPanel 上统一处理菜单项点击
    menuPanel.addEventListener('click', (event) => {
        // 如果菜单不可交互，阻止所有点击事件
        if (!_currentMenuContext.interactionState?.isMenuInteractive) {
            event.preventDefault();
            event.stopPropagation();
            return;
        }

        const optionElement = event.target.closest('.menu-option');
        if (optionElement && _currentMenuContext.videoElement && _currentMenuContext.interactionState) {
            const action = optionElement.dataset.action;
            if (action && menuActionHandlers[action]) {
                menuActionHandlers[action](
                    _currentMenuContext.videoElement,
                    optionElement,
                    _currentMenuContext.videoItem,
                    _currentMenuContext.interactionState
                );
            }
        }

        // 处理倍速按钮点击
        const playbackRateButton = event.target.closest('.playback-rate-btn');
        if (playbackRateButton && _currentMenuContext.videoElement) {
            const rate = parseFloat(playbackRateButton.dataset.rate);
            _currentMenuContext.videoElement.playbackRate = rate;
            const playbackRateLabel = menuPanel.querySelector('.menu-option-playback-rate');
            if (playbackRateLabel) playbackRateLabel.textContent = `倍速 ${rate}x`;
            menuPanel.querySelectorAll('.playback-rate-btn').forEach(btn => {
                btn.classList.toggle('active', parseFloat(btn.dataset.rate) === rate);
            });
        }
    });

    return menuPanel;
};

/**
 * 更新菜单选项状态
 * @param {HTMLElement} menuOverlay - 菜单浮层元素
 * @param {HTMLVideoElement} videoElement - 视频元素
 */
export const updateMenuOptions = (menuOverlay, videoElement) => {
    if (!menuOverlay || !videoElement) return;
    const globallyMuted = getGlobalMuteState();
    const muteOption = menuOverlay.querySelector('.menu-option-mute');
    if (muteOption) {
        muteOption.textContent = globallyMuted ? '取消静音' : '静音';
    }

    const playbackRateOption = menuOverlay.querySelector('.menu-option-playback-rate');
    if (playbackRateOption) {
        playbackRateOption.textContent = `倍速 ${videoElement.playbackRate}x`;
    }
};

/**
 * 打开菜单
 * @param {HTMLElement} videoItem - 视频项元素
 * @param {Object} interactionState - 交互状态对象
 */
export const openMenu = (videoItem, interactionState) => {
    if (!videoItem || !interactionState) {
        console.error("openMenu: videoItem or interactionState missing");
        return;
    }

    const menuOverlay = videoItem.querySelector('.menu-overlay');
    if (!menuOverlay || menuOverlay.classList.contains('visible')) return;

    console.log("Opening menu for video item");
    const videoElement = videoItem.querySelector('video');
    updateMenuOptions(menuOverlay, videoElement);

    // 添加菜单显示状态
    interactionState.isMenuVisible = true;
    interactionState.isMenuInteractive = false; // 初始时禁用交互

    menuOverlay.classList.add('visible');
    interactionState.activeMenuVideoItem = videoItem;
    console.log(interactionState.activeMenuVideoItem,"打开菜单");

    // 保存当前上下文
    _currentMenuContext.videoItem = videoItem;
    _currentMenuContext.videoElement = videoElement;
    _currentMenuContext.interactionState = interactionState;


    // 注意：这里不再设置延时，等待手势松开后再设置
};

/**
 * 启用菜单交互
 * @param {Object} interactionState - 交互状态对象
 */
export const enableMenuInteraction = (interactionState) => {
    if (!interactionState || !interactionState.isMenuVisible) return;

    // 设置一个延时，在手势松开后300ms启用交互
    setTimeout(() => {
        if (interactionState.isMenuVisible) {
            interactionState.isMenuInteractive = true;
            const menuPanel = interactionState.activeMenuVideoItem?.querySelector('.menu-panel');
            if (menuPanel) {
                menuPanel.classList.add('interactive');
            }
        }
    }, 300);
};

/**
 * 关闭菜单
 * @param {Object} interactionState - 交互状态对象
 */
export const closeMenu = (interactionState) => {
    if (!interactionState.activeMenuVideoItem && !_currentMenuContext.interactionState?.activeMenuVideoItem) return;

    const activeItem = interactionState.activeMenuVideoItem || _currentMenuContext.interactionState?.activeMenuVideoItem;
    if (!activeItem) return;

    console.log("Closing menu");
    const menuOverlay = activeItem.querySelector('.menu-overlay');
    if (menuOverlay) {
        const menuPanel = menuOverlay.querySelector('.menu-panel');
        if (menuPanel) {
            menuPanel.classList.remove('interactive');
        }
        menuOverlay.classList.remove('visible');
    }

    // 立即禁用菜单交互
    interactionState.isMenuInteractive = false;
    interactionState.isMenuVisible = false;
    if(interactionState.activeMenuVideoItem) interactionState.activeMenuVideoItem = null;
    console.log(interactionState.activeMenuVideoItem,"关闭菜单");

    // 清理上下文
    _currentMenuContext = { videoItem: null, videoElement: null, interactionState: null };
    if (_currentMenuContext.interactionState) {
        _currentMenuContext.interactionState.activeMenuVideoItem = null;
    }
};



/**
 * 初始化全屏状态监听器
 * @param {Object} interactionState - 交互状态对象
 */
export function initFullscreenListener(interactionState) {
    document.addEventListener('fullscreenchange', () => {
        const activeMenuPanel = document.querySelector('.menu-overlay.visible .menu-panel');
        let fullscreenOptionTextElement = null;
        if (activeMenuPanel) {
            fullscreenOptionTextElement = activeMenuPanel.querySelector('.menu-option-fullscreen');
        }

        if (document.fullscreenElement) {
            interactionState.isInFullscreen = true;
            if (fullscreenOptionTextElement) fullscreenOptionTextElement.textContent = '退出全屏';
            document.body.classList.add('h5-in-fullscreen');
            console.log('已进入全屏模式。');
        } else {
            interactionState.isInFullscreen = false;
            if (fullscreenOptionTextElement) fullscreenOptionTextElement.textContent = '进入全屏';
            document.body.classList.remove('h5-in-fullscreen');
            if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
                window.ReactNativeWebView.postMessage(JSON.stringify({ type: 'SET_ORIENTATION', orientation: 'portrait' }));
            }
            console.log('已退出全屏模式。');
        }
    });
}

// 添加全局消息监听器
window.addEventListener('message', (event) => {
    try {
        const data = JSON.parse(event.data);
        if (data.type === 'ORIENTATION_CHANGE' && data.isLandscape) {
            // 找到当前活动的视频项
            const activeVideoItem = document.querySelector('.video-item.active');
            if (activeVideoItem) {
                // 获取全屏按钮
                const fullscreenOption = activeVideoItem.querySelector('.menu-option-fullscreen');
                if (fullscreenOption) {
                    // 如果当前不是全屏状态，则进入全屏
                    if (!document.body.classList.contains('h5-in-fullscreen')) {
                        const videoElement = activeVideoItem.querySelector('video');
                        if (videoElement) {
                            // 调用全屏切换函数
                            menuActionHandlers.toggleFullscreen(
                                videoElement,
                                fullscreenOption,
                                activeVideoItem,
                                _currentMenuContext.interactionState
                            );
                        }
                    }
                }
            }
        }
    } catch (error) {
        console.error('Error handling orientation change message:', error);
    }
}); 