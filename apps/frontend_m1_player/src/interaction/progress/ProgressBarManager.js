import { formatTime } from '../../core/PlayerControls.js';

export class ProgressBarManager {
    constructor(videoPlayer, interactionState) {
        this.videoPlayer = videoPlayer;
        this.interactionState = interactionState;
        this.rafId = null;
    }

    /**
     * 处理进度条手势开始
     */
    handleProgressBarGesture(target, startX, originalEvent) {
        const progressBar = this.videoPlayer.getAllVideoElements()[this.videoPlayer.getCurrentVideoIndex()]?.querySelector('.progress-bar');
        if (progressBar && progressBar.contains(target)) {
            this.interactionState.isDraggingProgressBar = true;
            this.interactionState.isPotentialTap = false;
            
            const currentVideoElement = this.videoPlayer.getCurrentVideoElement();
            if (currentVideoElement) {
                this.interactionState.wasPlayingBeforeScrub = !currentVideoElement.paused;
            } else {
                this.interactionState.wasPlayingBeforeScrub = false;
            }

            this.interactionState.progressBarRect = progressBar.getBoundingClientRect();
            if (!this.interactionState.progressBarRect) {
                console.error('Failed to get progress bar rect');
                return false;
            }
            
            const { progressFill } = this.videoPlayer.getControlsFromVideoItem(
                this.videoPlayer.getAllVideoElements()[this.videoPlayer.getCurrentVideoIndex()]
            );
            
            if (progressFill) progressFill.style.transition = 'none';
            
            const clickXInProgressBar = startX - this.interactionState.progressBarRect.left;
            this.updateProgressBarWhileScrubbing(clickXInProgressBar, progressBar, currentVideoElement);
            originalEvent.preventDefault();
            return true;
        }
        return false;
    }

    /**
     * 处理进度条拖动
     */
    handleProgressBarDrag(originalEvent) {
        if (!this.interactionState.progressBarRect) return;
        
        originalEvent.preventDefault();
        const currentXAbsolute = originalEvent.touches[0].clientX;
        
        cancelAnimationFrame(this.rafId);
        this.rafId = requestAnimationFrame(() => {
            const progressBar = this.videoPlayer.getAllVideoElements()[this.videoPlayer.getCurrentVideoIndex()]
                .querySelector('.progress-bar');
            const currentXInProgressBar = currentXAbsolute - this.interactionState.progressBarRect.left;
            this.updateProgressBarWhileScrubbing(
                currentXInProgressBar,
                progressBar,
                this.videoPlayer.getCurrentVideoElement()
            );
        });
    }

    /**
     * 处理进度条拖动结束
     */
    handleProgressBarEnd() {
        const { progressFill } = this.videoPlayer.getControlsFromVideoItem(
            this.videoPlayer.getAllVideoElements()[this.videoPlayer.getCurrentVideoIndex()]
        );
        
        if (progressFill) progressFill.style.transition = '';
        
        const currentVideoElement = this.videoPlayer.getCurrentVideoElement();
        if (currentVideoElement && typeof this.interactionState.currentScrubTime === 'number' && 
            isFinite(this.interactionState.currentScrubTime)) {
            currentVideoElement.currentTime = this.interactionState.currentScrubTime;
        }
        
        if (this.interactionState.wasPlayingBeforeScrub && currentVideoElement) {
            currentVideoElement.play().catch(e => console.warn("Error playing after scrub:", e));
        }
        
        this.updateTimeDisplay(currentVideoElement);
        this.resetProgressBarState();
    }

    /**
     * 更新进度条显示
     */
    updateProgressBarWhileScrubbing(currentXInProgressBar, progressBarElement, videoElementToScrub) {
        if (!videoElementToScrub || !videoElementToScrub.duration || !this.interactionState.progressBarRect) return;

        const barWidth = this.interactionState.progressBarRect.width;
        const scrubRatio = Math.max(0, Math.min(currentXInProgressBar / barWidth, 1));
        const scrubTime = videoElementToScrub.duration * scrubRatio;

        const { progressFill, currentTimeDisplay, totalDurationDisplay } = this.videoPlayer.getControlsFromVideoItem(
            this.videoPlayer.getAllVideoElements()[this.videoPlayer.getCurrentVideoIndex()]
        );

        if (progressFill) {
            progressFill.style.width = `${scrubRatio * 100}%`;
        }
        if (currentTimeDisplay) {
            currentTimeDisplay.textContent = formatTime(scrubTime);
            currentTimeDisplay.style.visibility = 'visible';
        }
        if(totalDurationDisplay) totalDurationDisplay.style.visibility = 'visible';

        if (isFinite(scrubTime) && videoElementToScrub.readyState >= HTMLMediaElement.HAVE_METADATA) {
            videoElementToScrub.currentTime = scrubTime;
        }

        this.interactionState.currentScrubTime = scrubTime;
    }

    /**
     * 更新时间显示
     */
    updateTimeDisplay(currentVideoElement) {
        const { currentTimeDisplay, totalDurationDisplay } = this.videoPlayer.getControlsFromVideoItem(
            this.videoPlayer.getAllVideoElements()[this.videoPlayer.getCurrentVideoIndex()]
        );
        
        if (!currentVideoElement || currentVideoElement.paused || currentVideoElement.ended) {
            if(currentTimeDisplay) currentTimeDisplay.style.visibility = 'visible';
            if(totalDurationDisplay) totalDurationDisplay.style.visibility = 'visible';
        } else {
            if(currentTimeDisplay) currentTimeDisplay.style.visibility = 'visible';
            if(totalDurationDisplay) totalDurationDisplay.style.visibility = 'visible';
        }
    }

    /**
     * 重置进度条状态
     */
    resetProgressBarState() {
        this.interactionState.isDraggingProgressBar = false;
        this.interactionState.currentScrubTime = null;
        this.interactionState.wasPlayingBeforeScrub = undefined;
        this.interactionState.progressBarRect = null;
    }
} 