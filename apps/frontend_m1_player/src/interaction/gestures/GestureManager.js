import { INTERACTION_CONSTANTS } from '../../state/InteractionState.js';
import { formatTime, initializePlayerControls } from '../../core/PlayerControls.js';
import { openMenu, closeMenu, enableMenuInteraction } from '../menu/MenuManager.js';
import { updateFeedAppearance } from '../../ui/UIManager.js';
import { swipeThreshold as verticalSwipeThreshold } from './GestureDetector.js';
import { ProgressBarManager } from '../progress/ProgressBarManager.js';
import { createVideoElement } from '../../data/VideoData.js';

export class GestureManager {
    constructor(videoPlayer, interactionState, videoDataManager) {
        this.videoPlayer = videoPlayer;
        this.interactionState = interactionState;
        this.videoDataManager = videoDataManager;
        this.progressBarManager = new ProgressBarManager(videoPlayer, interactionState);
    }

    /**
     * 处理手势开始事件
     */
    handleGestureStart({ startX, startY, target, eventTime, originalEvent }) {
        console.log(target);
        this.interactionState.dragStartTime = eventTime;
        this.interactionState.dragStartX = startX;
        this.interactionState.dragStartY = startY;
        this.interactionState.isPotentialTap = true;
        this.interactionState.isVerticalSwiping = false;
        this.interactionState.isDraggingProgressBar = false;
        this.interactionState.isLongPress = false;

        // 清除可能存在的单击计时器
        clearTimeout(this.interactionState.singleTapTimer);

        // 处理菜单相关的手势
        if (this.handleMenuGesture(target, originalEvent)) {
            return;
        }

        // 处理进度条相关的手势
        if (this.progressBarManager.handleProgressBarGesture(target, startX, originalEvent)) {
            return;
        }

        // 处理侧边按钮的手势
        if (target.closest('.side-action-buttons .control-btn')) {
            this.interactionState.isPotentialTap = false;
            return;
        }

        // 处理垂直滑动和长按
        this.handleVerticalSwipeAndLongPress(target, originalEvent);
    }

    /**
     * 处理手势移动事件
     */
    handleGestureMove({ offsetX, offsetY, target, eventTime, originalEvent }) {
        if (this.interactionState.activeMenuVideoItem || this.interactionState.isDraggingProgressBar) {
            if (this.interactionState.isDraggingProgressBar) {
                this.progressBarManager.handleProgressBarDrag(originalEvent);
            } else if (this.interactionState.activeMenuVideoItem && target.classList.contains('menu-overlay')) {
                if(originalEvent.cancelable) originalEvent.preventDefault();
            }
            return;
        }

        this.handleTapAndSwipe(offsetX, offsetY, originalEvent);
    }

    /**
     * 处理手势结束事件
     */
    handleGestureEnd({ swipeX, swipeY, duration, target, eventTime, originalEvent }) {
        const wasLongPress = this.interactionState.isLongPress;
        clearTimeout(this.interactionState.longPressTimer);
        this.interactionState.isLongPress = false;

        if (wasLongPress) {
            this.interactionState.isPotentialTap = false;
            // 在手势结束时启用菜单交互
            if (this.interactionState.activeMenuVideoItem) {
                enableMenuInteraction(this.interactionState);
            }
            return;
        }

        if (this.handleMenuEndGesture(target)) {
            return;
        }

        if (this.interactionState.isDraggingProgressBar) {
            this.progressBarManager.handleProgressBarEnd();
            return;
        }

        this.handleSwipeEnd(swipeY);
        this.handleTapEnd(duration, target, originalEvent);
    }

    // 私有辅助方法
    handleMenuGesture(target, originalEvent) {
        if (this.interactionState.activeMenuVideoItem && target.classList.contains('menu-overlay')) {
            // 如果菜单不可交互，阻止所有交互
            if (!this.interactionState.isMenuInteractive) {
                if(originalEvent.cancelable) originalEvent.preventDefault();
                return true;
            }
            // 只阻止事件冒泡，不关闭菜单
            if(originalEvent.cancelable) originalEvent.preventDefault();
            return true;
        }
        
        if (this.interactionState.activeMenuVideoItem && target.closest('.menu-panel')) {
            // 如果菜单不可交互，阻止所有交互
            if (!this.interactionState.isMenuInteractive) {
                if(originalEvent.cancelable) originalEvent.preventDefault();
                return true;
            }
            this.interactionState.isPotentialTap = false;
            return true;
        }
        
        return false;
    }

    handleVerticalSwipeAndLongPress(target, originalEvent) {
        this.videoPlayer.feedContainerElement.style.transition = 'none';
        this.interactionState.initialVideoOffsetForDrag = -this.videoPlayer.getCurrentVideoIndex() * window.innerHeight;

        clearTimeout(this.interactionState.longPressTimer);
        this.interactionState.longPressTimer = setTimeout(() => {
            if (this.interactionState.isPotentialTap &&
                !this.interactionState.isVerticalSwiping &&
                !this.interactionState.isDraggingProgressBar &&
                !this.interactionState.activeMenuVideoItem) {

                console.log("长按检测到 - 准备打开菜单");
                this.interactionState.isLongPress = true;
                this.interactionState.isPotentialTap = false;
                
                if(originalEvent.cancelable) originalEvent.preventDefault();
                document.body.style.userSelect = 'none';
                
                openMenu(
                    this.videoPlayer.getAllVideoElements()[this.videoPlayer.getCurrentVideoIndex()],
                    this.interactionState
                );
                
                setTimeout(() => {
                    document.body.style.userSelect = '';
                }, 100);
            }
        }, INTERACTION_CONSTANTS.LONG_PRESS_DURATION);
    }

    handleTapAndSwipe(offsetX, offsetY, originalEvent) {
        // 检查是否在全屏模式下
        if (document.body.classList.contains('h5-in-fullscreen')) {
            return; // 在全屏模式下禁用所有滑动
        }

        if (Math.abs(offsetX) > INTERACTION_CONSTANTS.TAP_MAX_MOVEMENT || 
            Math.abs(offsetY) > INTERACTION_CONSTANTS.TAP_MAX_MOVEMENT) {
            this.interactionState.isPotentialTap = false;
            clearTimeout(this.interactionState.longPressTimer);
            clearTimeout(this.interactionState.singleTapTimer);
        }

        if (this.interactionState.isPotentialTap === false &&
            Math.abs(offsetY) > INTERACTION_CONSTANTS.TAP_MAX_MOVEMENT &&
            Math.abs(offsetY) > Math.abs(offsetX) * 1.5) {

            this.interactionState.isVerticalSwiping = true;
            this.videoPlayer.feedContainerElement.style.transform = 
                `translateY(${this.interactionState.initialVideoOffsetForDrag + offsetY}px)`;
            if(originalEvent.cancelable) originalEvent.preventDefault();
        }
    }

    handleMenuEndGesture(target) {
        if (this.interactionState.activeMenuVideoItem && target.classList.contains('menu-overlay')) {
            // 如果菜单不可交互，阻止关闭
            if (!this.interactionState.isMenuInteractive) {
                return true;
            }
            
            // 检查是否是有效的点击（不是长按或滑动）
            if (this.interactionState.isLongPress || 
                this.interactionState.isVerticalSwiping || 
                this.interactionState.isDraggingProgressBar) {
                return true;
            }
            
            closeMenu(this.interactionState);
            this.interactionState.isPotentialTap = false;
            return true;
        }
        if (this.interactionState.activeMenuVideoItem && target.closest('.menu-panel')) {
            // 如果菜单不可交互，阻止所有交互
            if (!this.interactionState.isMenuInteractive) {
                return true;
            }
            this.interactionState.isPotentialTap = false;
            return true;
        }
        return false;
    }

    async handleSwipeEnd(swipeY) {
        // 检查是否在全屏模式下
        if (document.body.classList.contains('h5-in-fullscreen')) {
            return; // 在全屏模式下禁用所有滑动和视频切换
        }
        console.log(this.videoPlayer.getAllVideoElements().length - 1)
        console.log(this.videoPlayer.getCurrentVideoIndex())
        this.videoPlayer.feedContainerElement.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

        if (this.interactionState.isVerticalSwiping) {
            let newIndex = this.videoPlayer.getCurrentVideoIndex();
            if (swipeY > verticalSwipeThreshold) {
                if (this.videoPlayer.getCurrentVideoIndex() < this.videoPlayer.getAllVideoElements().length - 1) {
                    newIndex = this.videoPlayer.getCurrentVideoIndex() + 1;
                }
            } else if (swipeY < -verticalSwipeThreshold) {
                if (this.videoPlayer.getCurrentVideoIndex() > 0) {
                    newIndex = this.videoPlayer.getCurrentVideoIndex() - 1;
                }
            }
            
            // 先切换视频
            this.videoPlayer.setActiveVideo(newIndex, (videoElement, videoItem) => {
                return initializePlayerControls(videoElement, videoItem);
            });
            
            // 在视频切换完成后，检查是否需要预加载下一页
            const currentPageSize = 10; // 每页视频数量
            const currentVideoIndex = this.videoPlayer.getCurrentVideoIndex();
            const isLastVideoOfCurrentPage = (currentVideoIndex + 1) % currentPageSize === 0;
            
            console.log('Checking video position after switch:', {
                currentVideoIndex,
                isLastVideoOfCurrentPage,
                hasMore: this.videoDataManager.hasMoreVideos(),
                isLoading: this.videoDataManager.isLoadingVideos(),
                totalVideos: this.videoPlayer.getAllVideoElements().length,
                pageNumber: Math.floor(currentVideoIndex / currentPageSize) + 1,
                nextVideoIndex: currentVideoIndex + 1
            });

            if (isLastVideoOfCurrentPage && this.videoDataManager.hasMoreVideos() && !this.videoDataManager.isLoadingVideos()) {
                console.log('Preloading next page as user reaches last video of current page');
                Promise.resolve().then(async () => {
                    try {
                        const newVideos = await this.videoDataManager.loadMoreVideos();
                        console.log('Preloaded new videos:', {
                            count: newVideos.length,
                            hasMore: this.videoDataManager.hasMoreVideos(),
                            isLoading: this.videoDataManager.isLoadingVideos()
                        });
                        
                        if (newVideos.length > 0) {
                            const newVideoElements = newVideos.map(videoData => {
                                const videoElement = createVideoElement(videoData);
                                this.videoPlayer.feedContainerElement.appendChild(videoElement);
                                return videoElement;
                            });
                            
                            const allElements = [...this.videoPlayer.getAllVideoElements(), ...newVideoElements];
                            this.videoPlayer.updateVideoElements(allElements);
                            
                            newVideoElements.forEach(videoElement => {
                                const video = videoElement.querySelector('video');
                                if (video) {
                                    video.preload = 'auto';
                                    video.load();
                                }
                            });
                        }
                    } catch (error) {
                        console.error('Error preloading next page:', error);
                    }
                });
            }
            
            this.interactionState.isVerticalSwiping = false;
            this.interactionState.isPotentialTap = false;
        }
    }

    handleTapEnd(duration, target, originalEvent) {
        if (this.interactionState.isPotentialTap && duration < INTERACTION_CONSTANTS.TAP_MAX_DURATION) {
            const clickTarget = target;

            // 检查是否点击了不应触发全局播放/暂停或双击的特定控件
            const isSpecificNonActionControl = clickTarget.closest('.side-action-buttons .control-btn') ||
                                               clickTarget.closest('.progress-bar-container') ||
                                               clickTarget.closest('.menu-panel');

            if (isSpecificNonActionControl) {
                this.interactionState.isPotentialTap = false;
                return;
            }

            const currentTime = Date.now();
            const timeSinceLastTap = currentTime - this.interactionState.lastTapTime;

            clearTimeout(this.interactionState.singleTapTimer);

            if (timeSinceLastTap < INTERACTION_CONSTANTS.DOUBLE_CLICK_THRESHOLD) {
                // 双击处理
                this.handleDoubleTap(originalEvent);
            } else {
                // 单击处理
                this.handleSingleTap();
            }
            
            this.interactionState.lastTapTime = currentTime;
            return;
        }

        if (!this.interactionState.isVerticalSwiping && !this.interactionState.isPotentialTap) {
            updateFeedAppearance(this.videoPlayer.feedContainerElement, this.videoPlayer.getCurrentVideoIndex());
        }
        this.interactionState.isPotentialTap = false;
    }

    handleDoubleTap(originalEvent) {
        console.log("双击检测到!");
        this.interactionState.isPotentialTap = false;
        this.interactionState.lastTapTime = 0;

        const videoItemElement = this.videoPlayer.getAllVideoElements()[this.videoPlayer.getCurrentVideoIndex()];
        const rect = videoItemElement.getBoundingClientRect();
        const clickX = originalEvent.changedTouches[0].clientX - rect.left;
        const videoWidth = rect.width;
        const isRightSide = clickX > videoWidth / 2;

        const currentVideoElement = this.videoPlayer.getCurrentVideoElement();
        if (currentVideoElement) {
            const seekTime = isRightSide ?
                Math.min(currentVideoElement.currentTime + 10, currentVideoElement.duration) :
                Math.max(currentVideoElement.currentTime - 10, 0);
            currentVideoElement.currentTime = seekTime;

            // 移除所有已存在的提示
            const existingIndicators = videoItemElement.querySelectorAll('.seek-indicator');
            existingIndicators.forEach(indicator => indicator.remove());

            const seekIndicator = document.createElement('div');
            seekIndicator.classList.add('seek-indicator');
            seekIndicator.textContent = isRightSide ? '+10s' : '-10s';
            videoItemElement.appendChild(seekIndicator);

            this.interactionState.isShowingSeekIndicator = true;

            if (this.interactionState.seekIndicatorTimer) {
                clearTimeout(this.interactionState.seekIndicatorTimer);
            }
            
            this.interactionState.seekIndicatorTimer = setTimeout(() => {
                seekIndicator.remove();
                this.interactionState.isShowingSeekIndicator = false;
            }, 1500);
        }
    }

    handleSingleTap() {
        this.interactionState.singleTapTimer = setTimeout(() => {
            if (!this.interactionState.isPotentialTap) return;

            console.log("单击操作执行 (延迟后)");
            const currentVideoElement = this.videoPlayer.getCurrentVideoElement();
            if (currentVideoElement) {
                if (currentVideoElement.paused || currentVideoElement.ended) {
                    currentVideoElement.play();
                } else {
                    currentVideoElement.pause();
                }
            }
            this.interactionState.isPotentialTap = false;
        }, INTERACTION_CONSTANTS.DOUBLE_CLICK_THRESHOLD);
    }
} 