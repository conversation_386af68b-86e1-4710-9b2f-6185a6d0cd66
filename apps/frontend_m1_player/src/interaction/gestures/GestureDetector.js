// src/js/gestures.js

export const swipeThreshold = 50; // 降低阈值，使其更容易触发视频切换

let touchStartX = 0; // 新增：记录X轴起点，用于判断是否为纯粹的垂直滑动
let touchStartY = 0;
let currentTouchY = 0;
let isDragging = false;
let touchStartTime = 0; // 新增：记录触摸开始时间

/**
 * 初始化滑动及点击手势监听器.
 * @param {HTMLElement} targetElement - 需要监听事件的DOM元素.
 * @param {object} callbacks - 包含各种滑动阶段回调的对象
 * @param {function} callbacks.onGestureStart - 手势开始 ({ startX, startY, target, eventTime })
 * @param {function} callbacks.onGestureMove - 手势移动中 ({ offsetX, offsetY, target, eventTime })
 * @param {function} callbacks.onGestureEnd - 手势结束 ({ swipeX, swipeY, duration, target, eventTime })
 */
export function initSwipeGestures(targetElement, callbacks = {}) {
    if (!targetElement) {
        console.error('Gesture Error: Target element not provided.');
        return;
    }

    const { onGestureStart, onGestureMove, onGestureEnd } = callbacks;

    targetElement.addEventListener('touchstart', (event) => {
        
        isDragging = true;
        touchStartX = event.touches[0].clientX;
        touchStartY = event.touches[0].clientY;
        currentTouchY = touchStartY; // 初始化
        touchStartTime = Date.now();

        

        if (typeof onGestureStart === 'function') {
            onGestureStart({
                startX: touchStartX,
                startY: touchStartY,
                target: event.target, // <<< 传递触摸目标
                eventTime: touchStartTime,
                originalEvent: event // <<< 传递原始事件，方便阻止默认行为
            });
        }
    }, { passive: false }); // 改为 false，因为我们可能需要阻止默认的滚动或触摸行为

    targetElement.addEventListener('touchmove', (event) => {
        if (!isDragging) return;

        currentTouchY = event.touches[0].clientY;
        const currentTouchX = event.touches[0].clientX; // 也获取X轴
        const offsetY = currentTouchY - touchStartY;
        const offsetX = currentTouchX - touchStartX;


        if (typeof onGestureMove === 'function') {
            onGestureMove({
                offsetX, // X轴像素偏移
                offsetY, // Y轴像素偏移
                target: event.target, // 目标可能在移动过程中变化，但通常我们关心起始目标
                eventTime: Date.now(),
                originalEvent: event
            });
        }
    }, { passive: false }); // 改为 false

    targetElement.addEventListener('touchend', (event) => {
        if (!isDragging) return;
        isDragging = false;

        const touchEndX = event.changedTouches[0].clientX;
        const touchEndY = event.changedTouches[0].clientY;
        const touchEndTime = Date.now();

        const swipeX = touchStartX - touchEndX; // 水平滑动距离
        const swipeY = touchStartY - touchEndY; // 垂直滑动距离，正值表示手指向上滑动
        const duration = touchEndTime - touchStartTime; // 触摸持续时间

        if (typeof onGestureEnd === 'function') {
            onGestureEnd({
                swipeX,
                swipeY,
                duration,
                target: event.target, // 通常是touchstart时的目标，但touchend有自己的target
                eventTime: touchEndTime,
                originalEvent: event
            });
        }

        // 重置
        touchStartX = 0;
        touchStartY = 0;
        currentTouchY = 0;
        touchStartTime = 0;
    });
} 