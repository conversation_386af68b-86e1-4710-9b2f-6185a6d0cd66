export const API_BASE_URL = 'http://192.168.2.149:3000/video/v1';
export const DEFAULT_REQUEST_TIMEOUT = 10000;

// API 端点常量
export const API_ENDPOINTS = {
    FEED: '/feed',
    VIDEO_LIKE: (videoId) => `/videos/${videoId}/like`,
    VIDEO_COMMENTS: (videoId) => `/videos/${videoId}/comments`,
    VIDEO_COLLECT: (videoId) => `/videos/${videoId}/collect`,
    USER_COLLECTIONS: '/user/collections',
    USER_EVENT: '/user/event'
};

// 请求状态码
export const HTTP_STATUS = {
    OK: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_SERVER_ERROR: 500
};

// 业务状态码
export const BUSINESS_STATUS = {
    SUCCESS: 1,
    FAILURE: 0
}; 