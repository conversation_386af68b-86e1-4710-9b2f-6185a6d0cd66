import { API_BASE_URL, DEFAULT_REQUEST_TIMEOUT, API_ENDPOINTS, HTTP_STATUS, BUSINESS_STATUS } from '../config.js';

/**
 * 通用的 API 请求函数
 * @param {string} endpoint - API 端点
 * @param {object} options - fetch API 的配置对象
 * @returns {Promise<object>} - API 返回的 data 对象
 * @throws {Error} - 如果请求失败或 API 返回错误
 */
async function request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), DEFAULT_REQUEST_TIMEOUT);

    const defaultHeaders = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
    };

    const config = {
        ...options,
        signal: controller.signal,
        headers: {
            ...defaultHeaders,
            ...(options.headers || {}),
        },
    };

    try {
        const response = await fetch(url, config);
        clearTimeout(timeoutId);

        if (!response.ok) {
            const errorData = await response.json().catch(() => null);
            const errorMessage = errorData?.msg || `HTTP error! status: ${response.status}`;
            console.error(`API request failed for ${url}: ${errorMessage}`, errorData);
            throw new Error(errorMessage);
        }

        const result = await response.json();

        if (result.ok === BUSINESS_STATUS.FAILURE) {
            console.error(`API business logic error for ${url}: ${result.msg}`, result.err);
            throw new Error(result.msg || 'API operation failed');
        }

        return result.data;

    } catch (error) {
        if (error.name === 'AbortError') {
            throw new Error('Request timeout');
        }
        console.error(`Network or other error for ${url}:`, error.message);
        throw error;
    }
}

/**
 * 获取视频信息流
 * @param {number} page - 页码
 * @param {number} limit - 每页数量
 * @param {object} [filterParams=null] - 筛选参数
 * @returns {Promise<object>} - 包含 items 和 pgn 的数据对象
 */
export async function getFeed(page = 1, limit = 10, filterParams = null) {
    const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
    });

    if (filterParams) {
        if (filterParams.keywords) {
            params.append('keywords', filterParams.keywords);
        }
        if (filterParams.categoryId) {
            params.append('categoryId', filterParams.categoryId);
        }
    }

    return request(`${API_ENDPOINTS.FEED}?${params.toString()}`);
}

/**
 * 点赞/取消点赞视频
 * @param {string} videoId - 视频ID
 * @param {boolean} likeState - true 表示点赞, false 表示取消点赞
 * @returns {Promise<object>} - 点赞操作后的结果
 */
export async function likeVideo(videoId, likeState) {
    return request(API_ENDPOINTS.VIDEO_LIKE(videoId), {
        method: 'POST',
        body: JSON.stringify({ lk: likeState }),
    });
}

/**
 * 获取视频评论
 * @param {string} videoId - 视频ID
 * @param {number} page - 页码
 * @param {number} limit - 每页数量
 * @returns {Promise<object>} - 评论列表数据
 */
export async function getComments(videoId, page = 1, limit = 10) {
    const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
    });
    return request(`${API_ENDPOINTS.VIDEO_COMMENTS(videoId)}?${params.toString()}`);
}

/**
 * 收藏/取消收藏视频
 * @param {string} videoId - 视频ID
 * @param {boolean} collectState - true 表示收藏, false 表示取消收藏
 * @returns {Promise<object>} - 收藏操作后的结果
 */
export async function collectVideo(videoId, collectState) {
    return request(API_ENDPOINTS.VIDEO_COLLECT(videoId), {
        method: 'POST',
        body: JSON.stringify({ clt: collectState }),
    });
}

/**
 * 获取用户收藏列表
 * @param {number} page - 页码
 * @param {number} limit - 每页数量
 * @returns {Promise<object>} - 收藏列表数据
 */
export async function getMyCollections(page = 1, limit = 10) {
    const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
    });
    return request(`${API_ENDPOINTS.USER_COLLECTIONS}?${params.toString()}`);
}

/**
 * 发送用户事件
 * @param {string} eventType - 事件类型
 * @param {object} eventData - 事件数据
 * @returns {Promise<object>} - 事件发送结果
 */
export async function postUserEvent(eventType, eventData) {
    return request(API_ENDPOINTS.USER_EVENT, {
        method: 'POST',
        body: JSON.stringify({
            type: eventType,
            data: eventData
        }),
    });
} 