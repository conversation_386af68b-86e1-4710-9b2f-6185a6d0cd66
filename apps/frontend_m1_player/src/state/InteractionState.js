// 交互状态常量
export const INTERACTION_CONSTANTS = {
    TAP_MAX_DURATION: 250, // ms，单击的最大持续时间
    TAP_MAX_MOVEMENT: 10,  // px，单击的最大移动距离
    LONG_PRESS_DURATION: 500, // ms，长按的最小持续时间
    PROGRESS_BAR_DRAG_VERTICAL_THRESHOLD: 30, // px，拖动进度条时，允许的最大垂直偏移
    DOUBLE_CLICK_THRESHOLD: 300 // ms，双击判定阈值
};

// 创建交互状态对象
export const createInteractionState = () => ({
    isDraggingProgressBar: false,
    isPotentialTap: false,
    isVerticalSwiping: false,
    dragStartTime: 0,
    dragStartX: 0,
    dragStartY: 0,
    longPressTimer: null,
    singleTapTimer: null,
    lastTapTime: 0,
    seekIndicatorTimer: null,
    isShowingSeekIndicator: false,
    initialVideoOffsetForDrag: 0,
    progressBarRect: null,
    currentScrubTime: null,
    wasPlayingBeforeScrub: undefined,
    isLongPress: false,
    activeMenuVideoItem: null,
    isInFullscreen: false
});

// 清除交互状态
export const clearInteractionState = (state) => {
    if (state.longPressTimer) {
        clearTimeout(state.longPressTimer);
        state.longPressTimer = null;
    }
    if (state.singleTapTimer) {
        clearTimeout(state.singleTapTimer);
        state.singleTapTimer = null;
    }
    if (state.seekIndicatorTimer) {
        clearTimeout(state.seekIndicatorTimer);
        state.seekIndicatorTimer = null;
    }
    
    state.isDraggingProgressBar = false;
    state.isPotentialTap = false;
    state.isVerticalSwiping = false;
    state.isLongPress = false;
    state.activeMenuVideoItem = null;
    state.isShowingSeekIndicator = false;
    state.currentScrubTime = null;
    state.wasPlayingBeforeScrub = undefined;
    state.progressBarRect = null;
    state.isInFullscreen = false;
};

// 重置拖拽相关状态
export const resetDragState = (state) => {
    state.dragStartTime = 0;
    state.dragStartX = 0;
    state.dragStartY = 0;
    state.initialVideoOffsetForDrag = 0;
}; 