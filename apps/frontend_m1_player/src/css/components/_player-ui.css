/* ===== 主要视频播放器UI (浮层与控件) ===== */

/* --- 播放器控制浮层 (播放/暂停按钮) --- */
.video-player-controls-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 20;
    pointer-events: none;
}

.play-pause-button {
    background: rgba(0, 0, 0, 0.5);
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 24px;
    color: white;
    pointer-events: auto;
    transition: background-color 0.2s;
}

.play-pause-button:hover {
    background: rgba(0, 0, 0, 0.7);
}

.play-pause-button.hidden { /* 这个类在 utils/_helpers.css 中定义为 !important，这里是特定于播放按钮的，可保留 */
    /* display: none;  -- 如果 utils/_helpers.css 定义了 .hidden, 这里可以不写 */
    /* 或者为了组件独立性，在这里定义，但不加 !important */
}


/* --- 底部控制条 (进度条, 时间显示) --- */
.bottom-controls-bar {
    pointer-events: auto;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 0 0 8px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.progress-bar-container {
    position: relative;
    left: auto;
    transform: none;
    width: 100%;
    max-width: 10000px; /* 这个值可能需要斟酌 */
    display: flex;
    align-items: center;
    gap: 4px;
}

.progress-bar {
    flex: 1;
    height: 0.6vh;
    background: rgba(239, 236, 236, 0.3);
    border-radius: 2px;
    cursor: pointer;
    position: relative;
}

.progress-bar::before { /* 进度条的可点击区域 */
    content: '';
    position: absolute;
    top: -8px;
    bottom: -8px;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0);
}

.progress-fill {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background: #FF004E;
    border-radius: 2px;
    width: 0;
    transition: width 0.1s linear;
}

.time-display {
    color: white;
    font-size: 0.85em;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.7);
    min-width: 45px;
    text-align: center;
    flex-shrink: 0;
    visibility: visible; /* 强制一直可见 */
}

/* --- 视频信息浮层 (用户名, 描述) --- */
.video-info-overlay {
    position: absolute;
    bottom: 40px;
    left: 3vw;
    right: 3vw;
    color: #fff;
    z-index: 10;
    pointer-events: none;
}

.video-info-overlay * {
    pointer-events: auto;
}

.video-info {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.video-info .username {
    font-weight: bold;
    font-size: 2.5vh;
    margin-bottom: 5px;
}

.video-info .description {
    font-size: 2vh;
    opacity: 0.9;
}

/* --- 侧边操作按钮 (点赞, 收藏等) --- */
/* 注意：.control-btn 的通用部分在 _video-item.css 中，这里是特定化 */
.side-action-buttons {
    pointer-events: auto;
    position: absolute;
    bottom: 18vh; 
    right: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 21;
}

.side-action-buttons .control-btn { /* 此容器中控制按钮的特定样式 */
    /* 继承自 _video-item.css 中的 .control-btn */
    font-size: 3.5vh;
    padding: clamp(8px, 1.2vmin, 14px);
    margin-bottom: 0px; /* 从通用的 .control-btn 重置 */
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5); /* 与通用 .control-btn 比较，这里的 alpha 值更高 */
    line-height: 1;
}

.side-action-buttons .control-btn:last-child {
    margin-bottom: 0;
}

.action-like-btn.liked,
.action-collect-btn.collected {
    color: #FF004E;
}

.action-container { /* 操作按钮及其计数的容器 */
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0;
    margin-bottom: 1.8vh;
}

.action-container:last-child {
    margin-bottom: 0;
}

.action-count {
    color: white;
    font-size: clamp(10px, 1.8vmin, 14px);
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    margin-top: -2px;
    line-height: 1;
} 