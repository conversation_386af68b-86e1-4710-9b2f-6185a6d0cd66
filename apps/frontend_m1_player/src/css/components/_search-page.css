/* ===== 搜索页面专属样式 ===== */
#page-search {
    /* display: none; (由 layout/_pages.css .page 类处理) */
    flex-direction: column; /* 如果 #page-search.active 总是 flex */
    height: 100vh;
    background: #000; /* 搜索页面的明确背景 */
}

#page-search.active { /* 如果需要 display:flex, 则覆盖 .page.active 的 display:block */
    display: flex;
}

/* 搜索页面头部 */
.search-header {
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(0, 0, 0, 0.8);
    position: sticky;
    top: 0;
    z-index: 100;
}

/* 返回按钮 (统一风格) */
.search-back-button,
.search-feed-back-button {
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-back-button svg,
.search-feed-back-button svg {
    width: 24px;
    height: 24px;
}

/* 搜索结果视频流页面的返回按钮特定定位 */
.search-feed-back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 100;
}

/* 搜索输入区域 */
.search-input-container {
    flex: 1;
    display: flex;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 8px 12px;
}

.search-input {
    flex: 1;
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    outline: none;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.search-button {
    background: transparent;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-button svg {
    width: 20px;
    height: 20px;
}

/* 搜索结果区域 */
.search-results {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.search-results-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding-bottom: 20px;
}

/* 单个搜索结果卡片 */
.search-result-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s;
}

.search-result-card:hover {
    transform: scale(1.02);
}

.card-image {
    position: relative;
    padding-top: 133.33%;
    overflow: hidden;
}

.card-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-title {
    padding: 12px;
    color: white;
    font-size: 14px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 搜索结果指示器 */
.loading-more {
    grid-column: 1 / -1;
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.6);
}

.no-results {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.6);
} 