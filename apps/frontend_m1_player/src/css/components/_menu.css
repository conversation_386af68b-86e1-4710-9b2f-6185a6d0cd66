/* ===== 长按菜单 ===== */
.menu-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: flex-end;
    z-index: 30;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    pointer-events: auto;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.menu-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.menu-panel {
    background-color: #2c2c2e;
    color: white;
    width: 100%;
    max-width: 2000px; /* 这个值可能需要斟酌 */
    border-top-left-radius: 5vw;
    border-top-right-radius: 5vw;
    padding: 10px 0;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.2);
    transform: translateY(100%);
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.menu-overlay.visible .menu-panel {
    transform: translateY(0);
}

.menu-option {
    padding: 15px 20px;
    font-size: 16px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    transition: background-color 0.2s ease, opacity 0.2s ease;
}

.menu-option:last-child {
    border-bottom: none;
}

.menu-panel.interactive .menu-option {
    cursor: pointer;
    opacity: 1;
    pointer-events: auto;
}

.menu-panel.interactive .menu-option:hover {
    background-color: rgba(255, 255, 255, 0.08);
}

.menu-panel:not(.interactive) .menu-option {
    cursor: default;
    pointer-events: none;
    opacity: 0.7;
}

.menu-option-playback-rate-buttons {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 10px 0;
}

.playback-rate-btn {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 6px 12px;
    font-size: 13px;
    margin: 0 5px;
    cursor: pointer;
    transition: background-color 0.2s ease, border-color 0.2s ease;
}

.playback-rate-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.playback-rate-btn.active {
    background-color: #FF004E;
    border-color: #FF004E;
    color: white;
}

.menu-option-report {
    color: #ff4d4f;
}

.menu-option-report:hover {
    background-color: rgba(255, 77, 79, 0.1);
}

.menu-label {
    color: #999;
    cursor: default;
    pointer-events: none;
} 