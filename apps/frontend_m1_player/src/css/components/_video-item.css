/* ===== 视频项目与内容 ===== */
.video-item {
    width: 100vw; /* vw = 视口宽度 */
    height: 100vh; /* vh = 视口高度 */
    position: relative; /* 每个项目在变换的容器内绝对定位 */
    display: flex; /* 用于视频居中（如果需要）或浮层布局 */
    justify-content: center;
    align-items: center;
    overflow: hidden; /* 隐藏视频中不符合 object-fit 的部分 */
    background-color: #111; /* 视频加载失败时的后备背景色 */
}

.video-item video {
    width: 100%;
    height: 100%;
    object-fit: contain; /* 确保视频适应项目大小，不裁剪 */
    display: block; /* 移除内联元素下方的额外空间 */
}

/* ===== 旧版/兼容性浮层与控件 (为兼容性保留，但标记为旧版) ===== */
/* 注意：这些样式如果确实不再使用，可以考虑移除或注释掉，以减小CSS体积 */
.video-overlay {
    position: absolute;
    bottom: 60px;
    left: 15px;
    right: 70px;
    color: #fff;
    z-index: 10;
    pointer-events: none;
}

.video-overlay * {
    pointer-events: auto;
}

.video-overlay .username {
    font-weight: bold;
    font-size: 1.1em;
    margin-bottom: 5px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.video-overlay .description {
    font-size: 0.9em;
    margin-bottom: 10px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.video-controls { /* 旧的侧边控件容器 */
    position: absolute;
    bottom: 0px;
    right: -55px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* .control-btn 的通用部分可以考虑移到 _player-ui.css 或 _buttons.css (如果按钮多) */
/* 或者，如果它只被旧的 .video-controls 和新的 .side-action-buttons 使用，
   可以考虑在各自组件文件中定义或继承。
   为保持原样，暂时放在这里，但标记一下。
*/
.control-btn {
    background: transparent;
    border: none;
    color: #fff;
    font-size: 28px;
    margin-bottom: 0px;
    cursor: pointer;
    padding: 5px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* 隐藏看起来是旧的/未使用的侧边按钮容器 */
.video-actions {
    display: none;
}

