/* ===== 全屏模式样式 ===== */

/* --- 通用全屏 (通过 :fullscreen 伪类) & H5 全屏 (通过 body 类) --- */
body.h5-in-fullscreen #video-feed-container,
#video-feed-container:fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background-color: black;
    z-index: 2147483647; /* CSS z-index 最大值 */
    overflow: hidden;
}

body.h5-in-fullscreen .video-item,
#video-feed-container:fullscreen .video-item {
    width: 100% !important;
    height: 100% !important;
}

body.h5-in-fullscreen .video-item video,
#video-feed-container:fullscreen .video-item video {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain;
}

/* H5/通用全屏模式下的控件和浮层 */
body.h5-in-fullscreen .video-player-controls-overlay,
#video-feed-container:fullscreen .video-player-controls-overlay {
    /* 这些样式已在 _player-ui.css 中定义，这里主要是确保 z-index 和覆盖 */
    z-index: 100; /* 确保控件在视频之上，但在菜单之下 */
}

body.h5-in-fullscreen .bottom-controls-bar,
#video-feed-container:fullscreen .bottom-controls-bar {
    /* 这些样式已在 _player-ui.css 中定义，这里主要是确保 z-index 和覆盖 */
    /* 如果要修改位置或尺寸，在这里进行 */
    position: absolute;
    bottom: 5px;
    left: 10px;
    right: 10px;
    width: auto;
    z-index: 100;
}

body.h5-in-fullscreen .progress-bar-container,
#video-feed-container:fullscreen .progress-bar-container {
    width: 95%;
    max-width: 800px;
    margin: 0 auto;
}

body.h5-in-fullscreen .play-pause-button,
#video-feed-container:fullscreen .play-pause-button {
    z-index: 100;
}

/* H5/通用全屏模式下隐藏元素 */
body.h5-in-fullscreen .side-action-buttons,
#video-feed-container:fullscreen .side-action-buttons {
    display: none !important;
}

body.h5-in-fullscreen .video-info-overlay,
#video-feed-container:fullscreen .video-info-overlay {
    display: none !important;
}

/* H5/通用全屏模式下的菜单 */
body.h5-in-fullscreen .menu-overlay.visible .menu-panel,
#video-feed-container:fullscreen .menu-overlay.visible .menu-panel {
    z-index: 200; /* 确保菜单在控件之上 */
}


/* --- React Native (RN) 全屏模式样式 (通过 .rn-fullscreen 类) --- */
/* 注意：这些选择器应该作用于 #video-feed-container 或其父级，或者 .video-item */
/* 例如： .rn-fullscreen #video-feed-container .video-item 或 #video-feed-container.rn-fullscreen .video-item */
/* 为保持原样，我先按你给的写，但你可能需要调整选择器使其正确生效 */
.rn-fullscreen .video-item { /* 如果 .rn-fullscreen 加在 #video-feed-container 上，则为 .rn-fullscreen .video-item */
    width: 100% !important;
    height: 100% !important;
}

.rn-fullscreen .video-item video {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain;
}

.rn-fullscreen .bottom-controls-bar {
    position: absolute;
    bottom: 5px;
    left: 10px;
    right: 10px;
    width: auto;
    z-index: 100;
}

.rn-fullscreen .progress-bar-container {
    width: 95%;
    max-width: 800px;
    margin: 0 auto;
}

.rn-fullscreen .play-pause-button {
    z-index: 100;
}

.rn-fullscreen .side-action-buttons,
.rn-fullscreen .video-info-overlay {
    display: none !important;
}

.rn-fullscreen .menu-overlay.visible .menu-panel {
    z-index: 200;
}

/* 在所有全屏模式下隐藏搜索图标和返回按钮 */
/* 这个部分比较复杂，依赖于JS如何添加全屏类名，以及HTML结构 */
/* 你可以考虑用一个统一的 JS 添加的类名来控制，例如 .is-fullscreen-active */
body.h5-in-fullscreen .search-icon-container,
body.h5-in-fullscreen .search-feed-back-button,
#video-feed-container:fullscreen .search-icon-container,
#video-feed-container:fullscreen .search-feed-back-button, /* 为 :fullscreen 添加返回按钮隐藏 */
.rn-fullscreen .search-icon-container,
.rn-fullscreen .search-feed-back-button,
.rn-fullscreen .search-icon, /* 如果图标是子元素, 则特定于RN结构 */
[class*="rn-fullscreen"] .search-feed-back-button,
[class*="rn-fullscreen"] .search-icon-container,
[class*="rn-fullscreen"] .search-icon,
[data-fullscreen="true"] .search-icon-container,
[data-fullscreen="true"] .search-feed-back-button, /* 为 data-fullscreen 添加返回按钮隐藏 */
[data-fullscreen="true"] .search-icon,
[class*="fullscreen"] .search-icon-container,
[class*="fullscreen"] .search-icon,
[class*="Fullscreen"] .search-icon-container,
[class*="Fullscreen"] .search-icon,
[class*="fullScreen"] .search-icon-container,
[class*="fullScreen"] .search-icon,
[class*="full-screen"] .search-icon-container,
[class*="full-screen"] .search-icon,
[class*="fullscreen"] .search-feed-back-button,
[class*="Fullscreen"] .search-feed-back-button,
[class*="fullScreen"] .search-feed-back-button,
[class*="full-screen"] .search-feed-back-button {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    position: absolute !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    transform: scale(0) !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    clip: rect(0, 0, 0, 0) !important;
}

/* 额外的RN全屏横屏规则 */
/* 这个规则比较宽泛，如果意图是只在全屏横屏时隐藏，
   并且JS会给 #video-feed-container 或 body 添加如 .rn-fullscreen-landscape 这样的类，
   那么选择器应该更精确。
   如果意图就是在任何横屏模式下（无论是否全屏）都隐藏搜索图标和返回按钮，则此规则可能是正确的，但要小心其影响范围。
   保留，但建议审查其确切意图和影响。
*/
@media screen and (orientation: landscape) {
    /* body.rn-mode .search-icon-container, */ /* 更精确的选择器示例 */
    /* body.rn-mode .search-feed-back-button { */
    /* 如果这个规则只针对 RN 的横屏，并且你有一个表示 RN 模式的类，例如 body.rn-mode */
    /* 或者，如果只针对特定元素的横屏，例如 #video-feed-container.rn-fullscreen-landscape */
    
    /* 为了保持原样，先按你给的。但强烈建议审查这部分。 */
    /* 它会隐藏所有横屏下的搜索图标和返回按钮。 */
    .search-icon-container,
    .search-feed-back-button {
        display: none !important;
    }
} 