#!/bin/bash

# RealMaster 开发环境启动脚本 - 高性能视频静态文件服务
# 基于实际goupload配置启动nginx静态文件服务

echo "🚀 启动RealMaster高性能开发环境..."

# 检查nginx是否安装
if ! command -v nginx &> /dev/null; then
    echo "❌ nginx未安装，请先安装nginx"
    echo "Ubuntu/Debian: sudo apt install nginx"
    echo "macOS: brew install nginx"
    exit 1
fi

# 检查nginx模块支持
echo "🔍 检查nginx模块支持..."
nginx_modules=$(nginx -V 2>&1)

# 检查mp4模块
if echo "$nginx_modules" | grep -q "http_mp4_module"; then
    echo "✅ MP4模块已启用 - 支持视频快速seek"
else
    echo "⚠️  MP4模块未启用 - 视频播放性能可能受限"
    echo "   建议重新编译nginx或安装包含mp4模块的版本"
fi

# 检查gzip模块
if echo "$nginx_modules" | grep -q "http_gzip_module"; then
    echo "✅ Gzip模块已启用 - 支持内容压缩"
else
    echo "⚠️  Gzip模块未启用"
fi

# 检查realip模块（生产环境有用）
if echo "$nginx_modules" | grep -q "http_realip_module"; then
    echo "✅ RealIP模块已启用"
else
    echo "ℹ️  RealIP模块未启用（开发环境可忽略）"
fi

# 检查配置文件
if [ ! -f "nginx.conf" ]; then
    echo "❌ 找不到nginx.conf配置文件"
    exit 1
fi

# 创建goupload存储目录（基于config.toml配置）
echo "📁 检查并创建存储目录..."

# 草稿阶段目录
sudo mkdir -p /var/www/draft/rm_video_drafts
sudo mkdir -p /var/www/draft/rm_thumbnail_drafts

# 最终媒体目录
sudo mkdir -p /var/www/media/rm_videos
sudo mkdir -p /var/www/media/rm_thumbnails
sudo mkdir -p /var/www/media/rm_avatars

# nginx临时目录（避免权限问题）
mkdir -p /tmp/nginx_client_temp
mkdir -p /tmp/nginx_proxy_temp
mkdir -p /tmp/nginx_fastcgi_temp
mkdir -p /tmp/nginx_uwsgi_temp
mkdir -p /tmp/nginx_scgi_temp

# 设置权限（开发环境）
sudo chmod -R 755 /var/www/draft/
sudo chmod -R 755 /var/www/media/
chmod -R 755 /tmp/nginx_*

echo "✅ 存储目录创建完成"

# 系统优化检查
echo "⚙️  系统优化检查..."

# 检查文件描述符限制
ulimit_files=$(ulimit -n)
if [ "$ulimit_files" -lt 4096 ]; then
    echo "⚠️  文件描述符限制较低: $ulimit_files (建议 >= 4096)"
    echo "   可运行: ulimit -n 4096"
else
    echo "✅ 文件描述符限制: $ulimit_files"
fi

# 检查TCP缓冲区设置（Linux）
if [ -f /proc/sys/net/core/rmem_max ]; then
    rmem_max=$(cat /proc/sys/net/core/rmem_max)
    if [ "$rmem_max" -lt 16777216 ]; then  # 16MB
        echo "⚠️  TCP接收缓冲区较小: $rmem_max (建议 >= 16MB)"
    else
        echo "✅ TCP接收缓冲区: $rmem_max"
    fi
fi

# 停止可能运行的nginx进程
echo "🛑 停止现有nginx进程..."
sudo pkill -f "nginx.*nginx.conf" 2>/dev/null || true

# 测试配置文件
echo "🧪 测试nginx配置..."
if ! sudo nginx -c "$(pwd)/nginx.conf" -t; then
    echo "❌ nginx配置文件有错误，请检查"
    exit 1
fi

# 启动nginx
echo "🌐 启动高性能nginx (端口3000)..."
sudo nginx -c "$(pwd)/nginx.conf" -g "daemon off;" &

NGINX_PID=$!

echo "✅ 高性能Nginx已启动!"
echo ""
echo "🚀 性能优化特性:"
echo "   ✅ 自动检测CPU核心数 (worker_processes auto)"
echo "   ✅ 高并发连接 (4096 connections)"
echo "   ✅ 异步I/O + 直接磁盘I/O (大文件优化)"
echo "   ✅ MP4快速seek支持 (如果模块可用)"
echo "   ✅ HLS流优化 (m3u8/ts文件)"
echo "   ✅ 智能缓存策略 (图片30天，视频1天)"
echo "   ✅ Gzip压缩 (m3u8等文本文件)"
echo ""
echo "📂 静态文件服务路径:"
echo "   草稿视频: http://localhost:3000/draft/videos/"
echo "   草稿缩略图: http://localhost:3000/draft/thumbnails/"
echo "   最终视频: http://localhost:3000/media/videos/"
echo "   最终缩略图: http://localhost:3000/media/thumbnails/"
echo "   客户头像: http://localhost:3000/media/avatars/"
echo "   健康检查: http://localhost:3000/health"
echo ""
echo "🔧 配置文件: $(pwd)/nginx.conf"
echo "🗂️  存储目录:"
echo "   /var/www/draft/ (草稿文件)"
echo "   /var/www/media/ (最终文件)"
echo ""
echo "💡 使用说明:"
echo "   - Go后端API: http://localhost:8080"
echo "   - 前端通过nginx访问媒体文件"
echo "   - 支持Range请求（视频拖拽播放）"
echo "   - 自动CORS支持"
echo "   - 按 Ctrl+C 停止服务"
echo ""
echo "📊 性能监控:"
echo "   - 访问日志已关闭（提升性能）"
echo "   - 可通过 /health 端点检查服务状态"
echo ""

# 等待中断信号
trap "echo '🛑 停止nginx...'; sudo kill $NGINX_PID 2>/dev/null; exit 0" INT

wait $NGINX_PID
